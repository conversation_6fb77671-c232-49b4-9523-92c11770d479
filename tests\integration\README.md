# SerialT 集成测试

## 🎯 概述

本目录包含 SerialT 应用的集成测试工具和脚本，用于验证应用在真实使用场景下的功能和稳定性。

## 🔧 环境配置

### 虚拟串口设置
- **SerialT 应用端口**: COM30
- **测试工具端口**: COM31
- **虚拟串口对**: COM30 ↔ COM31

### 依赖安装
```bash
pip install pyserial
```

## 📁 目录结构

```
tests/integration/
├── README.md                    # 本文件
├── config.py                    # 测试配置文件
├── scripts/
│   ├── serial_simulator.py     # 串口模拟器
│   ├── test_virtual_ports.py   # 虚拟串口测试
│   └── test_scenarios.py       # 测试场景脚本（待创建）
├── data/                        # 测试数据文件
└── results/                     # 测试结果输出
```

## 🚀 快速开始

### ✅ 环境确认
虚拟串口配置：**COM30 ↔ COM31** (SerialT使用COM30，测试工具使用COM31)

### 1. 验证环境
```bash
cd tests/integration
python scripts/detect_virtual_ports.py
```

### 2. 启动SerialT并连接
1. 启动SerialT应用
2. 连接到 **COM30**，波特率 **115200**

### 3. 运行测试
```bash
# 基础文本测试
python scripts/serial_simulator.py --port COM31 --test basic_text --duration 10

# VT100控制序列测试
python scripts/serial_simulator.py --port COM31 --test vt100 --duration 10

# 大数据传输测试
python scripts/serial_simulator.py --port COM31 --test large --duration 15

# 交互式测试
python scripts/interactive_tester.py --port COM31
```

## 📋 测试类型

### 1. 基础通信测试
- 简单文本收发
- 中文字符支持
- 特殊字符处理
- 不同波特率兼容性

### 2. VT100 渲染测试
- 光标控制序列
- 颜色和文本属性
- 屏幕清除和控制
- 复杂布局渲染

### 3. 大数据量测试
- 连续大文本传输
- 高频小数据包
- 混合数据类型
- 内存使用监控

### 4. 用户场景测试
- 完整使用流程
- 设置变更测试
- 搜索功能验证
- 日志记录测试

### 5. 稳定性测试
- 多实例并发
- 长时间运行
- 异常恢复
- 资源泄漏检测

## 🛠️ 使用串口模拟器

### 基本用法
```bash
python scripts/serial_simulator.py --port COM31 --baudrate 115200
```

### 参数说明
- `--port`: 串口名称（默认 COM31）
- `--baudrate`: 波特率（默认 115200）
- `--test`: 测试类型（text/vt100/large）

### 交互模式
运行模拟器后，它会：
1. 连接到指定串口
2. 发送测试数据到 SerialT
3. 接收并显示 SerialT 的回应
4. 按 Ctrl+C 退出

## 📊 测试配置

### 串口配置
在 `config.py` 中定义：
```python
SERIAL_CONFIG = {
    'SERIALT_PORT': 'COM30',      # SerialT 端口
    'TEST_TOOL_PORT': 'COM31',    # 测试工具端口
    'DEFAULT_BAUDRATE': 115200,   # 默认波特率
    # ... 其他配置
}
```

### 测试参数
```python
TEST_CONFIG = {
    'LARGE_DATA_SIZE_KB': 1024,   # 大数据测试大小
    'HIGH_FREQ_PACKET_COUNT': 1000, # 高频测试包数量
    # ... 其他参数
}
```

## 🔍 故障排除

### 常见问题

1. **串口连接失败**
   - 确认虚拟串口软件已安装并运行
   - 检查 COM30 和 COM31 是否正确配对
   - 确认没有其他程序占用端口

2. **数据传输异常**
   - 检查波特率设置是否一致
   - 确认串口参数（数据位、停止位、校验位）匹配
   - 查看防火墙或安全软件是否阻止

3. **VT100 渲染问题**
   - 确认 SerialT 支持相应的 VT100 序列
   - 检查字符编码设置
   - 验证终端字体支持

### 调试技巧

1. **启用详细日志**
   ```bash
   python scripts/serial_simulator.py --port COM31 --verbose
   ```

2. **使用串口监控工具**
   - 可以使用第三方串口监控软件查看数据流
   - 确认数据是否正确发送和接收

3. **分步测试**
   - 先运行虚拟串口测试
   - 再进行简单文本测试
   - 最后进行复杂场景测试

## 📈 测试报告

测试结果将保存在 `results/` 目录中：
- 测试日志文件
- 性能统计数据
- 错误报告和堆栈跟踪
- 内存使用图表

## 🔄 持续集成

这些测试可以集成到 CI/CD 流程中：
1. 自动化虚拟串口配置
2. 运行基础集成测试
3. 生成测试报告
4. 性能回归检测

## 📞 支持

如果遇到问题：
1. 检查本 README 的故障排除部分
2. 查看测试日志文件
3. 确认环境配置正确
4. 提交 issue 并附上详细的错误信息
