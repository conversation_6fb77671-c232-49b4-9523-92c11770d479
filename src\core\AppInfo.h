/**
 * @file AppInfo.h
 * @brief 定义了应用级别的元数据，如版本号、组织名称等。
 *
 * @note 这些常量是全局静态的，并且在整个应用程序中不应改变。
 */
#ifndef APPINFO_H
#define APPINFO_H

#include <string_view>

namespace App::Info {
using namespace std::string_view_literals;
inline constexpr std::string_view kAppName = "SerialT"sv;
inline constexpr std::string_view kAppVersion = "1.4.1"sv;
inline constexpr std::string_view kOrgName = "LSDT"sv;
} // namespace App::Info

#endif // APPINFO_H
