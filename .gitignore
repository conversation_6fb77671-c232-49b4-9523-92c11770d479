# This file is used to ignore files which are generated
# ----------------------------------------------------------------------------

*~
*.autosave
*.a
*.core
*.moc
*.o
*.obj
*.orig
*.rej
*.so
*.so.*
*_pch.h.cpp
*_resource.rc
*.qm
.#*
*.*#
core
!core/
tags
.DS_Store
.directory
*.debug
Makefile*
*.prl
*.app
moc_*.cpp
ui_*.h
qrc_*.cpp
Thumbs.db
*.res
/.qmake.cache
/.qmake.stash

# qtcreator generated files
*.pro.user*
*.qbs.user*
CMakeLists.txt.user*

# xemacs temporary files
*.flc

# Vim temporary files
.*.swp

# Visual Studio generated files
*.ib_pdb_index
*.idb
*.ilk
*.pdb
*.sln
*.suo
*.vcproj
*vcproj.*.*.user
*.ncb
*.sdf
*.opensdf
*.vcxproj
*vcxproj.*

# MinGW generated files
*.Debug
*.Release

# Python byte code
*.pyc

# Binaries
# --------
*.dll
*.exe

# Directories with generated files
.moc/
.obj/
.pch/
.rcc/
.uic/
/build*/
resources/app.rc

# vscode
.vscode/

# clang-tidy
.cache/

# test files
auto_files/
.cursorrules
todo.md
.cursor/rules/rules.mdc

# 但不忽略Qt翻译文件
!translations/qt_zh_CN.qm
!translations/qtbase_zh_CN.qm

# memorybank
memory-bank/

# kiro
.kiro/