#include <QCoreApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QSignalSpy>
#include <QThread>
#include <QTimer>
#include <QtTest/QtTest>

#include "../../src/SerialProcessor.h"

class TestFrequentConnection : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 频繁连接断开测试
    void testFrequentConnectDisconnect();
    void testRapidConnectDisconnect();
    void testConnectionStateConsistency();
    void testSignalEmissionConsistency();
    void testResourceCleanupAfterFrequentOperations();
    void testConcurrentConnectionAttempts();
    void testConnectionAfterMultipleFailures();
    void testPortSwitchingStressTest();

private:
    SerialProcessor *m_processor;
    QStringList m_testPorts;

    // 辅助方法
    void performConnectDisconnectCycle(qsizetype cycles, unsigned long delayMs = 10);
    bool waitForConnectionState(bool expectedState, int timeoutMs = 1000);
    void setupTestPorts();
};

void TestFrequentConnection::initTestCase()
{
    qDebug() << "Starting frequent connection tests";
    setupTestPorts();
}

void TestFrequentConnection::cleanupTestCase()
{
    qDebug() << "Frequent connection tests completed";
}

void TestFrequentConnection::init()
{
    m_processor = new SerialProcessor(this);
}

void TestFrequentConnection::cleanup()
{
    if (m_processor)
    {
        if (m_processor->isOpen())
        {
            m_processor->closePort();
        }
        delete m_processor;
        m_processor = nullptr;
    }
}

void TestFrequentConnection::setupTestPorts()
{
    // 设置测试用的端口列表（包括不存在的端口用于测试失败情况）
    m_testPorts << "COM999" << "COM998" << "COM997" << "/dev/ttyUSB999" << "/dev/ttyS999";
}

bool TestFrequentConnection::waitForConnectionState(bool expectedState, int timeoutMs)
{
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < timeoutMs)
    {
        if (m_processor->isOpen() == expectedState)
        {
            return true;
        }
        QCoreApplication::processEvents();
        QThread::msleep(1);
    }

    return m_processor->isOpen() == expectedState;
}

void TestFrequentConnection::performConnectDisconnectCycle(qsizetype cycles, unsigned long delayMs)
{
    QString testPort = m_testPorts.first();

    for (qsizetype i = 0; i < cycles; ++i)
    {
        // 尝试连接
        bool connectResult = m_processor->openPort(testPort, 9600);
        Q_UNUSED(connectResult) // 在测试环境中可能失败，这是正常的

        if (delayMs > 0)
        {
            QThread::msleep(delayMs);
        }

        // 断开连接
        m_processor->closePort();

        if (delayMs > 0)
        {
            QThread::msleep(delayMs);
        }

        // 处理事件循环
        QCoreApplication::processEvents();
    }
}

void TestFrequentConnection::testFrequentConnectDisconnect()
{
    // 测试频繁的连接和断开操作
    qDebug() << "Testing frequent connect/disconnect cycles";

    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 执行100次连接断开循环
    const int cycles = 100;
    QElapsedTimer timer;
    timer.start();

    performConnectDisconnectCycle(cycles, 5); // 5ms延迟

    qint64 elapsed = timer.elapsed();
    qDebug() << QString("Completed %1 cycles in %2ms (avg: %3ms per cycle)")
                    .arg(cycles)
                    .arg(elapsed)
                    .arg(double(elapsed) / cycles);

    // 验证最终状态
    QVERIFY(!m_processor->isOpen());

    // 验证没有崩溃或异常
    QVERIFY(true);
}

void TestFrequentConnection::testRapidConnectDisconnect()
{
    // 测试快速连接断开（无延迟）
    qDebug() << "Testing rapid connect/disconnect without delays";

    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 执行快速循环
    const int cycles = 50;
    performConnectDisconnectCycle(cycles, 0); // 无延迟

    // 验证最终状态
    QVERIFY(!m_processor->isOpen());

    // 验证对象仍然有效
    QVERIFY(m_processor->parser() != nullptr);
}

void TestFrequentConnection::testConnectionStateConsistency()
{
    // 测试连接状态的一致性
    qDebug() << "Testing connection state consistency";

    QString testPort = m_testPorts.first();

    for (int i = 0; i < 20; ++i)
    {
        // 记录连接前状态
        bool stateBefore = m_processor->isOpen();
        QVERIFY(!stateBefore); // 应该是断开状态

        // 尝试连接
        bool connectResult = m_processor->openPort(testPort, 9600);

        // 检查状态一致性
        bool stateAfterConnect = m_processor->isOpen();
        QCOMPARE(stateAfterConnect, connectResult);

        // 断开连接
        m_processor->closePort();

        // 检查断开后状态
        bool stateAfterDisconnect = m_processor->isOpen();
        QVERIFY(!stateAfterDisconnect);

        QCoreApplication::processEvents();
    }
}

void TestFrequentConnection::testSignalEmissionConsistency()
{
    // 测试信号发射的一致性
    qDebug() << "Testing signal emission consistency";

    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);
    QString testPort = m_testPorts.first();

    int successfulConnections = 0;
    const int attempts = 30;

    for (int i = 0; i < attempts; ++i)
    {
        qsizetype signalCountBefore = connectionSpy.count();

        // 尝试连接
        bool connectResult = m_processor->openPort(testPort, 9600);
        if (connectResult)
        {
            successfulConnections++;
            // 成功连接应该发射信号
            QVERIFY(connectionSpy.count() > signalCountBefore);
        }

        qsizetype signalCountAfterConnect = connectionSpy.count();

        // 断开连接
        m_processor->closePort();

        // 如果之前连接成功，断开时应该发射信号
        if (connectResult)
        {
            QVERIFY(connectionSpy.count() > signalCountAfterConnect);
        }

        QCoreApplication::processEvents();
    }

    qDebug() << QString("Successful connections: %1/%2").arg(successfulConnections).arg(attempts);
}

void TestFrequentConnection::testResourceCleanupAfterFrequentOperations()
{
    // 测试频繁操作后的资源清理
    qDebug() << "Testing resource cleanup after frequent operations";

    // 执行大量操作
    performConnectDisconnectCycle(200, 1);

    // 验证对象状态
    QVERIFY(m_processor != nullptr);
    QVERIFY(!m_processor->isOpen());
    QVERIFY(m_processor->parser() != nullptr);

    // 测试对象仍然可以正常工作
    QString testPort = m_testPorts.first();
    bool result = m_processor->openPort(testPort, 9600);
    Q_UNUSED(result)
    m_processor->closePort();

    QVERIFY(!m_processor->isOpen());
}

void TestFrequentConnection::testConcurrentConnectionAttempts()
{
    // 测试并发连接尝试的处理
    qDebug() << "Testing concurrent connection attempts";

    QString testPort = m_testPorts.first();

    // 快速连续的连接尝试
    for (int i = 0; i < 10; ++i)
    {
        m_processor->openPort(testPort, 9600);
        m_processor->openPort(testPort, 115200); // 不同参数
        m_processor->closePort();
        QCoreApplication::processEvents();
    }

    // 验证最终状态
    QVERIFY(!m_processor->isOpen());
}

void TestFrequentConnection::testConnectionAfterMultipleFailures()
{
    // 测试多次失败后的连接能力
    qDebug() << "Testing connection capability after multiple failures";

    // 尝试连接多个无效端口
    for (const QString &port : m_testPorts)
    {
        for (int i = 0; i < 5; ++i)
        {
            bool result = m_processor->openPort(port, 9600);
            QVERIFY(!result); // 应该失败
            QVERIFY(!m_processor->isOpen());
            m_processor->closePort(); // 即使失败也调用关闭
        }
    }

    // 验证对象仍然可用
    QVERIFY(m_processor != nullptr);
    QVERIFY(m_processor->parser() != nullptr);

    // 尝试正常操作
    QString testPort = m_testPorts.first();
    m_processor->openPort(testPort, 9600);
    m_processor->closePort();
    QVERIFY(!m_processor->isOpen());
}

void TestFrequentConnection::testPortSwitchingStressTest()
{
    // 测试端口切换的压力测试
    qDebug() << "Testing port switching stress test";

    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 在不同端口间快速切换
    for (int cycle = 0; cycle < 20; ++cycle)
    {
        for (const QString &port : m_testPorts)
        {
            m_processor->openPort(port, 9600);
            QThread::msleep(1);
            m_processor->closePort();
            QCoreApplication::processEvents();
        }
    }

    // 验证最终状态
    QVERIFY(!m_processor->isOpen());
    QVERIFY(m_processor != nullptr);
}

QTEST_MAIN(TestFrequentConnection)
#include "test_frequent_connection.moc"
