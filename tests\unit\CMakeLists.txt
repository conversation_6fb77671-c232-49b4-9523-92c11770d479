# 单元测试模块

# SerialProcessor 测试
add_qt_test(test_serial_processor
    test_serial_processor.cpp
)

# VT100Parser 测试
add_qt_test(test_vt100_parser
    test_vt100_parser.cpp
)

# HighPerformanceTerminal 测试
add_qt_test(test_terminal_rendering
    test_terminal_rendering.cpp
)

# 设置持久化测试
add_qt_test(test_settings_persistence
    test_settings_persistence.cpp
)

# 内存泄漏检测测试
add_qt_test(test_memory_leaks
    test_memory_leaks.cpp
)

# 长时间运行稳定性测试
add_qt_test(test_long_running_stability
    test_long_running_stability.cpp
)

# 频繁连接断开测试
add_qt_test(test_frequent_connection
    test_frequent_connection.cpp
)

# 异常处理测试
add_qt_test(test_exception_handling
    test_exception_handling.cpp
)

# 串口断开处理测试
add_qt_test(test_disconnection_handling
    test_disconnection_handling.cpp
)

# 缓冲区溢出保护测试
add_qt_test(test_buffer_overflow_protection
    test_buffer_overflow_protection.cpp
)

# 无效VT100序列容错测试
add_qt_test(test_invalid_vt100_sequences
    test_invalid_vt100_sequences.cpp
)

# 数据格式化测试
add_qt_test(test_data_formatter
    test_data_formatter.cpp
)

# VT100 命令禁用测试
add_qt_test(test_vt100_command_disable
    test_vt100_command_disable.cpp
)

# 自动启用日志测试
add_qt_test(test_auto_enable_logging
    test_auto_enable_logging.cpp
)

# Logger时间戳修复测试
add_qt_test(test_logger_timestamp_fix
    test_logger_timestamp_fix.cpp
)

# 高速数据丢包测试
add_qt_test(test_high_speed_data_loss
    test_high_speed_data_loss.cpp
)

# 真实串口使用场景测试
add_qt_test(test_realistic_serial_usage
    test_realistic_serial_usage.cpp
)

# 智能连接监控测试
add_qt_test(test_intelligent_connection_monitoring
    test_intelligent_connection_monitoring.cpp
)

# 自动重连功能测试
add_qt_test(test_auto_reconnect
    test_auto_reconnect.cpp
)

# 性能测试
add_qt_test(test_reconnect_performance
    ../performance/test_reconnect_performance.cpp
)

# CommandParser 测试
add_qt_test(test_command_parser
    test_command_parser.cpp
)
