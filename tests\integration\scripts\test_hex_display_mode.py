#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SerialT 十六进制显示模式集成测试

测试十六进制显示功能的正确性，包括：
1. ASCII模式显示
2. 十六进制模式显示  
3. 混合模式显示
4. 模式切换功能

使用方法：
1. 启动SerialT应用程序
2. 连接到COM30端口
3. 运行此脚本，它会通过COM31发送测试数据
4. 在SerialT中手动切换显示模式，观察显示效果
"""

import serial
import time
import sys
import os

# 添加配置文件路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config import SERIAL_CONFIG

def send_test_data(port_name, test_data, description):
    """发送测试数据到串口"""
    try:
        with serial.Serial(port_name, SERIAL_CONFIG['DEFAULT_BAUDRATE'], timeout=1) as ser:
            print(f"\n=== {description} ===")
            print(f"发送数据: {test_data}")
            print(f"十六进制: {' '.join(f'{b:02X}' for b in test_data)}")
            
            ser.write(test_data)
            time.sleep(0.5)  # 等待数据发送完成
            
            print("数据已发送，请在SerialT中观察显示效果")
            return True
            
    except serial.SerialException as e:
        print(f"串口错误: {e}")
        return False
    except Exception as e:
        print(f"发送数据时发生错误: {e}")
        return False

def main():
    """主测试函数"""
    port_name = SERIAL_CONFIG['TEST_TOOL_PORT']
    
    print("SerialT 十六进制显示模式集成测试")
    print("=" * 50)
    print(f"测试端口: {port_name}")
    print(f"波特率: {SERIAL_CONFIG['DEFAULT_BAUDRATE']}")
    print("\n请确保：")
    print("1. SerialT应用程序已启动")
    print("2. SerialT已连接到COM30端口")
    print("3. 虚拟串口对COM30-COM31已正确配置")
    
    input("\n按回车键开始测试...")
    
    # 测试数据集
    test_cases = [
        {
            'data': b'Hello World!',
            'description': '基础ASCII文本测试'
        },
        {
            'data': b'Test\r\nLine2\r\nLine3',
            'description': '多行文本测试'
        },
        {
            'data': bytes([0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x57, 0x6F, 0x72, 0x6C, 0x64, 0x21]),
            'description': '纯十六进制数据测试'
        },
        {
            'data': bytes(range(32, 127)),  # 可打印ASCII字符
            'description': '可打印ASCII字符集测试'
        },
        {
            'data': bytes([0x00, 0x01, 0x02, 0x1B, 0x7F, 0xFF]),
            'description': '不可打印字符测试'
        },
        {
            'data': b'\x1B[31mRed Text\x1B[0m',
            'description': 'VT100控制序列测试'
        },
        {
            'data': b'Mixed: ABC\x00\x01\x02DEF\xFF',
            'description': '混合字符测试'
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}/{total_count}] {test_case['description']}")
        
        if send_test_data(port_name, test_case['data'], test_case['description']):
            success_count += 1
            
            # 给用户时间观察和切换显示模式
            print("\n请在SerialT中测试以下操作：")
            print("1. 查看ASCII模式显示效果")
            print("2. 切换到十六进制模式 (视图 -> 数据显示模式 -> 十六进制模式)")
            print("3. 切换到混合模式 (视图 -> 数据显示模式 -> 混合模式)")
            print("4. 切换回ASCII模式")
            
            if i < total_count:
                input("按回车键继续下一个测试...")
        else:
            print("测试失败，跳过...")
    
    # 测试总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"总测试用例: {total_count}")
    print(f"成功发送: {success_count}")
    print(f"失败: {total_count - success_count}")
    
    if success_count == total_count:
        print("\n✅ 所有测试数据发送成功！")
        print("\n请验证以下功能：")
        print("1. ASCII模式：显示可读文本，不可打印字符显示为点号")
        print("2. 十六进制模式：显示字节的十六进制表示，用空格分隔")
        print("3. 混合模式：同时显示十六进制和ASCII，按行组织")
        print("4. 模式切换：能够实时切换显示模式，数据正确更新")
        print("5. VT100序列：在ASCII模式下正确解析，在HEX/混合模式下显示原始数据")
    else:
        print(f"\n❌ 有 {total_count - success_count} 个测试失败")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
