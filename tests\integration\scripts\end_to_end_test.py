#!/usr/bin/env python3
"""
用户场景端到端测试
模拟完整的用户使用场景，验证SerialT的整体稳定性和用户体验
"""

import sys
import time
import serial
import threading
import random
import string
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from config import SERIAL_CONFIG, VT100_SEQUENCES

class EndToEndTester:
    """端到端测试器"""
    
    def __init__(self, port, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        self.running = False
        self.receive_thread = None
        self.received_data = []
        
    def connect(self):
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1.0
            )
            print(f"✅ 已连接到串口 {self.port}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.stop_receiving()
        if self.serial and self.serial.is_open:
            self.serial.close()
            print("🔌 已断开串口连接")
    
    def start_receiving(self):
        """开始接收数据"""
        self.running = True
        self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
        self.receive_thread.start()
        print("📡 开始监听SerialT的回应数据")
    
    def stop_receiving(self):
        """停止接收数据"""
        self.running = False
        if self.receive_thread:
            self.receive_thread.join(timeout=2)
    
    def _receive_loop(self):
        """接收数据循环"""
        while self.running and self.serial and self.serial.is_open:
            try:
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data:
                        self.received_data.append(data)
                        print(f"📥 收到SerialT回应: {len(data)} 字节")
                time.sleep(0.01)
            except Exception as e:
                print(f"❌ 接收数据错误: {e}")
                break
    
    def send_data(self, data, description):
        """发送数据"""
        if not self.serial or not self.serial.is_open:
            print("❌ 串口未连接")
            return False
        
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            self.serial.write(data)
            self.serial.flush()
            print(f"📤 {description}")
            return True
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def wait_and_observe(self, seconds, description):
        """等待并观察"""
        print(f"⏳ {description} (等待 {seconds} 秒)")
        time.sleep(seconds)
    
    def scenario_startup_connection(self):
        """场景1: 启动和连接测试"""
        print("\n🚀 场景1: 启动和连接测试")
        print("=" * 40)
        
        print("📋 测试步骤:")
        print("1. 确认SerialT应用已启动")
        print("2. 确认SerialT已连接到COM30")
        print("3. 测试基础连接通信")
        
        # 发送连接测试数据
        test_data = "Connection test - SerialT startup verification\n"
        if self.send_data(test_data, "发送连接测试数据"):
            self.wait_and_observe(2, "观察SerialT是否正确显示测试数据")
            return True
        return False
    
    def scenario_basic_communication(self):
        """场景2: 基础通信测试"""
        print("\n💬 场景2: 基础通信测试")
        print("=" * 40)
        
        print("📋 测试各种类型的数据传输:")
        
        # 测试数据类型
        test_cases = [
            ("Hello SerialT! Basic text communication.\n", "发送基础英文文本"),
            ("Numbers: 1234567890\n", "发送数字字符"),
            ("Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?\n", "发送特殊字符"),
            ("Mixed: ABC123!@# test\n", "发送混合字符"),
            ("Long line: " + "A" * 100 + "\n", "发送长行文本"),
        ]
        
        success_count = 0
        for data, description in test_cases:
            if self.send_data(data, description):
                success_count += 1
                self.wait_and_observe(1, "等待SerialT处理数据")
            else:
                print(f"❌ {description} 失败")
        
        print(f"📊 基础通信测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
    
    def scenario_vt100_features(self):
        """场景3: VT100功能使用测试"""
        print("\n🎨 场景3: VT100功能使用测试")
        print("=" * 40)
        
        print("📋 测试用户常用的VT100功能:")
        
        # 清屏和重新开始
        self.send_data(VT100_SEQUENCES['CLEAR_SCREEN'], "清屏命令")
        self.send_data(VT100_SEQUENCES['CURSOR_HOME'], "光标归位")
        self.wait_and_observe(1, "观察清屏效果")
        
        # 彩色输出测试
        self.send_data("=== VT100 Feature Demo ===\n", "发送标题")
        
        color_tests = [
            (VT100_SEQUENCES['FG_RED'] + "Error message" + VT100_SEQUENCES['RESET'] + "\n", "红色错误信息"),
            (VT100_SEQUENCES['FG_GREEN'] + "Success message" + VT100_SEQUENCES['RESET'] + "\n", "绿色成功信息"),
            (VT100_SEQUENCES['FG_YELLOW'] + "Warning message" + VT100_SEQUENCES['RESET'] + "\n", "黄色警告信息"),
            (VT100_SEQUENCES['BOLD'] + "Important notice" + VT100_SEQUENCES['RESET'] + "\n", "粗体重要信息"),
        ]
        
        for sequence, description in color_tests:
            self.send_data(sequence, description)
            self.wait_and_observe(0.5, "观察颜色效果")
        
        # 光标移动测试
        self.send_data("\nCursor movement test: ", "光标移动测试开始")
        self.send_data("A", "输入字符A")
        self.wait_and_observe(0.5, "观察字符A")
        
        self.send_data(VT100_SEQUENCES['CURSOR_LEFT'], "光标左移")
        self.send_data("B", "在A前插入B")
        self.wait_and_observe(0.5, "观察插入效果")
        
        self.send_data(VT100_SEQUENCES['CURSOR_RIGHT'], "光标右移")
        self.send_data("C\n", "在A后插入C")
        self.wait_and_observe(1, "观察最终结果应为BAC")
        
        return True
    
    def scenario_data_volume_test(self):
        """场景4: 数据量处理测试"""
        print("\n📦 场景4: 数据量处理测试")
        print("=" * 40)
        
        print("📋 测试SerialT处理不同数据量的能力:")
        
        # 中等数据量测试
        print("🔸 发送中等数据量 (1KB)")
        medium_data = "Medium data test: " + "X" * 1000 + "\n"
        self.send_data(medium_data, "发送1KB数据")
        self.wait_and_observe(2, "观察SerialT处理中等数据的表现")
        
        # 分块发送测试
        print("🔸 分块发送测试")
        for i in range(10):
            chunk = f"Chunk {i+1:02d}: " + "D" * 50 + "\n"
            self.send_data(chunk, f"发送数据块 {i+1}/10")
            time.sleep(0.1)  # 短暂间隔
        
        self.wait_and_observe(2, "观察分块数据的显示效果")
        
        # 快速连续发送测试
        print("🔸 快速连续发送测试")
        for i in range(20):
            quick_msg = f"Quick msg {i+1:02d}\n"
            self.send_data(quick_msg, f"快速发送消息 {i+1}/20")
            time.sleep(0.05)  # 很短的间隔
        
        self.wait_and_observe(3, "观察快速数据流的处理")
        
        return True
    
    def scenario_interactive_features(self):
        """场景5: 交互功能测试"""
        print("\n🎮 场景5: 交互功能测试")
        print("=" * 40)
        
        print("📋 测试SerialT的交互功能:")
        print("⚠️ 请在SerialT中手动测试以下功能:")
        
        # 发送提示信息
        self.send_data("\n=== Interactive Feature Test ===\n", "发送交互测试标题")
        self.send_data("Please test the following features in SerialT:\n", "发送测试提示")
        
        features_to_test = [
            "1. Scroll up and down to view history",
            "2. Select and copy some text",
            "3. Use search function (Ctrl+F) to find 'test'",
            "4. Try auto-wrap toggle if available",
            "5. Check status bar information",
            "6. Test window resizing",
        ]
        
        for feature in features_to_test:
            self.send_data(feature + "\n", f"发送测试项目: {feature}")
            time.sleep(0.5)
        
        self.send_data("\nInteractive test data ready!\n", "交互测试数据准备完成")
        
        # 等待用户手动测试
        print("\n⏸️ 暂停30秒，请在SerialT中手动测试上述功能")
        print("   测试期间请观察:")
        print("   - 滚动是否流畅")
        print("   - 文本选择是否正常")
        print("   - 搜索功能是否工作")
        print("   - 界面响应是否及时")
        
        for i in range(30, 0, -5):
            print(f"   剩余时间: {i} 秒...")
            time.sleep(5)
        
        return True
    
    def scenario_stress_test(self):
        """场景6: 压力测试"""
        print("\n⚡ 场景6: 压力测试")
        print("=" * 40)
        
        print("📋 对SerialT进行压力测试:")
        
        # 混合数据类型压力测试
        print("🔸 混合数据类型压力测试")
        
        for round_num in range(3):
            print(f"   第 {round_num + 1} 轮压力测试")
            
            # 发送各种类型的混合数据
            for i in range(50):
                data_type = random.choice(['text', 'vt100', 'numbers', 'special'])
                
                if data_type == 'text':
                    data = f"Text data {i}: " + ''.join(random.choices(string.ascii_letters, k=20)) + "\n"
                elif data_type == 'vt100':
                    color = random.choice(['FG_RED', 'FG_GREEN', 'FG_BLUE', 'FG_YELLOW'])
                    data = VT100_SEQUENCES[color] + f"Colored text {i}" + VT100_SEQUENCES['RESET'] + "\n"
                elif data_type == 'numbers':
                    data = f"Numbers {i}: " + ''.join(random.choices(string.digits, k=15)) + "\n"
                else:  # special
                    data = f"Special {i}: " + ''.join(random.choices('!@#$%^&*()', k=10)) + "\n"
                
                self.send_data(data, f"压力测试数据 {i+1}/50")
                time.sleep(0.02)  # 很短的间隔
            
            self.wait_and_observe(3, f"第 {round_num + 1} 轮测试完成，观察SerialT状态")
        
        print("📊 压力测试完成，请检查SerialT是否:")
        print("   - 界面仍然响应正常")
        print("   - 内存使用没有异常增长")
        print("   - 滚动和显示正常")
        print("   - 没有出现错误或崩溃")
        
        return True
    
    def scenario_graceful_shutdown(self):
        """场景7: 优雅退出测试"""
        print("\n👋 场景7: 优雅退出测试")
        print("=" * 40)
        
        print("📋 测试应用的正常退出流程:")
        
        # 发送结束信息
        self.send_data("\n" + "="*50 + "\n", "发送分隔线")
        self.send_data("End-to-End Test Completed Successfully!\n", "发送测试完成信息")
        self.send_data("You can now safely close SerialT.\n", "发送安全退出提示")
        self.send_data("Thank you for testing!\n", "发送感谢信息")
        self.send_data("="*50 + "\n", "发送结束分隔线")
        
        self.wait_and_observe(3, "等待最后的数据显示")
        
        print("\n📋 请手动测试SerialT的退出流程:")
        print("1. 在SerialT中断开串口连接")
        print("2. 正常关闭SerialT应用")
        print("3. 观察是否有任何错误或异常")
        print("4. 确认程序完全退出，没有残留进程")
        
        return True
    
    def run_all_scenarios(self):
        """运行所有用户场景测试"""
        print("🧪 SerialT 用户场景端到端测试")
        print("=" * 60)
        print("本测试将模拟完整的用户使用流程")
        print("请确保SerialT已启动并连接到COM30")
        print()
        
        if not self.connect():
            return False
        
        self.start_receiving()
        
        try:
            scenarios = [
                ("启动和连接", self.scenario_startup_connection),
                ("基础通信", self.scenario_basic_communication),
                ("VT100功能", self.scenario_vt100_features),
                ("数据量处理", self.scenario_data_volume_test),
                ("交互功能", self.scenario_interactive_features),
                ("压力测试", self.scenario_stress_test),
                ("优雅退出", self.scenario_graceful_shutdown),
            ]
            
            results = []
            for scenario_name, scenario_func in scenarios:
                print(f"\n🔄 开始执行场景: {scenario_name}")
                try:
                    result = scenario_func()
                    results.append((scenario_name, result))
                    if result:
                        print(f"✅ 场景 '{scenario_name}' 执行完成")
                    else:
                        print(f"❌ 场景 '{scenario_name}' 执行失败")
                except Exception as e:
                    print(f"❌ 场景 '{scenario_name}' 执行异常: {e}")
                    results.append((scenario_name, False))
                
                # 场景间暂停
                if scenario_name != "优雅退出":  # 最后一个场景不需要暂停
                    print(f"⏸️ 场景间暂停 3 秒...")
                    time.sleep(3)
            
            # 生成测试报告
            self.generate_test_report(results)
            
            return all(result for _, result in results)
            
        finally:
            self.disconnect()
    
    def generate_test_report(self, results):
        """生成测试报告"""
        print(f"\n📊 端到端测试报告")
        print("=" * 50)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"总场景数: {total}")
        print(f"通过场景: {passed}")
        print(f"失败场景: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        print(f"\n详细结果:")
        for scenario_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {scenario_name}: {status}")
        
        print(f"\n接收数据统计:")
        print(f"  收到数据包: {len(self.received_data)} 个")
        total_bytes = sum(len(data) for data in self.received_data)
        print(f"  总接收字节: {total_bytes} 字节")
        
        if passed == total:
            print(f"\n🎉 所有用户场景测试通过！")
            print("SerialT的整体功能和稳定性表现优秀")
        else:
            print(f"\n⚠️ 部分场景测试失败，需要进一步检查")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SerialT 用户场景端到端测试')
    parser.add_argument('--port', default=SERIAL_CONFIG['TEST_TOOL_PORT'], help='串口名称')
    parser.add_argument('--baudrate', type=int, default=SERIAL_CONFIG['DEFAULT_BAUDRATE'], help='波特率')
    
    args = parser.parse_args()
    
    print("🎯 SerialT 用户场景端到端测试")
    print("=" * 60)
    print(f"测试端口: {args.port}")
    print(f"波特率: {args.baudrate}")
    print()
    print("⚠️ 请确保:")
    print("1. SerialT应用已启动")
    print("2. SerialT已连接到COM30")
    print("3. 波特率设置为115200")
    print("4. 准备观察和手动测试某些功能")
    print()
    
    input("准备就绪后按回车键开始测试...")
    
    tester = EndToEndTester(args.port, args.baudrate)
    
    try:
        success = tester.run_all_scenarios()
        return success
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
