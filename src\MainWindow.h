#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include "HighPerformanceTerminal.h"
#include "InstanceManager.h"
#include "SettingsDialog.h"
#include "ThemeManager.h"

#include <QCheckBox>
#include <QLineEdit>
#include <QMainWindow>
#include <QMap>
#include <QMenu>
#include <QPropertyAnimation>
#include <QSystemTrayIcon>

// 前向声明
class SerialProcessor;
class FindWidget;
class QComboBox;
class QAction;
class QToolBar;
class QMenu;
class QLabel;
class Logger;
class QTimer;
class QCloseEvent;

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow() override;

    MainWindow(const MainWindow &) = delete;
    auto operator=(const MainWindow &) -> MainWindow & = delete;
    MainWindow(MainWindow &&) = delete;
    auto operator=(MainWindow &&) -> MainWindow & = delete;

protected:
    void closeEvent(QCloseEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    auto nativeEvent(const QByteArray &eventType, void *message, qintptr *result) -> bool override;
    auto eventFilter(QObject *obj, QEvent *event) -> bool override;

private slots:
    // 搜索
    void performSearch(const QString &term, HighPerformanceTerminal::FindFlags flags);

    // 串口操作
    void openSerialPort();
    void closeSerialPort();
    void toggleConnection(); // 切换连接状态
    void refreshPorts();
    void onPortChanged(const QString &name);
    void onBaudRateChanged(const QString &baudRate);

    // 菜单操作
    void showSettingsDialog();
    void clearTerminalView();
    void toggleLogging(bool checked);
    void toggleWordWrap(bool checked);
    void onDisplayModeChanged();
    void onThemeChanged();
    void showAboutDialog();
    void onExitActionTriggered();

    // 快捷命令
    void onQuickCommandTriggered(bool checked);
    void onQuickCommandTimerTimeout();
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void updateUiForConnectionState(bool connected);
    void onOpenLogFileClicked();
    void onOpenLogDirectoryClicked();
    void onPinActionToggled(bool checked);
    void showQuickCommandsSettings();

    // 错误处理
    void handleSerialError(const QString &error);
    void handleConnectionLost();
    void handleConnectionHealthChanged(bool healthy);

    // 自动重连状态处理
    void onConnectionStateChanged(ConnectionState state, int currentAttempt, int maxAttempts);

    // 状态栏统计信息更新
    void updateStatusBarStatistics();

    // 快捷键管理
    static void updateShortcutsForConnectionState(bool connected);
    void enableGlobalShortcuts(bool enabled);

    // 终端事件
    void onScreenCleared();
    void updateFindWidgetPosition();

    // 自定义标题相关
    void onCustomTitleCheckBoxToggled(bool checked);
    void onCustomTitleTextChanged(const QString &text);
    void showCustomTitleInput();
    void hideCustomTitleInput();

private:
    // 内部常量
    static constexpr int kIconSize = 16;
    static constexpr int kMinimumWidth = 800;
    static constexpr int kSearchDebounceTimeMs = 200;
    static constexpr int kAboutIconSize = 64;
    static constexpr int kTrayIconMessageDurationMs = 3000;
    static constexpr int kStatusMessageDurationMs = 3000;
    static constexpr int kErrorMessageDurationMs = 3000;
    static constexpr int kFindWidgetOffset = 5;
    static constexpr int kCustomTitleAnimDurationMs = 200;
    static constexpr int kCustomTitleInputWidth = 200;
    static constexpr int kHideTimerDelayMs = 100;
    static constexpr int kConnectionDurationUpdateThreshold = 3600;
    static constexpr int kTimeValueBase = 10;
    static constexpr size_t kTimeBufferSize = 256;
    static constexpr int kCustomTitleLayoutMargin = 5;
    static constexpr int kCustomTitleLayoutSpacing = 5;

    void updateTrayIconVisibility();
    void setupUi();
    void createActions();
    void createToolBar();
    void createMenus();
    void updatePortList();
    void updateQuickCommandsMenu();
    void createTrayMenu();

    void loadApplicationSettings();
    void saveApplicationSettings();
    void applyApplicationSettings();

    // 辅助方法
    static auto formatBytes(qint64 bytes) -> QString;
    void updateWindowTitleWithCustomTitle();
    void setupCustomTitleWidget();

    // 状态栏消息管理
    void showTemporaryStatusMessage(const QString &message, int durationMs = kStatusMessageDurationMs);
    void restoreNormalStatusMessage();

    [[nodiscard]] auto formatLogFileName(const QString &format) const -> QString;
    AppSettings m_appSettings;
    HighPerformanceTerminal *m_terminal;
    SerialProcessor *m_serialProcessor;
    Logger *m_logger;

    QToolBar *m_toolBar;
    QComboBox *m_portComboBox;
    QComboBox *m_baudRateComboBox;
    QAction *m_connectionToggleAction; // 合并连接/断开为切换按钮
    QAction *m_refreshAction;
    QAction *m_findAction;
    QAction *m_toggleLoggingAction;
    QAction *m_openLogFileAction;
    QAction *m_openLogDirAction;
    QAction *m_pinAction;
    QAction *m_wordWrapAction;
    QActionGroup *m_displayModeActionGroup;
    QAction *m_asciiModeAction;
    QAction *m_hexModeAction;
    QAction *m_mixedModeAction;
    QAction *m_lightThemeAction;
    QAction *m_darkThemeAction;
    QAction *m_autoThemeAction;
    QMenu *m_quickCommandsMenu;
    QMap<QAction *, QTimer *> m_quickCommandTimers;

    // 搜索框
    FindWidget *m_findWidget;
    QTimer *m_searchDebounceTimer;

    // 系统托盘
    QSystemTrayIcon *m_trayIcon;
    bool m_trayMessageShown = false;

    // 系统托盘菜单
    QMenu *m_trayMenu;
    QAction *m_trayShowAction;
    QAction *m_trayExitAction;

    bool m_isQuittingFromAction = false; // 用于区分退出操作和关闭窗口的标志

    InstanceManager m_instanceManager;
    ThemeManager *m_themeManager;

    bool m_pauseOnResizeEnabled = false;

    // 状态栏统计信息显示
    QLabel *m_statusLabel;
    QLabel *m_statisticsLabel;
    QTimer *m_statusMessageTimer;  // 临时消息定时器
    QString m_normalStatusMessage; // 正常状态消息

    // 自定义标题控件
    QWidget *m_customTitleWidget;
    QCheckBox *m_customTitleCheckBox;
    QLineEdit *m_customTitleLineEdit;
    QPropertyAnimation *m_titleAnimation;
    QString m_customTitle;
    QTimer *m_hideTimer; // 延迟隐藏定时器

    // 快捷键管理
    QList<QAction *> m_vt100ConflictActions; // 与VT100冲突的快捷键
    QList<QAction *> m_safeActions;          // 安全的快捷键
};

#endif // MAINWINDOW_H
