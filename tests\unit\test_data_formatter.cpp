#include <QByteArray>
#include <QString>
#include <QtTest>

#include "../../src/DataFormatter.h"

class TestDataFormatter : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();

    // DataFormatter 测试
    void testFormatAsASCII();
    void testFormatAsHex();
    void testFormatAsMixed();
    void testFormatData();
    void testIsPrintableChar();
    void testByteToHex();
    void testEmptyData();
    void testSpecialCharacters();
    void testLargeData();

private:
    QByteArray createTestData();
    QByteArray createBinaryData();
};

void TestDataFormatter::initTestCase()
{
    // 测试初始化
}

void TestDataFormatter::cleanupTestCase()
{
    // 测试清理
}

void TestDataFormatter::testFormatAsASCII()
{
    // 测试ASCII格式化
    QByteArray testData = "Hello\nWorld\t!";
    QString result = DataFormatter::formatAsASCII(testData);

    QCOMPARE(result, QString("Hello\nWorld\t!"));

    // 测试不可打印字符
    QByteArray binaryData;
    binaryData.append(char(0x01)); // 不可打印字符
    binaryData.append('A');
    binaryData.append(char(0xFF)); // 不可打印字符

    QString binaryResult = DataFormatter::formatAsASCII(binaryData);
    QCOMPARE(binaryResult, QString(".A."));
}

void TestDataFormatter::testFormatAsHex()
{
    // 测试十六进制格式化
    QByteArray testData;
    testData.append(char(0x48)); // 'H'
    testData.append(char(0x65)); // 'e'
    testData.append(char(0x6C)); // 'l'
    testData.append(char(0x6C)); // 'l'
    testData.append(char(0x6F)); // 'o'

    QString result = DataFormatter::formatAsHex(testData);
    QCOMPARE(result, QString("48 65 6C 6C 6F"));

    // 测试空数据
    QByteArray emptyData;
    QString emptyResult = DataFormatter::formatAsHex(emptyData);
    QCOMPARE(emptyResult, QString(""));

    // 测试单字节
    QByteArray singleByte;
    singleByte.append(char(0xAB));
    QString singleResult = DataFormatter::formatAsHex(singleByte);
    QCOMPARE(singleResult, QString("AB"));
}

void TestDataFormatter::testFormatAsMixed()
{
    // 测试混合格式化
    QByteArray testData = "Hello World!";
    QString result = DataFormatter::formatAsMixed(testData);

    // 验证结果包含十六进制和ASCII部分
    QVERIFY(result.contains("48 65 6C 6C 6F 20 57 6F 72 6C 64 21")); // 十六进制部分
    QVERIFY(result.contains("Hello World!"));                        // ASCII部分
    QVERIFY(result.contains("|"));                                   // 分隔符
}

void TestDataFormatter::testFormatData()
{
    // 测试通用格式化方法
    QByteArray testData = "Test";

    QString asciiResult = DataFormatter::formatData(testData, DisplayMode::ASCII);
    QString hexResult = DataFormatter::formatData(testData, DisplayMode::HEX);
    QString mixedResult = DataFormatter::formatData(testData, DisplayMode::MIXED);

    QCOMPARE(asciiResult, DataFormatter::formatAsASCII(testData));
    QCOMPARE(hexResult, DataFormatter::formatAsHex(testData));
    QCOMPARE(mixedResult, DataFormatter::formatAsMixed(testData));
}

void TestDataFormatter::testIsPrintableChar()
{
    // 测试可打印字符判断
    QVERIFY(DataFormatter::isPrintableChar(' ')); // 空格 (32)
    QVERIFY(DataFormatter::isPrintableChar('A')); // 字母
    QVERIFY(DataFormatter::isPrintableChar('z')); // 字母
    QVERIFY(DataFormatter::isPrintableChar('0')); // 数字
    QVERIFY(DataFormatter::isPrintableChar('~')); // 波浪号 (126)

    QVERIFY(!DataFormatter::isPrintableChar('\0'));      // NULL
    QVERIFY(!DataFormatter::isPrintableChar('\n'));      // 换行符
    QVERIFY(!DataFormatter::isPrintableChar('\t'));      // 制表符
    QVERIFY(!DataFormatter::isPrintableChar(char(31)));  // 控制字符
    QVERIFY(!DataFormatter::isPrintableChar(char(127))); // DEL
    QVERIFY(!DataFormatter::isPrintableChar(char(255))); // 扩展ASCII
}

void TestDataFormatter::testByteToHex()
{
    // 测试字节到十六进制转换
    QCOMPARE(DataFormatter::byteToHex(0x00), QString("00"));
    QCOMPARE(DataFormatter::byteToHex(0x0F), QString("0F"));
    QCOMPARE(DataFormatter::byteToHex(0xAB), QString("AB"));
    QCOMPARE(DataFormatter::byteToHex(0xFF), QString("FF"));
    QCOMPARE(DataFormatter::byteToHex(0x48), QString("48")); // 'H'
}

void TestDataFormatter::testEmptyData()
{
    // 测试空数据处理
    QByteArray emptyData;

    QCOMPARE(DataFormatter::formatAsASCII(emptyData), QString(""));
    QCOMPARE(DataFormatter::formatAsHex(emptyData), QString(""));
    QCOMPARE(DataFormatter::formatAsMixed(emptyData), QString(""));
    QCOMPARE(DataFormatter::formatData(emptyData, DisplayMode::ASCII), QString(""));
}

void TestDataFormatter::testSpecialCharacters()
{
    // 测试特殊字符处理
    QByteArray specialData;
    specialData.append('\n');       // 换行符
    specialData.append('\r');       // 回车符
    specialData.append('\t');       // 制表符
    specialData.append(char(0x00)); // NULL
    specialData.append(char(0x1B)); // ESC

    QString asciiResult = DataFormatter::formatAsASCII(specialData);
    QCOMPARE(asciiResult, QString("\n\r\t.."));

    QString hexResult = DataFormatter::formatAsHex(specialData);
    QCOMPARE(hexResult, QString("0A 0D 09 00 1B"));
}

void TestDataFormatter::testLargeData()
{
    // 测试大数据处理
    QByteArray largeData;
    for (int i = 0; i < 100; ++i)
    {
        largeData.append(char(i % 256));
    }

    // 确保格式化不会崩溃或产生异常
    QString asciiResult = DataFormatter::formatAsASCII(largeData);
    QString hexResult = DataFormatter::formatAsHex(largeData);
    QString mixedResult = DataFormatter::formatAsMixed(largeData);

    QVERIFY(!asciiResult.isEmpty());
    QVERIFY(!hexResult.isEmpty());
    QVERIFY(!mixedResult.isEmpty());

    // 验证十六进制结果的长度
    // 100字节应该产生 100*2 + 99 = 299个字符（每个字节2个字符 + 99个空格）
    QCOMPARE(hexResult.length(), 299);
}

QByteArray TestDataFormatter::createTestData()
{
    return QByteArray("Hello World! 123");
}

QByteArray TestDataFormatter::createBinaryData()
{
    QByteArray data;
    for (int i = 0; i < 256; ++i)
    {
        data.append(char(i));
    }
    return data;
}

QTEST_MAIN(TestDataFormatter)
#include "test_data_formatter.moc"
