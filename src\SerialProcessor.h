#ifndef SERIALPROCESSOR_H
#define SERIALPROCESSOR_H

#include "core/AppTypes.h" // 包含共享的枚举类型
#include "core/ReconnectConstants.h"
#include "core/SerialConstants.h"

#include <QDateTime>
#include <QObject>
#include <QSerialPort>
#include <chrono>

class VT100Parser;
class QTimer;

/**
 * @brief 定义监控间隔的范围（毫秒）。
 * @note 使用此结构体避免了 `bugprone-easily-swappable-parameters` 告警。
 */
struct MonitorIntervalRange
{
    int minMs;
    int maxMs;
};

class SerialProcessor : public QObject {
    Q_OBJECT

public:
    explicit SerialProcessor(QObject *parent = nullptr);
    ~SerialProcessor() override;

    SerialProcessor(const SerialProcessor &) = delete;
    auto operator=(const SerialProcessor &) -> SerialProcessor & = delete;
    SerialProcessor(SerialProcessor &&) = delete;
    auto operator=(SerialProcessor &&) -> SerialProcessor & = delete;

    auto openPort(const QString &portName, int baudRate, QSerialPort::DataBits dataBits = QSerialPort::Data8,
                  QSerialPort::Parity parity = QSerialPort::NoParity,
                  QSerialPort::StopBits stopBits = QSerialPort::OneStop) -> bool;
    void closePort();
    [[nodiscard]] auto isOpen() const -> bool;
    [[nodiscard]] auto portName() const -> QString;
    [[nodiscard]] auto getLastError() const -> QString;

    // 连接状态检查
    [[nodiscard]] auto isConnectionHealthy() const -> bool;
    void startConnectionMonitoring();
    void stopConnectionMonitoring();

    // 智能连接监控
    [[nodiscard]] auto getConnectionQuality() const -> double;             // 获取连接质量评分
    [[nodiscard]] auto getCurrentMonitorInterval() const -> int;           // 获取当前监控间隔
    void setMonitorIntervalRange(const MonitorIntervalRange &range);       // 设置监控间隔范围
    [[nodiscard]] auto getConnectionQualityDescription() const -> QString; // 获取连接质量描述

    // 数据统计功能
    [[nodiscard]] auto getBytesReceived() const -> qint64;
    [[nodiscard]] auto getBytesSent() const -> qint64;
    [[nodiscard]] auto getReceiveRate() const -> double; // 字节/秒
    [[nodiscard]] auto getSendRate() const -> double;    // 字节/秒
    [[nodiscard]] auto getConnectionTime() const -> QDateTime;
    [[nodiscard]] auto getConnectionDuration() const -> qint64; // 连接时长（秒）
    void resetStatistics();

    [[nodiscard]] auto parser() const -> VT100Parser *;

    // 自动重连功能
    [[nodiscard]] auto connectionState() const -> ConnectionState;
    [[nodiscard]] auto isReconnecting() const -> bool;
    void setAutoReconnectEnabled(bool enabled);
    void setMaxReconnectAttempts(int attempts);
    void setReconnectInterval(int intervalMs);
    void setReconnectIntervalMultiplier(double multiplier);
    void cancelReconnection();

signals:
    void dataForLogging(const QByteArray &data);
    void rawDataReceived(const QByteArray &data); // 新增：原始数据信号
    void connectionStatusChanged(bool connected);
    void errorOccurred(const QString &error);
    void connectionLost();
    void connectionHealthChanged(bool healthy);
    void statisticsUpdated(); // 数据统计更新信号

    // 智能监控信号
    void connectionQualityChanged(double quality);              // 连接质量变化
    void monitorIntervalChanged(int interval);                  // 监控间隔变化
    void dataStallDetected(int stallTimeMs);                    // 数据停滞检测
    void connectionAnomalyDetected(const QString &description); // 连接异常检测

    // 自动重连信号
    void connectionStateChanged(ConnectionState state, int currentAttempt = 0, int maxAttempts = 0);

public slots:
    void writeData(const QByteArray &data);
    void writeDataRaw(const QByteArray &data);
    void sendBreak();
    void setEchoEnabled(bool enabled);
    void setNewLineMode(NewLineMode mode);
    void setDisplayMode(DisplayMode mode);

private slots:
    void handleReadyRead();
    void handleError(QSerialPort::SerialPortError error);
    void checkConnectionHealth();
    void updateStatistics(); // 更新统计信息

private:
    // 智能监控私有方法
    void updateConnectionQuality();                             // 更新连接质量评估
    void adjustMonitorInterval();                               // 自适应调整监控间隔
    void detectDataStall();                                     // 检测数据停滞
    void detectConnectionAnomalies();                           // 检测连接异常
    void addHealthCheckResult(bool healthy);                    // 添加健康检查结果到历史
    [[nodiscard]] auto calculateQualityScore() const -> double; // 计算质量评分

    // 自动重连私有方法
    void startReconnecting();
    void attemptReconnection();
    void onReconnectionSuccess();
    void onReconnectionFailed();
    void backupConnectionParameters();
    void calculateNextReconnectInterval();
    void setState(ConnectionState newState);

    // 重连专用的端口打开方法
    auto reconnectPort() -> bool;

    // 信号连接管理辅助类
    class SignalConnectionGuard;
    friend class SignalConnectionGuard;

    // 类内常量，符合开发规范
    static constexpr NewLineMode kDefaultNewLineMode = NewLineMode::Cr;
    static constexpr int kBytesToChopForNewLine = 1;
    static constexpr int kConsecutiveFailuresThreshold = 3;
    static constexpr int kResponseTimeHistorySize = 10; // 用于异常检测
    static constexpr int kHealthCheckHistorySize = 20;  // 用于异常检测
    static constexpr int kRecentHealthCheckCount = 10;
    static constexpr int kMaxHistorySize = 50; // 历史记录列表的最大尺寸
    static constexpr std::chrono::milliseconds kMaxDataStallTime{30000};
    static constexpr double kInitialConnectionQuality = 1.0;

    QSerialPort m_serialPort;
    VT100Parser *m_parser;
    bool m_echoEnabled = false;
    NewLineMode m_newLineMode = kDefaultNewLineMode;
    QByteArray m_newLineSequence;
    DisplayMode m_displayMode = DisplayMode::ASCII; // 数据显示模式
    QString m_lastError;

    // 连接监控
    QTimer *m_connectionMonitor;
    bool m_isConnectionHealthy = true;
    int m_consecutiveFailures = 0;

    // 智能连接监控
    int m_baseMonitorInterval;    // 基础监控间隔（毫秒）
    int m_currentMonitorInterval; // 当前监控间隔（自适应）
    int m_minMonitorInterval;     // 最小监控间隔
    int m_maxMonitorInterval;     // 最大监控间隔

    // 连接质量评估
    double m_connectionQuality = kInitialConnectionQuality; // 连接质量评分 (0.0-1.0)
    QList<qint64> m_responseTimeHistory;                    // 响应时间历史
    QList<bool> m_healthCheckHistory;                       // 健康检查历史

    // 异常检测
    int m_dataStallCount = 0;     // 数据停滞计数
    QDateTime m_lastDataActivity; // 最后数据活动时间

    // 数据统计
    qint64 m_bytesReceived = 0;
    qint64 m_bytesSent = 0;
    QDateTime m_connectionTime;
    QDateTime m_lastReceiveTime;
    QDateTime m_lastSendTime;
    qint64 m_lastReceiveBytes = 0;
    qint64 m_lastSendBytes = 0;
    double m_receiveRate = 0.0;
    double m_sendRate = 0.0;
    QTimer *m_statisticsTimer;

    // 自动重连状态管理
    ConnectionState m_connectionState = ConnectionState::Disconnected;
    QTimer *m_reconnectTimer = nullptr;

    // 自动重连配置
    bool m_autoReconnectEnabled = true;
    int m_maxReconnectAttempts = App::Reconnect::kDefaultMaxReconnectAttempts;
    int m_reconnectIntervalMs;
    double m_reconnectIntervalMultiplier = App::Reconnect::kDefaultReconnectIntervalMultiplier;

    // 重连状态
    int m_currentReconnectAttempt = 0;
    int m_currentReconnectInterval;

    // 连接参数备份
    QString m_lastPortName;
    int m_lastBaudRate = App::Serial::kDefaultBaudRate;
    QSerialPort::DataBits m_lastDataBits = QSerialPort::Data8;
    QSerialPort::Parity m_lastParity = QSerialPort::NoParity;
    QSerialPort::StopBits m_lastStopBits = QSerialPort::OneStop;
    QSerialPort::FlowControl m_lastFlowControl = QSerialPort::NoFlowControl;
};

#endif // SERIALPROCESSOR_H
