#include "VT100Parser.h"
#include "core/TerminalConstants.h"

#include <QDebug>

#include <algorithm>

VT100Parser::VT100Parser(QObject *parent) : QObject(parent), m_state(Normal) {}

void VT100Parser::setCommandConfig(const VT100CommandConfig &config)
{
    m_config = config;
}

void VT100Parser::processData(const QByteArray &data)
{
    QByteArray normalizedData = data;
    // 规范化行尾符：将 \r\n 和 \r 都统一为 \n
    // 必须先替换 \r\n，再替换 \r，以避免错误处理
    normalizedData.replace("\r\n", "\n");
    normalizedData.replace("\r", "\n");

    m_commandList.clear();
    for (const char kCh : normalizedData)
    {
        const auto kByte = static_cast<uchar>(kCh);
        parseByte(kByte);
    }
    flushTextBuffer(); // 清空所有剩余的文本

    if (!m_commandList.isEmpty())
    {
        const QMutexLocker<QMutex> kLocker(&m_bufferMutex);

        // 先添加新命令
        m_commandBuffer.append(m_commandList);

        // 然后检查缓冲区溢出保护
        if (m_bufferOverflowProtectionEnabled && m_commandBuffer.size() > m_maxCommandBufferSize)
        {
            const qsizetype kCurrentSize = m_commandBuffer.size();

            // 发出警告信号
            emit bufferOverflowWarning(kCurrentSize, m_maxCommandBufferSize);

            // 改进的溢出处理：保留85%的数据，而不是严格限制到最大值
            // 这样可以减少频繁的溢出处理，同时保留更多数据
            auto targetSize = static_cast<qsizetype>(static_cast<double>(m_maxCommandBufferSize)
                                                     * App::Terminal::kBufferOverflowRetentionRatio);
            const qsizetype kCommandsToRemove = kCurrentSize - targetSize;
            if (kCommandsToRemove > 0 && kCommandsToRemove <= m_commandBuffer.size())
            {
                m_commandBuffer.remove(0, static_cast<int>(kCommandsToRemove));
            }
        }
    }
    emit parseFinished();
}

void VT100Parser::parseByte(uchar byte)
{
    switch (m_state)
    {
        case Normal:
            handleNormalState(byte);
            break;

        case Escape:
            if (byte == '[') // CSI (控制序列引导符)
            {
                m_state = CSIEntry;
            }
            else
            {
                // 如果需要，处理其他转义序列
                m_state = Normal;
            }
            break;

        case CSIEntry:
            m_paramsBuffer.clear();
            m_intermediateBuffer.clear();
            if ((byte >= '0' && byte <= '9') || (byte == ';'))
            {
                m_paramsBuffer.append(static_cast<char>(byte));
                m_state = CSIParam;
            }
            else if (byte >= App::Terminal::kCSIFinalRangeStart
                     && byte <= App::Terminal::kCSIFinalRangeEnd) // 有效的最终字符
            {
                // 处理不带参数的CSI序列
                handleCSISequence(byte);
                m_state = Normal;
            }
            else
            {
                // 无效字符，重置状态
                resetParserState();
            }
            break;

        case CSIParam:
            if ((byte >= '0' && byte <= '9') || (byte == ';'))
            {
                // 检查参数缓冲区长度限制
                if (m_paramsBuffer.size() < VT100Parser::kMaxParamsBufferSize)
                {
                    m_paramsBuffer.append(static_cast<char>(byte));
                }
                else
                {
                    // 参数过长，重置状态
                    resetParserState();
                }
            }
            else if (byte >= App::Terminal::kCSIFinalRangeStart
                     && byte <= App::Terminal::kCSIFinalRangeEnd) // 有效的最终字符
            {
                handleCSISequence(byte);
                m_state = Normal;
            }
            else
            {
                // 无效字符，重置状态
                resetParserState();
            }
            break;

        case CSIIntermediate:
            // 尚未实现
            m_state = Normal;
            break;
    }
}

void VT100Parser::handleNormalState(uchar byte)
{
    if (byte == App::Terminal::kEscapeChar) // 转义字符
    {
        flushTextBuffer();
        m_state = Escape;
    }
    else if (byte == '\b' || byte == '\t' || byte == '\a' || byte == '\f' || byte == '\v')
    {
        handleControlCharacter(byte);
    }
    else
    {
        // 只处理可打印的ASCII字符和换行符
        if ((byte >= App::Terminal::kPrintableRangeStart && byte <= App::Terminal::kPrintableRangeEnd) || byte == '\n')
        {
            m_textBuffer.append(QChar(byte));
        }
        // 其他所有无效或不可见字符将被忽略
    }
}

void VT100Parser::handleControlCharacter(uchar byte)
{
    switch (byte)
    {
        case '\b':
            if (m_config.enableBackspace)
            {
                flushTextBuffer();
                m_commandList.append(TerminalCommand{.type = TerminalCommand::Backspace, .params = {}});
            }
            break;
        case '\t':
            if (m_config.enableHorizontalTab)
            {
                flushTextBuffer();
                m_commandList.append(TerminalCommand{.type = TerminalCommand::HorizontalTab, .params = {}});
            }
            break;
        case '\a':
            if (m_config.enableBell)
            {
                flushTextBuffer();
                m_commandList.append(TerminalCommand{.type = TerminalCommand::Bell, .params = {}});
            }
            break;
        case '\f':
            if (m_config.enableFormFeed)
            {
                flushTextBuffer();
                m_commandList.append(TerminalCommand{.type = TerminalCommand::FormFeed, .params = {}});
            }
            // 如果禁用FormFeed，则忽略该字符（不做任何处理）
            break;
        case '\v':
            if (m_config.enableVerticalTab)
            {
                flushTextBuffer();
                m_commandList.append(TerminalCommand{.type = TerminalCommand::VerticalTab, .params = {}});
            }
            break;
        default:
            break;
    }
}

void VT100Parser::handleCSISequence(uchar finalByte)
{
    const QString kParamsStr = QString::fromLatin1(m_paramsBuffer);

    switch (finalByte)
    {
        case 'm': // SGR - 选择图形再现
            handleSGRSequence(kParamsStr);
            break;
        case 'A':
        case 'B':
        case 'C':
        case 'D':
        case 'H':
            handleCursorMovement(finalByte, kParamsStr);
            break;
        case 'J':
        case 'K':
            handleEraseCommands(finalByte, kParamsStr);
            break;
        default:
            break;
    }
}

void VT100Parser::handleSGRSequence(const QString &paramsStr)
{
    if (!m_config.enableSGR)
    {
        return;
    }

    flushTextBuffer();
    if (paramsStr.isEmpty())
    {
        m_commandList.append(TerminalCommand{.type = TerminalCommand::AttributesReset, .params = QVariantList{}});
        return;
    }

    const QStringList kParams = paramsStr.split(';');
    for (const QString &param : kParams)
    {
        bool ok = false;
        const int kCode = param.toInt(&ok);
        if (ok)
        {
            switch (kCode)
            {
                case 0:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::AttributesReset, .params = QVariantList{}});
                    break;
                case 1:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BoldChanged, .params = {true}});
                    break;
                case App::Terminal::kSGRBoldDisable:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BoldChanged, .params = {false}});
                    break;
                case App::Terminal::kSGRForegroundBlack:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::ForegroundColorChanged,
                                                         .params = {QColor(Qt::black)}});
                    break;
                case App::Terminal::kSGRForegroundRed:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::ForegroundColorChanged, .params = {QColor(Qt::red)}});
                    break;
                case App::Terminal::kSGRForegroundGreen:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::ForegroundColorChanged,
                                                         .params = {QColor(Qt::green)}});
                    break;
                case App::Terminal::kSGRForegroundYellow:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::ForegroundColorChanged,
                                                         .params = {QColor(Qt::yellow)}});
                    break;
                case App::Terminal::kSGRForegroundBlue:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::ForegroundColorChanged, .params = {QColor(Qt::blue)}});
                    break;
                case App::Terminal::kSGRForegroundMagenta:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::ForegroundColorChanged,
                                                         .params = {QColor(Qt::magenta)}});
                    break;
                case App::Terminal::kSGRForegroundCyan:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::ForegroundColorChanged, .params = {QColor(Qt::cyan)}});
                    break;
                case App::Terminal::kSGRForegroundWhite:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::ForegroundColorChanged,
                                                         .params = {QColor(Qt::white)}});
                    break;
                case App::Terminal::kSGRBackgroundBlack:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BackgroundColorChanged,
                                                         .params = {QColor(Qt::black)}});
                    break;
                case App::Terminal::kSGRBackgroundRed:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::BackgroundColorChanged, .params = {QColor(Qt::red)}});
                    break;
                case App::Terminal::kSGRBackgroundGreen:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BackgroundColorChanged,
                                                         .params = {QColor(Qt::green)}});
                    break;
                case App::Terminal::kSGRBackgroundYellow:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BackgroundColorChanged,
                                                         .params = {QColor(Qt::yellow)}});
                    break;
                case App::Terminal::kSGRBackgroundBlue:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::BackgroundColorChanged, .params = {QColor(Qt::blue)}});
                    break;
                case App::Terminal::kSGRBackgroundMagenta:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BackgroundColorChanged,
                                                         .params = {QColor(Qt::magenta)}});
                    break;
                case App::Terminal::kSGRBackgroundCyan:
                    m_commandList.append(
                        TerminalCommand{.type = TerminalCommand::BackgroundColorChanged, .params = {QColor(Qt::cyan)}});
                    break;
                case App::Terminal::kSGRBackgroundWhite:
                    m_commandList.append(TerminalCommand{.type = TerminalCommand::BackgroundColorChanged,
                                                         .params = {QColor(Qt::white)}});
                    break;
                default:
                    break;
            }
        }
    }
}

void VT100Parser::handleCursorMovement(uchar finalByte, const QString &paramsStr)
{
    if (!m_config.enableCursorMovement)
    {
        return;
    }

    flushTextBuffer();

    switch (finalByte)
    {
        case 'A': // 光标上移
        {
            const int kCount = paramsStr.isEmpty() ? 1 : paramsStr.toInt();
            m_commandList.append(
                TerminalCommand{.type = TerminalCommand::CursorUp, .params = {kCount > 0 ? kCount : 1}});
            break;
        }
        case 'B': // 光标下移
        {
            const int kCount = paramsStr.isEmpty() ? 1 : paramsStr.toInt();
            m_commandList.append(
                TerminalCommand{.type = TerminalCommand::CursorDown, .params = {kCount > 0 ? kCount : 1}});
            break;
        }
        case 'C': // 光标前移
        {
            const int kCount = paramsStr.isEmpty() ? 1 : paramsStr.toInt();
            m_commandList.append(
                TerminalCommand{.type = TerminalCommand::CursorForward, .params = {kCount > 0 ? kCount : 1}});
            break;
        }
        case 'D': // 光标后移
        {
            const int kCount = paramsStr.isEmpty() ? 1 : paramsStr.toInt();
            m_commandList.append(
                TerminalCommand{.type = TerminalCommand::CursorBack, .params = {kCount > 0 ? kCount : 1}});
            break;
        }
        case 'H': // CUP - 光标定位
        {
            QStringList params = paramsStr.split(';');
            const int kRow = (!params.empty() && !params[0].isEmpty()) ? params[0].toInt() : 1;
            const int kCol = (params.size() > 1 && !params[1].isEmpty()) ? params[1].toInt() : 1;
            m_commandList.append(
                TerminalCommand{.type = TerminalCommand::SetCursorPosition, .params = {kRow - 1, kCol - 1}});
            break;
        }
        default:
            break;
    }
}

void VT100Parser::handleEraseCommands(uchar finalByte, const QString &paramsStr)
{
    if (!m_config.enableErase)
    {
        return;
    }

    flushTextBuffer();
    const int kMode = paramsStr.isEmpty() ? 0 : paramsStr.toInt();

    switch (finalByte)
    {
        case 'J': // ED - 擦除显示
            m_commandList.append(TerminalCommand{.type = TerminalCommand::EraseInDisplay, .params = {kMode}});
            break;
        case 'K': // EL - 擦除行
            m_commandList.append(TerminalCommand{.type = TerminalCommand::EraseInLine, .params = {kMode}});
            break;
        default:
            break;
    }
}

void VT100Parser::flushTextBuffer()
{
    if (!m_textBuffer.isEmpty())
    {
        for (const QChar &ch : m_textBuffer)
        {
            m_commandList.append(TerminalCommand{.type = TerminalCommand::PrintableChar, .params = {ch}});
        }
        m_textBuffer.clear();
    }
}

auto VT100Parser::takeAllCommands() -> QList<TerminalCommand>
{
    QList<TerminalCommand> commands;
    {
        const QMutexLocker<QMutex> kLocker(&m_bufferMutex);
        commands.swap(m_commandBuffer); // 高效交换，避免复制
    }
    return commands;
}

auto VT100Parser::takeCommands(int maxCount) -> QList<TerminalCommand>
{
    QList<TerminalCommand> commands;
    const QMutexLocker<QMutex> kLocker(&m_bufferMutex);

    const int kCount = std::min(maxCount, static_cast<int>(m_commandBuffer.size()));
    if (kCount <= 0)
    {
        return commands;
    }

    // 从缓冲区前面取出指定数量的命令
    commands = m_commandBuffer.mid(0, kCount);
    // 从缓冲区中移除已取出的命令
    m_commandBuffer.remove(0, kCount);

    return commands;
}

auto VT100Parser::hasPendingCommands() const -> bool
{
    const QMutexLocker<QMutex> kLocker(&m_bufferMutex);
    return !m_commandBuffer.isEmpty();
}

void VT100Parser::setMaxCommandBufferSize(int maxSize)
{
    const QMutexLocker<QMutex> kLocker(&m_bufferMutex);
    m_maxCommandBufferSize =
        qMax(App::Terminal::kMinCommandBufferSize, static_cast<qsizetype>(maxSize)); // 最小1000个命令

    // 如果当前缓冲区超过新的限制，截断它
    if (m_commandBuffer.size() > m_maxCommandBufferSize)
    {
        const qsizetype kCommandsToRemove = m_commandBuffer.size() - m_maxCommandBufferSize;
        if (kCommandsToRemove > 0 && kCommandsToRemove <= m_commandBuffer.size())
        {
            m_commandBuffer.remove(0, static_cast<int>(kCommandsToRemove));
        }
    }
}

auto VT100Parser::getMaxCommandBufferSize() const -> int
{
    const QMutexLocker<QMutex> kLocker(&m_bufferMutex);
    return static_cast<int>(m_maxCommandBufferSize);
}

auto VT100Parser::getCurrentCommandCount() const -> int
{
    const QMutexLocker<QMutex> kLocker(&m_bufferMutex);
    return static_cast<int>(m_commandBuffer.size());
}

void VT100Parser::resetParserState()
{
    m_state = Normal;
    m_paramsBuffer.clear();
    m_intermediateBuffer.clear();
    // 注意：不清除m_textBuffer，因为可能包含有效的文本数据
}
