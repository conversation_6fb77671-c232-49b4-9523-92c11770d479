cmake_minimum_required(VERSION 3.10)

option(STATIC_BUILD "Build with static runtime and libraries" OFF)

if(STATIC_BUILD)
    message(STATUS "Building with static runtime and libraries")
    cmake_policy(SET CMP0091 NEW)
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
    set(QT_STATIC_BUILD ON)

    # For static builds, help CMake find Qt's bundled dependencies (like zlib)
    # by adding the Qt installation prefix to CMAKE_PREFIX_PATH.
    # We derive the prefix from Qt6_DIR, which is passed by the build script.
    if(DEFINED Qt6_DIR)
        get_filename_component(QT_INSTALL_PREFIX "${Qt6_DIR}/../../.." REALPATH)
        list(PREPEND CMAKE_PREFIX_PATH "${QT_INSTALL_PREFIX}")
        message(STATUS "Added Qt static install prefix to CMAKE_PREFIX_PATH: ${QT_INSTALL_PREFIX}")
    else()
        message(WARNING "STATIC_BUILD is ON but Qt6_DIR is not defined. This may lead to errors finding Qt dependencies.")
    endif()
endif()

project(SerialT VERSION 1.4.1 LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt包
find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets SerialPort)

# 根据构建类型决定是否显示控制台
# 自动发现源文件，遵循 SERIALT_DEVELOPMENT_SPECIFICATION.md 7.3 节
file(GLOB_RECURSE SOURCES
    "src/*.h"
    "src/*.cpp"
    "resources/*.qrc"
)

# 根据构建类型决定是否显示控制台 (WIN32 handle)
# Debug版本：显示控制台，便于调试和查看日志输出
# Release版本：隐藏控制台，提供更好的用户体验
if(NOT CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(EXECUTABLE_TYPE WIN32)
endif()

qt_add_executable(SerialT ${EXECUTABLE_TYPE} MANUAL_FINALIZATION ${SOURCES})

# --- 针对SerialT的静态分析和警告级别 ---
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    find_program(CLANGTIDY_EXE clang-tidy)
    if(CLANGTIDY_EXE)
        message(STATUS "Found clang-tidy, enabling static analysis for the SerialT target: ${CLANGTIDY_EXE}")
        set_property(TARGET SerialT PROPERTY CXX_CLANG_TIDY
            ${CLANGTIDY_EXE}            # 参数1: clang-tidy程序路径
            "--warnings-as-errors=*"    # 参数2: 将警告视为错误
            "-p=${CMAKE_BINARY_DIR}"      # 参数3: 编译数据库路径
        )
    else()
        message(WARNING "clang-tidy not found. Static analysis checks for SerialT will be skipped.")
    endif()
endif()

if(MSVC)
    # 为所有构建类型开启最高警告等级
    target_compile_options(SerialT PRIVATE /W4)
    # 在非 Debug 构建中，将警告视为错误
    if(NOT CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_options(SerialT PRIVATE /WX)
    endif()
else()
    # For Clang/GCC, add equivalent warnings
    target_compile_options(SerialT PRIVATE -Wall -Wextra -Wpedantic)
    if(NOT CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_options(SerialT PRIVATE -Werror)
    endif()
endif()

# 链接Qt库
target_link_libraries(SerialT PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Widgets
    Qt6::SerialPort
)

# 安装目标
install(TARGETS SerialT
    BUNDLE DESTINATION .
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(WIN32)
    set(RC_FILE ${CMAKE_CURRENT_SOURCE_DIR}/resources/app.rc)
    set(RC_ICON ${CMAKE_CURRENT_SOURCE_DIR}/resources/icons/icon.ico)
    configure_file(${CMAKE_CURRENT_SOURCE_DIR}/resources/app.rc.in ${RC_FILE} @ONLY)
    target_sources(SerialT PRIVATE ${RC_FILE})
endif()

# Finalize the executable
qt_finalize_executable(SerialT)

# 添加测试支持
option(BUILD_TESTING "Build tests" ON)
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()