# SerialT 测试框架

## 📁 目录结构

```
tests/
├── CMakeLists.txt          # 测试构建配置
├── README.md              # 本文档
├── unit/                  # 单元测试
│   ├── CMakeLists.txt
│   ├── test_serial_processor.cpp
│   ├── test_vt100_parser.cpp
│   ├── test_terminal_rendering.cpp
│   └── test_settings_persistence.cpp
└── integration/           # 集成测试
    ├── CMakeLists.txt
    └── (待添加的集成测试)
```

## 🧪 测试框架

使用 **Qt Test Framework** 作为主要测试框架：
- 与Qt完美集成
- 支持信号槽测试
- 支持GUI测试
- 内置基准测试

## 🚀 运行测试

### 构建测试
```bash
# 在项目根目录
mkdir build && cd build
cmake .. -DBUILD_TESTING=ON
cmake --build . --target run_all_tests
```

### 运行单个测试
```bash
# 在build目录
./tests/test_serial_processor
./tests/test_vt100_parser
./tests/test_terminal_rendering
./tests/test_settings_persistence
```

### 运行所有测试
```bash
# 使用CTest
ctest --output-on-failure --verbose

# 或使用自定义目标
cmake --build . --target run_all_tests
```

## 📊 测试覆盖率

在Debug模式下可以生成测试覆盖率报告：
```bash
cmake .. -DCMAKE_BUILD_TYPE=Debug
cmake --build . --target coverage
```

## 📝 编写测试指南

### 基本测试结构
```cpp
#include <QtTest/QtTest>
#include "../../src/YourClass.h"

class TestYourClass : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();    // 测试套件初始化
    void init();           // 每个测试前初始化
    void testFunction();   // 测试函数
    void cleanup();        // 每个测试后清理
    void cleanupTestCase(); // 测试套件清理

private:
    YourClass *m_instance;
};

QTEST_MAIN(TestYourClass)
#include "test_your_class.moc"
```

### 常用断言
```cpp
QVERIFY(condition);                    // 验证条件为真
QCOMPARE(actual, expected);            // 比较两个值
QVERIFY2(condition, "error message");  // 带错误信息的验证
QFAIL("Test failed");                  // 强制失败
```

### 信号测试
```cpp
QSignalSpy spy(object, &Class::signal);
// 触发信号的操作
QCOMPARE(spy.count(), 1);
```

## 🎯 测试策略

### 单元测试重点
- **SerialProcessor**: 端口操作、数据处理、信号发射
- **VT100Parser**: 控制序列解析、状态管理
- **HighPerformanceTerminal**: 渲染逻辑、用户交互
- **Settings**: 配置加载/保存、数据验证

### 集成测试重点
- 完整的数据流测试
- 长时间运行稳定性
- 内存泄漏检测
- 异常情况处理

## 🔧 CI/CD 集成

测试可以集成到CI/CD流程中：
```yaml
# GitHub Actions 示例
- name: Run Tests
  run: |
    cmake --build build --target run_all_tests
    ctest --test-dir build --output-on-failure
```
