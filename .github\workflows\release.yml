# 工作流的名称，会显示在 GitHub Actions 页面上
name: Build and Release for Windows

# 触发条件
on:
  # 1. 在创建并推送一个以 'v' 开头的标签时运行 (例如 v1.0, v1.2.1)
  push:
    tags:
      - 'v*'

  # 2. 允许在 GitHub Actions 页面手动触发此工作流
  workflow_dispatch:

env:
  # 编译类型，Release 是发布版本的最佳选择
  BUILD_TYPE: Release

jobs:
  build-and-release:
    permissions:
      contents: write # 必须添加此行，以允许 action 创建 Release

    # 你的软件是 Windows 的，所以我们必须使用 Windows 虚拟机
    runs-on: windows-latest

    steps:
    # 第一步：签出你的代码
    - name: Checkout repository
      uses: actions/checkout@v4

    # 第二步：安装 Qt
    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
        # 指定你需要的 Qt 版本
        version: '6.8.2'
        # 指定安装平台
        host: 'windows'
        # 指定目标平台
        target: 'desktop'
        # 指定编译器和架构
        arch: 'win64_msvc2022_64'
        # 关键！告诉 action 额外安装 qtserialport 模块
        modules: 'qtserialport'

    # 第三步：配置 CMake
    - name: Configure CMake
      run: cmake -B ${{github.workspace}}/build -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}}

    # 第四步：构建项目
    - name: Build
      run: cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

    # 第五步：打包应用程序
    - name: Package application
      run: |
        mkdir deploy
        cp -r ${{github.workspace}}/build/Release/* deploy/
        windeployqt --release deploy

    # 第六步：创建压缩包
    - name: Create ZIP archive
      run: Compress-Archive -Path deploy/* -DestinationPath SerialT-${{ github.ref_name }}.zip

    # 第七步：创建并上传 Release
    - name: Create Release and Upload Artifact
      uses: softprops/action-gh-release@v1
      with:
        files: SerialT-${{ github.ref_name }}.zip
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
