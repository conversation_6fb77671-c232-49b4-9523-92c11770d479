/**
 * @file LoggingConstants.h
 * @brief 定义了日志系统相关的所有常量，包括日志格式、文件管理和显示配置参数。
 *
 * 本文件包含了SerialT项目中日志记录系统的核心常量定义，涵盖日志文件命名、
 * 时间戳格式、文件分割策略和日志查看器配置等方面的参数。
 *
 * @note 这些常量影响日志的可读性和存储效率，修改时需要考虑用户习惯和存储空间。
 */
#ifndef LOGGINGCONSTANTS_H
#define LOGGINGCONSTANTS_H

#include <string_view>

namespace App::Logging {

using namespace std::string_view_literals;

// #############################################################################
// # 默认日志设置常量 (Default Logging Settings Constants)
// #############################################################################

/**
 * @brief 默认日志文件名格式
 *
 * 使用占位符定义日志文件的命名规则：
 * %P = 端口名称, %Y = 年份, %m = 月份, %d = 日期, %H = 小时, %M = 分钟, %S = 秒
 * 示例：COM3_2024-01-15_14-30-25.log
 */
inline constexpr std::string_view kDefaultLogFileNameFormat = "%P_%Y-%m-%d_%H-%M-%S.log"sv;

/**
 * @brief 默认日志时间戳启用状态
 *
 * 控制是否在日志条目中包含时间戳信息，便于问题追踪和分析。
 */
inline constexpr bool kDefaultLogTimestampEnabled = true;

/**
 * @brief 默认日志时间戳格式
 *
 * 定义日志条目中时间戳的显示格式，使用Qt的日期时间格式字符串。
 * 格式：[2024-01-15 14:30:25.123]
 */
inline constexpr std::string_view kDefaultLogTimestampFormat = "[yyyy-MM-dd hh:mm:ss.zzz] "sv;

/**
 * @brief 默认日志文件分割功能启用状态
 *
 * 控制是否启用日志文件自动分割功能，防止单个文件过大。
 */
inline constexpr bool kDefaultLogSplitEnabled = false;

/**
 * @brief 默认日志文件分割大小（MB）
 *
 * 当启用日志分割时，单个日志文件的最大大小限制。
 * 超过此大小会自动创建新的日志文件。
 */
inline constexpr int kDefaultLogSplitSizeMB = 10;

/**
 * @brief 默认使用内置日志查看器
 *
 * 控制是否使用应用内置的日志查看器，而不是外部编辑器。
 */
inline constexpr bool kDefaultUseLogViewer = true;

/**
 * @brief 默认端口打开时自动启用日志记录
 *
 * 控制是否在串口连接成功时自动开始日志记录，提升用户体验。
 */
inline constexpr bool kDefaultAutoEnableLogOnPortOpen = true;

} // namespace App::Logging

#endif // LOGGINGCONSTANTS_H
