#include <QApplication>
#include <QDir>
#include <QFile>
#include <QRegularExpression>
#include <QTest>
#include <QTextStream>
#include <QtTest/QtTest>

#include "../../src/Logger.h"

class TestLoggerTimestampFix : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    void testBasicTimestampPlacement();
    void testChunkedDataWithNewline();
    void testMixedNewlineTypes();
    void testNoTimestampInMiddleOfLine();
    void testMultipleLines();

private:
    Logger *m_logger;
    QString m_testLogPath;

    // 辅助方法
    QString readLogFile();
    void writeTestData(const QByteArray &data);
    bool hasTimestampInMiddleOfLine(const QString &content);
    int countTimestamps(const QString &content);
};

void TestLoggerTimestampFix::initTestCase()
{
    if (!QApplication::instance())
    {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestLoggerTimestampFix::cleanupTestCase() {}

void TestLoggerTimestampFix::init()
{
    m_logger = new Logger(this);

    // 创建临时日志文件路径
    m_testLogPath = QDir::temp().filePath("test_timestamp_fix.log");

    // 启用时间戳
    m_logger->updateSettings(true, "[yyyy-MM-dd hh:mm:ss.zzz] ");

    // 开始日志记录
    m_logger->startLogging(m_testLogPath, false, 10);
}

void TestLoggerTimestampFix::cleanup()
{
    if (m_logger)
    {
        m_logger->stopLogging();
        delete m_logger;
        m_logger = nullptr;
    }

    // 清理测试文件
    QFile::remove(m_testLogPath);
}

void TestLoggerTimestampFix::testBasicTimestampPlacement()
{
    // 测试基本的时间戳放置
    writeTestData("Hello World\n");

    QString content = readLogFile();
    QVERIFY(!content.isEmpty());

    // 验证时间戳在行开始
    QVERIFY(content.startsWith("["));
    QVERIFY(!hasTimestampInMiddleOfLine(content));
}

void TestLoggerTimestampFix::testChunkedDataWithNewline()
{
    // 测试分块数据 + 换行符的情况（重现原始bug）
    writeTestData("CDBK-load:8,4,193"); // 第一块
    writeTestData(" do iP\n");          // 第二块带换行符

    QString content = readLogFile();
    qDebug() << "Chunked data content:" << content;

    // 验证没有时间戳在行中间
    QVERIFY(!hasTimestampInMiddleOfLine(content));

    // 验证只有一个时间戳（在行开始）
    QCOMPARE(countTimestamps(content), 1);
}

void TestLoggerTimestampFix::testMixedNewlineTypes()
{
    // 测试不同类型的换行符
    writeTestData("Line1\r\n"); // Windows换行
    writeTestData("Line2\r");   // Mac换行
    writeTestData("Line3\n");   // Unix换行

    QString content = readLogFile();
    qDebug() << "Mixed newlines content:" << content;

    // 应该有3个时间戳（每行一个）
    QCOMPARE(countTimestamps(content), 3);

    // 验证没有时间戳在行中间
    QVERIFY(!hasTimestampInMiddleOfLine(content));
}

void TestLoggerTimestampFix::testNoTimestampInMiddleOfLine()
{
    // 专门测试时间戳不会出现在行中间
    writeTestData("Start");
    writeTestData("Middle");
    writeTestData("End\n");

    QString content = readLogFile();
    qDebug() << "No middle timestamp content:" << content;

    // 验证没有时间戳在行中间
    QVERIFY(!hasTimestampInMiddleOfLine(content));

    // 应该只有一个时间戳（在行开始）
    QCOMPARE(countTimestamps(content), 1);

    // 验证内容正确拼接
    QVERIFY(content.contains("StartMiddleEnd"));
}

void TestLoggerTimestampFix::testMultipleLines()
{
    // 测试多行数据
    writeTestData("Line1\nLine2\nLine3\n");

    QString content = readLogFile();
    qDebug() << "Multiple lines content:" << content;

    // 应该有3个时间戳
    QCOMPARE(countTimestamps(content), 3);

    // 验证没有时间戳在行中间
    QVERIFY(!hasTimestampInMiddleOfLine(content));
}

// 辅助方法实现
QString TestLoggerTimestampFix::readLogFile()
{
    QFile file(m_testLogPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        return QString();
    }

    QTextStream stream(&file);
    return stream.readAll();
}

void TestLoggerTimestampFix::writeTestData(const QByteArray &data)
{
    m_logger->writeData(data);
}

bool TestLoggerTimestampFix::hasTimestampInMiddleOfLine(const QString &content)
{
    // 检查是否有时间戳出现在行中间
    QRegularExpression timestampPattern(R"(\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\])");
    QRegularExpressionMatchIterator matches = timestampPattern.globalMatch(content);

    while (matches.hasNext())
    {
        QRegularExpressionMatch match = matches.next();
        qsizetype timestampPos = match.capturedStart();

        // 检查时间戳前面是否有非换行字符
        if (timestampPos > 0)
        {
            QChar prevChar = content.at(timestampPos - 1);
            if (prevChar != '\n' && prevChar != '\r')
            {
                qDebug() << "Found timestamp in middle at position:" << timestampPos;
                qDebug() << "Previous character:" << prevChar;
                qDebug() << "Context:" << content.mid(qMax(0, timestampPos - 20), 40);
                return true;
            }
        }
    }
    return false;
}

int TestLoggerTimestampFix::countTimestamps(const QString &content)
{
    QRegularExpression timestampPattern(R"(\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\])");
    QRegularExpressionMatchIterator matches = timestampPattern.globalMatch(content);

    int count = 0;
    while (matches.hasNext())
    {
        matches.next();
        count++;
    }
    return count;
}

#include "test_logger_timestamp_fix.moc"
QTEST_MAIN(TestLoggerTimestampFix)
