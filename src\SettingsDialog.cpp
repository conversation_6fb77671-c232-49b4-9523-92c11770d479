#include "SettingsDialog.h"
#include "core/SerialConstants.h"

#include <QCheckBox>
#include <QComboBox>
#include <QDoubleSpinBox>
#include <QFileDialog>
#include <QFontComboBox>
#include <QFormLayout>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QHeaderView>
#include <QLabel>
#include <QLineEdit>
#include <QMessageBox>
#include <QPushButton>
#include <QSerialPortInfo>
#include <QSpinBox>
#include <QStandardPaths>
#include <QTabWidget>
#include <QTableWidget>
#include <QUuid>
#include <QVBoxLayout>

#include <QDebug>

SettingsDialog::SettingsDialog(QWidget *parent)
    : QDialog(parent),
      m_tabWidget(nullptr),
      m_minimizeToTrayCheckBox(nullptr),
      m_alwaysShowInTrayCheckBox(nullptr),
      m_bufferSizeSpinBox(nullptr),
      m_scrollPolicyComboBox(nullptr),
      m_scrollOnInputCheckBox(nullptr),
      m_pauseOnResizeCheckBox(nullptr),
      m_wordWrapCheckBox(nullptr),
      m_displayModeComboBox(nullptr),
      m_fontComboBox(nullptr),
      m_fontSizeSpinBox(nullptr),
      m_fontPreviewLabel(nullptr),
      m_portComboBox(nullptr),
      m_baudRateComboBox(nullptr),
      m_dataBitsComboBox(nullptr),
      m_parityComboBox(nullptr),
      m_stopBitsComboBox(nullptr),
      m_newLineModeComboBox(nullptr),
      m_serialEchoCheckBox(nullptr),
      m_autoReconnectGroupBox(nullptr),
      m_autoReconnectEnabledCheckBox(nullptr),
      m_maxReconnectAttemptsSpinBox(nullptr),
      m_reconnectIntervalSpinBox(nullptr),
      m_reconnectIntervalMultiplierSpinBox(nullptr),
      m_logPathEdit(nullptr),
      m_logPathBrowseButton(nullptr),
      m_logFileNameFormatEdit(nullptr),
      m_logSplitCheckBox(nullptr),
      m_logSplitSizeSpinBox(nullptr),
      m_logTimestampGroupBox(nullptr),
      m_logTimestampCheckBox(nullptr),
      m_logTimestampFormatEdit(nullptr),
      m_useDefaultLogViewerCheckBox(nullptr),
      m_externalLogViewerPathEdit(nullptr),
      m_externalLogViewerBrowseButton(nullptr),
      m_autoEnableLogOnOpenCheckBox(nullptr),
      m_quickCommandsTable(nullptr),
      m_addCommandButton(nullptr),
      m_deleteCommandButton(nullptr),
      m_enableSgrCheckBox(nullptr),
      m_enableEraseCheckBox(nullptr),
      m_enableCursorMovementCheckBox(nullptr),
      m_enableBackspaceCheckBox(nullptr),
      m_enableHorizontalTabCheckBox(nullptr),
      m_enableBellCheckBox(nullptr),
      m_enableFormFeedCheckBox(nullptr),
      m_enableVerticalTabCheckBox(nullptr),
      m_enableLineFeedCheckBox(nullptr),
      m_enableCarriageReturnCheckBox(nullptr),
      m_okButton(nullptr),
      m_cancelButton(nullptr),
      m_applyButton(nullptr)
{
    setupUi();
    setWindowTitle("设置");
    setMinimumSize(kMinDialogWidth, kMinDialogHeight);
}

SettingsDialog::~SettingsDialog()
{
    // 显式清理可能导致内存泄漏的资源
    // Qt 的父子关系应该处理大部分清理，但我们确保关键资源被释放

    // 清理快捷命令表格中的自定义控件
    if (m_quickCommandsTable != nullptr)
    {
        for (int row = 0; row < m_quickCommandsTable->rowCount(); ++row)
        {
            // 清理表格中的自定义控件
            if (m_quickCommandsTable->cellWidget(row, EnabledColumn) != nullptr)
            {
                m_quickCommandsTable->removeCellWidget(row, EnabledColumn);
            }
            if (m_quickCommandsTable->cellWidget(row, FormatColumn) != nullptr)
            {
                m_quickCommandsTable->removeCellWidget(row, FormatColumn);
            }
            if (m_quickCommandsTable->cellWidget(row, CycleColumn) != nullptr)
            {
                m_quickCommandsTable->removeCellWidget(row, CycleColumn);
            }
            if (m_quickCommandsTable->cellWidget(row, IntervalColumn) != nullptr)
            {
                m_quickCommandsTable->removeCellWidget(row, IntervalColumn);
            }
        }
    }

    // 清理设置数据
    m_currentSettings.quickCommands.clear();
}

void SettingsDialog::setSettings(const AppSettings &settings)
{
    m_currentSettings = settings;
    loadSettingsToUi();
}

auto SettingsDialog::getSettings() const -> AppSettings
{
    return m_currentSettings;
}

void SettingsDialog::setSerialSettingsEnabled(bool enabled)
{
    // Core serial settings that require re-connection should be disabled
    m_portComboBox->setEnabled(enabled);
    m_baudRateComboBox->setEnabled(enabled);
    m_dataBitsComboBox->setEnabled(enabled);
    m_parityComboBox->setEnabled(enabled);
    m_stopBitsComboBox->setEnabled(enabled);

    // Settings that can be changed live should remain enabled
    m_newLineModeComboBox->setEnabled(true);
    m_serialEchoCheckBox->setEnabled(true);
}

void SettingsDialog::switchToQuickCommandsTab()
{
    // Find the quick commands tab and switch to it.
    for (int i = 0; i < m_tabWidget->count(); ++i)
    {
        if (m_tabWidget->tabText(i) == "快捷命令")
        {
            m_tabWidget->setCurrentIndex(i);
            break;
        }
    }
}

void SettingsDialog::setupUi()
{
    m_tabWidget = new QTabWidget(this);

    createGeneralTab();
    createAppearanceTab();
    createSerialTab();
    createLogTab();
    createQuickCommandsTab();
    createVt100Tab();
    // createScriptTab(); // As per design, stub for now

    m_okButton = new QPushButton("确定");
    m_cancelButton = new QPushButton("取消");
    m_applyButton = new QPushButton("应用");

    auto *buttonLayout = new QHBoxLayout;
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_cancelButton);
    buttonLayout->addWidget(m_applyButton);

    auto *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(m_tabWidget);
    mainLayout->addLayout(buttonLayout);

    connect(m_okButton, &QPushButton::clicked, this, &SettingsDialog::onOkButtonClicked);
    connect(m_cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    connect(m_applyButton, &QPushButton::clicked, this, &SettingsDialog::onApplyButtonClicked);
}

void SettingsDialog::createGeneralTab()
{
    auto *generalTab = new QWidget;
    auto *layout = new QFormLayout(generalTab);

    m_minimizeToTrayCheckBox = new QCheckBox("关闭时最小化到系统托盘");
    layout->addRow(m_minimizeToTrayCheckBox);

    m_alwaysShowInTrayCheckBox = new QCheckBox("始终在系统托盘显示图标");
    layout->addRow(m_alwaysShowInTrayCheckBox);

    m_bufferSizeSpinBox = new QSpinBox;
    m_bufferSizeSpinBox->setRange(kMinBufferSize, kMaxBufferSize);
    m_bufferSizeSpinBox->setSingleStep(kBufferSizeStep);
    m_bufferSizeSpinBox->setSuffix(" 行");
    layout->addRow("终端缓冲区大小:", m_bufferSizeSpinBox);

    m_scrollPolicyComboBox = new QComboBox;
    m_scrollPolicyComboBox->addItem("智能滚动 (默认)", 0);
    m_scrollPolicyComboBox->addItem("始终滚动到底部", 1);
    m_scrollPolicyComboBox->addItem("禁用自动滚动", 2);
    layout->addRow("滚动策略:", m_scrollPolicyComboBox);

    m_scrollOnInputCheckBox = new QCheckBox("输入后滚动到底部");
    layout->addRow(m_scrollOnInputCheckBox);

    m_pauseOnResizeCheckBox = new QCheckBox("拖动或缩放窗口时暂停渲染");
    m_pauseOnResizeCheckBox->setToolTip("在大量数据刷新时，此选项可以显著提升拖动窗口或调整大小时的流畅度。");
    layout->addRow(m_pauseOnResizeCheckBox);

    m_wordWrapCheckBox = new QCheckBox("启用自动换行");
    m_wordWrapCheckBox->setToolTip("启用后，长行文本将自动换行显示，禁用水平滚动条。");
    layout->addRow(m_wordWrapCheckBox);

    m_displayModeComboBox = new QComboBox;
    m_displayModeComboBox->addItem("ASCII文本模式", static_cast<int>(DisplayMode::ASCII));
    m_displayModeComboBox->addItem("十六进制模式", static_cast<int>(DisplayMode::HEX));
    m_displayModeComboBox->addItem("混合模式 (ASCII + HEX)", static_cast<int>(DisplayMode::MIXED));
    m_displayModeComboBox->setToolTip("选择数据的显示格式：ASCII文本、十六进制或混合显示。");
    layout->addRow("数据显示模式:", m_displayModeComboBox);

    m_tabWidget->addTab(generalTab, "常规");
}

void SettingsDialog::createAppearanceTab()
{
    auto *appearanceTab = new QWidget;
    auto *layout = new QFormLayout(appearanceTab);

    m_fontComboBox = new QFontComboBox;
    // 只显示等宽字体，提升终端显示效果
    m_fontComboBox->setFontFilters(QFontComboBox::MonospacedFonts);
    // 优化下拉列表的宽度
    m_fontComboBox->view()->setMinimumWidth(m_fontComboBox->sizeHint().width() + kFontComboBoxExtraWidth);
    layout->addRow("字体:", m_fontComboBox);

    m_fontSizeSpinBox = new QSpinBox;
    m_fontSizeSpinBox->setRange(kMinFontSize, kMaxFontSize);
    m_fontSizeSpinBox->setSuffix(" pt");
    layout->addRow("大小:", m_fontSizeSpinBox);

    // 添加字体预览
    m_fontPreviewLabel = new QLabel("AaBbYyZz 123");
    m_fontPreviewLabel->setFrameShape(QFrame::StyledPanel);
    m_fontPreviewLabel->setMinimumHeight(kPreviewLabelMinHeight);
    m_fontPreviewLabel->setAlignment(Qt::AlignCenter);
    layout->addRow("预览:", m_fontPreviewLabel);

    m_tabWidget->addTab(appearanceTab, "外观");

    // 连接信号以更新预览
    connect(m_fontComboBox, &QFontComboBox::currentFontChanged, this, &SettingsDialog::updateFontPreview);
    connect(m_fontSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SettingsDialog::updateFontPreview);
}

void SettingsDialog::createSerialTab()
{
    auto *serialTab = new QWidget;
    auto *layout = new QFormLayout(serialTab);

    m_portComboBox = new QComboBox;
    const auto kInfos = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &info : kInfos)
    {
        m_portComboBox->addItem(info.portName());
    }
    layout->addRow("串口:", m_portComboBox);

    m_baudRateComboBox = new QComboBox;
    QStringList baudRates;
    for (const char *rate : App::Serial::kBaudRateList)
    {
        baudRates << rate;
    }
    m_baudRateComboBox->addItems(baudRates);
    m_baudRateComboBox->setEditable(true);
    layout->addRow("波特率:", m_baudRateComboBox);

    m_dataBitsComboBox = new QComboBox;
    m_dataBitsComboBox->addItem("5", QSerialPort::Data5);
    m_dataBitsComboBox->addItem("6", QSerialPort::Data6);
    m_dataBitsComboBox->addItem("7", QSerialPort::Data7);
    m_dataBitsComboBox->addItem("8", QSerialPort::Data8);
    layout->addRow("数据位:", m_dataBitsComboBox);

    m_parityComboBox = new QComboBox;
    m_parityComboBox->addItem("无", QSerialPort::NoParity);
    m_parityComboBox->addItem("偶校验", QSerialPort::EvenParity);
    m_parityComboBox->addItem("奇校验", QSerialPort::OddParity);
    m_parityComboBox->addItem("空格校验", QSerialPort::SpaceParity);
    m_parityComboBox->addItem("标记校验", QSerialPort::MarkParity);
    layout->addRow("校验位:", m_parityComboBox);

    m_stopBitsComboBox = new QComboBox;
    m_stopBitsComboBox->addItem("1", QSerialPort::OneStop);
    m_stopBitsComboBox->addItem("1.5", QSerialPort::OneAndHalfStop);
    m_stopBitsComboBox->addItem("2", QSerialPort::TwoStop);
    layout->addRow("停止位:", m_stopBitsComboBox);

    m_newLineModeComboBox = new QComboBox;
    m_newLineModeComboBox->addItem("CR+LF (\\r\\n)", 0);
    m_newLineModeComboBox->addItem("CR (\\r)", 1);
    m_newLineModeComboBox->addItem("LF (\\n)", 2);
    layout->addRow("发送新行:", m_newLineModeComboBox);

    m_serialEchoCheckBox = new QCheckBox("启用本地回显");
    layout->addRow(m_serialEchoCheckBox);

    // --- 自动重连设置 ---
    m_autoReconnectGroupBox = new QGroupBox("自动重连");
    auto *reconnectLayout = new QFormLayout(m_autoReconnectGroupBox);

    m_autoReconnectEnabledCheckBox = new QCheckBox("启用自动重连");
    m_autoReconnectEnabledCheckBox->setToolTip("当串口连接意外断开时，自动尝试重新连接");
    reconnectLayout->addRow(m_autoReconnectEnabledCheckBox);

    m_maxReconnectAttemptsSpinBox = new QSpinBox;
    m_maxReconnectAttemptsSpinBox->setRange(App::Reconnect::kMinReconnectAttempts,
                                            App::Reconnect::kMaxReconnectAttempts);
    m_maxReconnectAttemptsSpinBox->setSuffix(" 次");
    m_maxReconnectAttemptsSpinBox->setToolTip("达到最大重连次数后将停止自动重连");
    reconnectLayout->addRow("最大重连次数:", m_maxReconnectAttemptsSpinBox);

    m_reconnectIntervalSpinBox = new QSpinBox;
    m_reconnectIntervalSpinBox->setRange(App::Reconnect::kMinReconnectIntervalMs,
                                         App::Reconnect::kMaxReconnectIntervalMs);
    m_reconnectIntervalSpinBox->setSingleStep(App::Reconnect::kMinReconnectIntervalMs);
    m_reconnectIntervalSpinBox->setSuffix(" ms");
    m_reconnectIntervalSpinBox->setToolTip("第一次重连尝试的等待时间");
    reconnectLayout->addRow("初始重连间隔:", m_reconnectIntervalSpinBox);

    m_reconnectIntervalMultiplierSpinBox = new QDoubleSpinBox;
    m_reconnectIntervalMultiplierSpinBox->setRange(App::Reconnect::kMinReconnectMultiplier,
                                                   App::Reconnect::kMaxReconnectMultiplier);
    m_reconnectIntervalMultiplierSpinBox->setSingleStep(App::Reconnect::kReconnectIntervalStep);
    m_reconnectIntervalMultiplierSpinBox->setDecimals(App::Reconnect::kReconnectIntervalDecimals);
    m_reconnectIntervalMultiplierSpinBox->setToolTip("每次重连失败后，等待时间将乘以此倍数（指数退避）");
    reconnectLayout->addRow("间隔递增倍数:", m_reconnectIntervalMultiplierSpinBox);

    layout->addRow(m_autoReconnectGroupBox);

    // 连接信号以实现控件联动
    connect(m_autoReconnectEnabledCheckBox, &QCheckBox::toggled, this, [this](bool enabled) {
        m_maxReconnectAttemptsSpinBox->setEnabled(enabled);
        m_reconnectIntervalSpinBox->setEnabled(enabled);
        m_reconnectIntervalMultiplierSpinBox->setEnabled(enabled);
    });

    m_tabWidget->addTab(serialTab, "串口");
}

void SettingsDialog::createLogTab()
{
    auto *logTab = new QWidget;
    auto *layout = new QFormLayout(logTab);

    auto *pathLayout = new QHBoxLayout;
    m_logPathEdit = new QLineEdit;
    m_logPathBrowseButton = new QPushButton("...");
    m_logPathBrowseButton->setMaximumWidth(kLogPathButtonWidth);
    pathLayout->addWidget(m_logPathEdit);
    pathLayout->addWidget(m_logPathBrowseButton);
    layout->addRow("日志文件路径:", pathLayout);

    m_logFileNameFormatEdit = new QLineEdit;
    layout->addRow("日志文件名格式:", m_logFileNameFormatEdit);

    m_logSplitCheckBox = new QCheckBox("启用日志文件分割");
    layout->addRow(m_logSplitCheckBox);

    m_logSplitSizeSpinBox = new QSpinBox;
    m_logSplitSizeSpinBox->setRange(kMinLogSplitSize, kMaxLogSplitSize);
    m_logSplitSizeSpinBox->setSuffix(" MB");
    layout->addRow("分割大小:", m_logSplitSizeSpinBox);

    connect(m_logPathBrowseButton, &QPushButton::clicked, this, &SettingsDialog::onLogPathBrowseClicked);
    connect(m_logSplitCheckBox, &QCheckBox::toggled, m_logSplitSizeSpinBox, &QSpinBox::setEnabled);

    // --- 时间戳设置 ---
    m_logTimestampGroupBox = new QGroupBox("时间戳");
    m_logTimestampGroupBox->setCheckable(true); // Use the groupbox's own checkbox
    auto *timestampLayout = new QFormLayout(m_logTimestampGroupBox);
    m_logTimestampFormatEdit = new QLineEdit();
    m_logTimestampFormatEdit->setPlaceholderText("例如: [yyyy-MM-dd hh:mm:ss.zzz] ");
    timestampLayout->addRow("时间戳格式:", m_logTimestampFormatEdit);
    layout->addRow(m_logTimestampGroupBox);

    // --- 自动启用日志 ---
    m_autoEnableLogOnOpenCheckBox = new QCheckBox("打开串口时自动启用日志");
    m_autoEnableLogOnOpenCheckBox->setToolTip("当打开一个串口时，如果日志功能当前未启用，则自动启用日志。");
    layout->addRow(m_autoEnableLogOnOpenCheckBox);

    // --- 日志查看器 ---
    auto *viewerGroupBox = new QGroupBox("日志查看器");
    auto *viewerLayout = new QFormLayout;

    m_useDefaultLogViewerCheckBox = new QCheckBox("使用系统默认工具打开日志");
    viewerLayout->addRow(m_useDefaultLogViewerCheckBox);

    auto *externalViewerLayout = new QHBoxLayout;
    m_externalLogViewerPathEdit = new QLineEdit;
    m_externalLogViewerBrowseButton = new QPushButton("浏览...");
    externalViewerLayout->addWidget(m_externalLogViewerPathEdit);
    externalViewerLayout->addWidget(m_externalLogViewerBrowseButton);
    viewerLayout->addRow("外部工具路径:", externalViewerLayout);

    viewerGroupBox->setLayout(viewerLayout);
    layout->addWidget(viewerGroupBox);

    connect(m_useDefaultLogViewerCheckBox, &QCheckBox::toggled, this, [this](bool checked) {
        m_externalLogViewerPathEdit->setDisabled(checked);
        m_externalLogViewerBrowseButton->setDisabled(checked);
    });
    connect(m_externalLogViewerBrowseButton, &QPushButton::clicked, this, &SettingsDialog::onLogViewerBrowseClicked);

    m_tabWidget->addTab(logTab, "日志");
}

void SettingsDialog::createQuickCommandsTab()
{
    auto *quickCommandsTab = new QWidget;
    auto *layout = new QVBoxLayout(quickCommandsTab);

    m_quickCommandsTable = new QTableWidget;
    m_quickCommandsTable->setColumnCount(ColumnCount);
    m_quickCommandsTable->setHorizontalHeaderLabels({"UUID", "启用", "名称", "命令", "格式", "循环", "间隔(ms)"});
    m_quickCommandsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_quickCommandsTable->verticalHeader()->setVisible(false);
    m_quickCommandsTable->verticalHeader()->setDefaultSectionSize(kQuickCommandRowHeight); // 设置默认行高
    m_quickCommandsTable->horizontalHeader()->setStretchLastSection(true);
    // Hide UUID column
    m_quickCommandsTable->setColumnHidden(UuidColumn, true);

    auto *buttonLayout = new QHBoxLayout;
    m_addCommandButton = new QPushButton("添加命令");
    m_deleteCommandButton = new QPushButton("删除选中命令");
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_addCommandButton);
    buttonLayout->addWidget(m_deleteCommandButton);

    layout->addWidget(m_quickCommandsTable);
    layout->addLayout(buttonLayout);

    connect(m_addCommandButton, &QPushButton::clicked, this, &SettingsDialog::onAddQuickCommandClicked);
    connect(m_deleteCommandButton, &QPushButton::clicked, this, &SettingsDialog::onDeleteQuickCommandClicked);

    m_tabWidget->addTab(quickCommandsTab, "快捷命令");
}

void SettingsDialog::createVt100Tab()
{
    auto *vt100Tab = new QWidget;
    auto *mainLayout = new QVBoxLayout(vt100Tab);

    auto *csiGroup = new QGroupBox("CSI 控制序列 (Control Sequence Introducer)");
    auto *gridLayout = new QGridLayout(csiGroup);
    gridLayout->setSpacing(kLayoutSpacing);

    // SGR
    m_enableSgrCheckBox = new QCheckBox("SGR");
    m_enableSgrCheckBox->setToolTip("启用 SGR (Select Graphic Rendition) 序列\n"
                                    "控制文本属性，如颜色、粗体、重置等。\n"
                                    "例如: ESC[31m (设置前景色为红色)");
    gridLayout->addWidget(m_enableSgrCheckBox, 0, 0);

    // Erase
    m_enableEraseCheckBox = new QCheckBox("ED, EL");
    m_enableEraseCheckBox->setToolTip("启用擦除序列\n"
                                      "ED: Erase in Display (擦除显示区域)\n"
                                      "EL: Erase in Line (擦除行)\n"
                                      "例如: ESC[2J (清屏)");
    gridLayout->addWidget(m_enableEraseCheckBox, 0, 1);

    // Cursor Movement
    m_enableCursorMovementCheckBox = new QCheckBox("CUU, CUD, CUF, CUB, CUP");
    m_enableCursorMovementCheckBox->setToolTip("启用光标移动序列\n"
                                               "CUU: Cursor Up\n"
                                               "CUD: Cursor Down\n"
                                               "CUF: Cursor Forward\n"
                                               "CUB: Cursor Back\n"
                                               "CUP: Cursor Position\n"
                                               "例如: ESC[10;20H (移动光标到第10行第20列)");
    gridLayout->addWidget(m_enableCursorMovementCheckBox, 1, 0, 1, 2); // 跨两列

    mainLayout->addWidget(csiGroup);

    // ASCII控制字符组
    auto *asciiGroup = new QGroupBox("ASCII 控制字符");
    auto *asciiLayout = new QGridLayout(asciiGroup);
    asciiLayout->setSpacing(kLayoutSpacing);

    // Backspace
    m_enableBackspaceCheckBox = new QCheckBox("Backspace (\\b)");
    m_enableBackspaceCheckBox->setToolTip("启用退格字符 (0x08)\n"
                                          "移动光标向左一个位置");
    asciiLayout->addWidget(m_enableBackspaceCheckBox, 0, 0);

    // Horizontal Tab
    m_enableHorizontalTabCheckBox = new QCheckBox("Tab (\\t)");
    m_enableHorizontalTabCheckBox->setToolTip("启用水平制表符 (0x09)\n"
                                              "移动光标到下一个制表位");
    asciiLayout->addWidget(m_enableHorizontalTabCheckBox, 0, 1);

    // Bell
    m_enableBellCheckBox = new QCheckBox("Bell (\\a)");
    m_enableBellCheckBox->setToolTip("启用响铃字符 (0x07)\n"
                                     "发出系统提示音");
    asciiLayout->addWidget(m_enableBellCheckBox, 1, 0);

    // Form Feed
    m_enableFormFeedCheckBox = new QCheckBox("Form Feed (\\f)");
    m_enableFormFeedCheckBox->setToolTip("启用换页字符 (0x0C)\n"
                                         "现代终端通常忽略或视为换行\n"
                                         "注意: 禁用此选项可避免干扰信号导致的意外清屏");
    asciiLayout->addWidget(m_enableFormFeedCheckBox, 1, 1);

    // Vertical Tab
    m_enableVerticalTabCheckBox = new QCheckBox("Vertical Tab (\\v)");
    m_enableVerticalTabCheckBox->setToolTip("启用垂直制表符 (0x0B)\n"
                                            "移动光标到下一行");
    asciiLayout->addWidget(m_enableVerticalTabCheckBox, 2, 0);

    // Line Feed (强制启用)
    m_enableLineFeedCheckBox = new QCheckBox("Line Feed (\\n)");
    m_enableLineFeedCheckBox->setToolTip("换行符 (0x0A)\n"
                                         "此功能始终启用，不可禁用\n"
                                         "换行符是终端基本功能，禁用会导致显示异常");
    m_enableLineFeedCheckBox->setChecked(true);
    m_enableLineFeedCheckBox->setEnabled(false); // 禁用控件，不允许用户取消选中
    m_enableLineFeedCheckBox->setStyleSheet("QCheckBox:disabled { color: gray; }");
    asciiLayout->addWidget(m_enableLineFeedCheckBox, 2, 1);

    // Carriage Return (强制启用)
    m_enableCarriageReturnCheckBox = new QCheckBox("Carriage Return (\\r)");
    m_enableCarriageReturnCheckBox->setToolTip("回车符 (0x0D)\n"
                                               "自动转换为换行符 (\\r → \\n)\n"
                                               "此功能始终启用，不可禁用\n"
                                               "确保跨平台换行符兼容性");
    m_enableCarriageReturnCheckBox->setChecked(true);
    m_enableCarriageReturnCheckBox->setEnabled(false); // 禁用控件，不允许用户取消选中
    m_enableCarriageReturnCheckBox->setStyleSheet("QCheckBox:disabled { color: gray; }");
    asciiLayout->addWidget(m_enableCarriageReturnCheckBox, 3, 0);

    mainLayout->addWidget(asciiGroup);
    mainLayout->addStretch();

    m_tabWidget->addTab(vt100Tab, "VT100命令");
}

void SettingsDialog::createScriptTab()
{
    // This is a placeholder for future implementation
    auto *scriptTab = new QWidget;
    auto *layout = new QVBoxLayout(scriptTab);
    layout->addWidget(new QLabel("脚本设置将在未来版本中提供。"));
    layout->addStretch();
    m_tabWidget->addTab(scriptTab, "脚本");
}

void SettingsDialog::loadSettingsToUi()
{
    // General
    m_minimizeToTrayCheckBox->setChecked(m_currentSettings.minimizeToTrayOnClose);
    m_alwaysShowInTrayCheckBox->setChecked(m_currentSettings.alwaysShowInTray);
    m_bufferSizeSpinBox->setValue(m_currentSettings.terminalBufferSize);
    m_scrollPolicyComboBox->setCurrentIndex(m_currentSettings.terminalScrollPolicy);
    m_scrollOnInputCheckBox->setChecked(m_currentSettings.scrollOnInput);
    m_pauseOnResizeCheckBox->setChecked(m_currentSettings.pauseOnResize);
    m_wordWrapCheckBox->setChecked(m_currentSettings.wordWrapEnabled);
    m_displayModeComboBox->setCurrentIndex(static_cast<int>(m_currentSettings.displayMode));

    // Appearance
    m_fontComboBox->setCurrentFont(m_currentSettings.terminalFont);
    m_fontSizeSpinBox->setValue(m_currentSettings.terminalFont.pointSize());
    updateFontPreview(); // 初始化预览

    // Serial
    m_portComboBox->setCurrentText(m_currentSettings.portName);
    m_baudRateComboBox->setCurrentText(QString::number(m_currentSettings.baudRate));
    m_dataBitsComboBox->setCurrentIndex(m_dataBitsComboBox->findData(m_currentSettings.dataBits));
    m_parityComboBox->setCurrentIndex(m_parityComboBox->findData(m_currentSettings.parity));
    m_stopBitsComboBox->setCurrentIndex(m_stopBitsComboBox->findData(m_currentSettings.stopBits));
    m_newLineModeComboBox->setCurrentIndex(m_currentSettings.sendNewLineMode);
    m_serialEchoCheckBox->setChecked(m_currentSettings.serialEchoEnabled);

    // Auto-reconnect settings
    m_autoReconnectEnabledCheckBox->setChecked(m_currentSettings.autoReconnectEnabled);
    m_maxReconnectAttemptsSpinBox->setValue(m_currentSettings.maxReconnectAttempts);
    m_reconnectIntervalSpinBox->setValue(m_currentSettings.reconnectIntervalMs);
    m_reconnectIntervalMultiplierSpinBox->setValue(m_currentSettings.reconnectIntervalMultiplier);

    // 设置控件联动状态
    m_maxReconnectAttemptsSpinBox->setEnabled(m_currentSettings.autoReconnectEnabled);
    m_reconnectIntervalSpinBox->setEnabled(m_currentSettings.autoReconnectEnabled);
    m_reconnectIntervalMultiplierSpinBox->setEnabled(m_currentSettings.autoReconnectEnabled);

    // Logging
    m_logPathEdit->setText(m_currentSettings.logPath);
    m_logFileNameFormatEdit->setText(m_currentSettings.logFileNameFormat);
    m_logSplitCheckBox->setChecked(m_currentSettings.logSplitEnabled);
    m_logSplitSizeSpinBox->setValue(m_currentSettings.logSplitSizeMB);
    m_logSplitSizeSpinBox->setEnabled(m_currentSettings.logSplitEnabled);

    // Timestamp
    m_logTimestampGroupBox->setChecked(m_currentSettings.logTimestampEnabled);
    m_logTimestampFormatEdit->setText(m_currentSettings.logTimestampFormat);

    m_useDefaultLogViewerCheckBox->setChecked(m_currentSettings.useDefaultLogViewer);
    m_externalLogViewerPathEdit->setText(m_currentSettings.externalLogViewerPath);
    m_externalLogViewerPathEdit->setDisabled(m_currentSettings.useDefaultLogViewer);
    m_externalLogViewerBrowseButton->setDisabled(m_currentSettings.useDefaultLogViewer);
    m_autoEnableLogOnOpenCheckBox->setChecked(m_currentSettings.autoEnableLogOnPortOpen);

    // VT100 Commands
    m_enableSgrCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableSGR);
    m_enableEraseCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableErase);
    m_enableCursorMovementCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableCursorMovement);

    // ASCII Control Characters
    m_enableBackspaceCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableBackspace);
    m_enableHorizontalTabCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableHorizontalTab);
    m_enableBellCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableBell);
    m_enableFormFeedCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableFormFeed);
    m_enableVerticalTabCheckBox->setChecked(m_currentSettings.vt100CommandConfig.enableVerticalTab);

    // 换行符控件 (始终保持选中且禁用状态)
    m_enableLineFeedCheckBox->setChecked(true);
    m_enableLineFeedCheckBox->setEnabled(false);
    m_enableCarriageReturnCheckBox->setChecked(true);
    m_enableCarriageReturnCheckBox->setEnabled(false);

    // Quick Commands
    m_quickCommandsTable->setRowCount(0);
    for (const auto &cmd : std::as_const(m_currentSettings.quickCommands))
    {
        const int kRow = m_quickCommandsTable->rowCount();
        m_quickCommandsTable->insertRow(kRow);

        m_quickCommandsTable->setItem(kRow, UuidColumn, new QTableWidgetItem(cmd.uuid));

        auto *enabledCheck = new QCheckBox();
        enabledCheck->setChecked(cmd.enabled);
        m_quickCommandsTable->setCellWidget(kRow, EnabledColumn, enabledCheck);

        m_quickCommandsTable->setItem(kRow, NameColumn, new QTableWidgetItem(cmd.name));
        m_quickCommandsTable->setItem(kRow, CommandColumn, new QTableWidgetItem(cmd.command));

        auto *formatComboBox = new QComboBox();
        formatComboBox->addItems({"ASCII", "HEX"});
        formatComboBox->setCurrentIndex(static_cast<int>(cmd.format));
        m_quickCommandsTable->setCellWidget(kRow, FormatColumn, formatComboBox);

        auto *cycleCheck = new QCheckBox();
        cycleCheck->setChecked(cmd.isCycle);
        m_quickCommandsTable->setCellWidget(kRow, CycleColumn, cycleCheck);

        auto *intervalSpin = new QSpinBox();
        intervalSpin->setRange(kMinQuickCommandInterval, kMaxQuickCommandInterval);
        intervalSpin->setValue(cmd.cycleInterval);
        m_quickCommandsTable->setCellWidget(kRow, IntervalColumn, intervalSpin);
    }
}

void SettingsDialog::saveUiToSettings()
{
    // General
    m_currentSettings.minimizeToTrayOnClose = m_minimizeToTrayCheckBox->isChecked();
    m_currentSettings.alwaysShowInTray = m_alwaysShowInTrayCheckBox->isChecked();
    m_currentSettings.terminalBufferSize = m_bufferSizeSpinBox->value();
    m_currentSettings.terminalScrollPolicy = m_scrollPolicyComboBox->currentIndex();
    m_currentSettings.scrollOnInput = m_scrollOnInputCheckBox->isChecked();
    m_currentSettings.pauseOnResize = m_pauseOnResizeCheckBox->isChecked();
    m_currentSettings.wordWrapEnabled = m_wordWrapCheckBox->isChecked();
    m_currentSettings.displayMode = static_cast<DisplayMode>(m_displayModeComboBox->currentIndex());

    // Appearance
    QFont font = m_fontComboBox->currentFont();
    font.setPointSize(m_fontSizeSpinBox->value());
    m_currentSettings.terminalFont = font;

    // Serial
    m_currentSettings.portName = m_portComboBox->currentText();
    m_currentSettings.baudRate = m_baudRateComboBox->currentText().toInt();
    m_currentSettings.dataBits = static_cast<QSerialPort::DataBits>(m_dataBitsComboBox->currentData().toInt());
    m_currentSettings.parity = static_cast<QSerialPort::Parity>(m_parityComboBox->currentData().toInt());
    m_currentSettings.stopBits = static_cast<QSerialPort::StopBits>(m_stopBitsComboBox->currentData().toInt());
    m_currentSettings.sendNewLineMode = m_newLineModeComboBox->currentIndex();
    m_currentSettings.serialEchoEnabled = m_serialEchoCheckBox->isChecked();

    // Auto-reconnect settings
    m_currentSettings.autoReconnectEnabled = m_autoReconnectEnabledCheckBox->isChecked();
    m_currentSettings.maxReconnectAttempts = m_maxReconnectAttemptsSpinBox->value();
    m_currentSettings.reconnectIntervalMs = m_reconnectIntervalSpinBox->value();
    m_currentSettings.reconnectIntervalMultiplier = m_reconnectIntervalMultiplierSpinBox->value();

    // Logging
    m_currentSettings.logPath = m_logPathEdit->text();
    m_currentSettings.logFileNameFormat = m_logFileNameFormatEdit->text();
    m_currentSettings.logSplitEnabled = m_logSplitCheckBox->isChecked();
    m_currentSettings.logSplitSizeMB = m_logSplitSizeSpinBox->value();
    m_currentSettings.logTimestampEnabled = m_logTimestampGroupBox->isChecked();
    m_currentSettings.logTimestampFormat = m_logTimestampFormatEdit->text();
    m_currentSettings.useDefaultLogViewer = m_useDefaultLogViewerCheckBox->isChecked();
    m_currentSettings.externalLogViewerPath = m_externalLogViewerPathEdit->text();
    m_currentSettings.autoEnableLogOnPortOpen = m_autoEnableLogOnOpenCheckBox->isChecked();

    // VT100 Commands
    m_currentSettings.vt100CommandConfig.enableSGR = m_enableSgrCheckBox->isChecked();
    m_currentSettings.vt100CommandConfig.enableErase = m_enableEraseCheckBox->isChecked();
    m_currentSettings.vt100CommandConfig.enableCursorMovement = m_enableCursorMovementCheckBox->isChecked();

    // ASCII Control Characters
    m_currentSettings.vt100CommandConfig.enableBackspace = m_enableBackspaceCheckBox->isChecked();
    m_currentSettings.vt100CommandConfig.enableHorizontalTab = m_enableHorizontalTabCheckBox->isChecked();
    m_currentSettings.vt100CommandConfig.enableBell = m_enableBellCheckBox->isChecked();
    m_currentSettings.vt100CommandConfig.enableFormFeed = m_enableFormFeedCheckBox->isChecked();
    m_currentSettings.vt100CommandConfig.enableVerticalTab = m_enableVerticalTabCheckBox->isChecked();

    // Quick Commands
    m_currentSettings.quickCommands.clear();
    for (int row = 0; row < m_quickCommandsTable->rowCount(); ++row)
    {
        QuickCommand cmd;
        cmd.uuid = m_quickCommandsTable->item(row, UuidColumn)->text();
        if (auto *cb = qobject_cast<QCheckBox *>(m_quickCommandsTable->cellWidget(row, EnabledColumn)))
        {
            cmd.enabled = cb->isChecked();
        }
        cmd.name = m_quickCommandsTable->item(row, NameColumn)->text();
        cmd.command = m_quickCommandsTable->item(row, CommandColumn)->text();
        if (auto *combo = qobject_cast<QComboBox *>(m_quickCommandsTable->cellWidget(row, FormatColumn)))
        {
            cmd.format = (combo->currentText() == "HEX") ? CommandFormat::HEX : CommandFormat::ASCII;
        }
        if (auto *cb = qobject_cast<QCheckBox *>(m_quickCommandsTable->cellWidget(row, CycleColumn)))
        {
            cmd.isCycle = cb->isChecked();
        }
        if (auto *spin = qobject_cast<QSpinBox *>(m_quickCommandsTable->cellWidget(row, IntervalColumn)))
        {
            cmd.cycleInterval = spin->value();
        }
        m_currentSettings.quickCommands.append(cmd);
    }

    // Font setting is now edited in the Appearance tab
}

void SettingsDialog::onOkButtonClicked()
{
    saveUiToSettings();
    emit settingsApplied();
    QDialog::accept();
}

void SettingsDialog::onApplyButtonClicked()
{
    saveUiToSettings();
    emit settingsApplied();
}

void SettingsDialog::onLogPathBrowseClicked()
{
    const QString kDirectory = QFileDialog::getExistingDirectory(
        this, "选择日志目录",
        m_logPathEdit->text().isEmpty() ? QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)
                                        : m_logPathEdit->text());
    if (!kDirectory.isEmpty())
    {
        m_logPathEdit->setText(kDirectory);
    }
}

void SettingsDialog::onLogViewerBrowseClicked()
{
    const QString kFilePath = QFileDialog::getOpenFileName(
        this, "选择日志查看器程序", m_externalLogViewerPathEdit->text(), "可执行文件 (*.exe);;所有文件 (*)");
    if (!kFilePath.isEmpty())
    {
        m_externalLogViewerPathEdit->setText(kFilePath);
    }
}

void SettingsDialog::onAddQuickCommandClicked()
{
    const int kRow = m_quickCommandsTable->rowCount();
    m_quickCommandsTable->insertRow(kRow);

    m_quickCommandsTable->setItem(kRow, UuidColumn, new QTableWidgetItem(QUuid::createUuid().toString()));

    auto *enabledCheck = new QCheckBox();
    enabledCheck->setChecked(true);
    m_quickCommandsTable->setCellWidget(kRow, EnabledColumn, enabledCheck);

    m_quickCommandsTable->setItem(kRow, NameColumn, new QTableWidgetItem("新命令"));
    m_quickCommandsTable->setItem(kRow, CommandColumn, new QTableWidgetItem("AT"));

    auto *formatComboBox = new QComboBox();
    formatComboBox->addItems({"ASCII", "HEX"});
    m_quickCommandsTable->setCellWidget(kRow, FormatColumn, formatComboBox);

    auto *cycleCheck = new QCheckBox();
    m_quickCommandsTable->setCellWidget(kRow, CycleColumn, cycleCheck);

    auto *intervalSpin = new QSpinBox();
    intervalSpin->setRange(kMinQuickCommandInterval, kMaxQuickCommandInterval);
    intervalSpin->setValue(kDefaultQuickCommandInterval);
    m_quickCommandsTable->setCellWidget(kRow, IntervalColumn, intervalSpin);
}

void SettingsDialog::onDeleteQuickCommandClicked()
{
    QList<QTableWidgetItem *> selectedItems = m_quickCommandsTable->selectedItems();
    if (selectedItems.isEmpty())
    {
        QMessageBox::information(this, "提示", "请先选择要删除的命令。");
        return;
    }

    const int kRow = m_quickCommandsTable->row(selectedItems.first());
    m_quickCommandsTable->removeRow(kRow);
}

void SettingsDialog::updateFontPreview()
{
    QFont font = m_fontComboBox->currentFont();
    font.setPointSize(m_fontSizeSpinBox->value());
    m_fontPreviewLabel->setFont(font);
}
