#include "InstanceManager.h"

#include <QDataStream>
#include <QDebug>
#include <QObject>
#include <QtLogging>

#include <array>
#include <cstddef>

namespace {
const int kMaxInstances = 256;
const char *const kSharedMemoryKey = "SerialT_Instance_Manager";
const char *const kSemaphoreKey = "SerialT_Instance_Semaphore";

struct SharedInstanceData
{
    std::array<bool, kMaxInstances> instance_slots;
};
} // namespace

InstanceManager::InstanceManager(QObject *parent)
    : QObject(parent),
      m_sharedMemory(kSharedMemoryKey),
      m_semaphore(kSemaphoreKey, 1, QSystemSemaphore::Open),
      m_instanceNumber(-1),
      m_isAttached(m_sharedMemory.attach())
{
    m_semaphore.acquire();

    if (!m_isAttached && !m_sharedMemory.create(sizeof(SharedInstanceData)))
    {
        // If another instance creates the memory between attach() and create()
        if (m_sharedMemory.error() == QSharedMemory::AlreadyExists)
        {
            m_isAttached = m_sharedMemory.attach();
            if (!m_isAttached)
            {
                qWarning() << "Failed to attach to shared memory:" << m_sharedMemory.errorString();
                m_semaphore.release();
                return;
            }
        }
        else
        {
            qWarning() << "Failed to create shared memory:" << m_sharedMemory.errorString();
            m_semaphore.release();
            return;
        }
    }

    m_sharedMemory.lock();

    auto *sharedData = static_cast<SharedInstanceData *>(m_sharedMemory.data());

    if (!m_isAttached)
    { // This is the first instance
        // This is the first instance, initialize the memory.
        sharedData->instance_slots.fill(false);
    }

    for (size_t i = 0; i < kMaxInstances; ++i)
    {
        if (!sharedData->instance_slots.at(i))
        {
            m_instanceNumber = static_cast<int>(i + 1);
            sharedData->instance_slots.at(i) = true;
            break;
        }
    }

    m_sharedMemory.unlock();
    m_semaphore.release();
}

InstanceManager::~InstanceManager()
{
    releaseInstanceNumber();
}

auto InstanceManager::getInstanceNumber() const -> int
{
    return m_instanceNumber;
}

void InstanceManager::releaseInstanceNumber()
{
    if (m_instanceNumber > 0 && m_isAttached)
    {
        m_semaphore.acquire();
        m_sharedMemory.lock();

        auto *sharedData = static_cast<SharedInstanceData *>(m_sharedMemory.data());
        auto index = static_cast<size_t>(m_instanceNumber - 1);
        if (index < kMaxInstances)
        {
            sharedData->instance_slots.at(index) = false;
        }

        // Check if this is the last instance
        bool isLastInstance = true;
        for (size_t i = 0; i < kMaxInstances; ++i)
        {
            if (sharedData->instance_slots.at(i))
            {
                isLastInstance = false;
                break;
            }
        }

        m_sharedMemory.unlock();

        if (isLastInstance)
        {
            m_sharedMemory.detach();
        }

        m_semaphore.release();
    }
}
