#include <QApplication>
#include <QtTest/QtTest>

#include "../../src/VT100Parser.h"

class TestVT100CommandDisable : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    void testDisableClearScreen();
    void testDisableCursorMovement();
    void testDisableSGR();

    // 新增测试方法
    void testEnableAllCommands();
    void testMixedCommandConfig();
    void testDefaultConfig();
    void testConfigPersistence();

private:
    VT100Parser *m_parser;
};

void TestVT100CommandDisable::initTestCase()
{
    if (!QApplication::instance())
    {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestVT100CommandDisable::cleanupTestCase() {}

void TestVT100CommandDisable::init()
{
    m_parser = new VT100Parser(this);
}

void TestVT100CommandDisable::cleanup()
{
    delete m_parser;
}

void TestVT100CommandDisable::testDisableClearScreen()
{
    // Arrange: Disable erase commands
    VT100CommandConfig config;
    config.enableErase = false;
    m_parser->setCommandConfig(config);

    // Act: Send a clear screen command
    m_parser->processData("\x1b[2J");

    // Assert: No erase command should be generated
    QVERIFY(m_parser->takeCommands(10).isEmpty());
}

void TestVT100CommandDisable::testDisableCursorMovement()
{
    // Arrange: Disable cursor movement commands
    VT100CommandConfig config;
    config.enableCursorMovement = false;
    m_parser->setCommandConfig(config);

    // Act: Send a cursor up command
    m_parser->processData("\x1b[5A");

    // Assert: No cursor movement command should be generated
    QVERIFY(m_parser->takeCommands(10).isEmpty());
}

void TestVT100CommandDisable::testDisableSGR()
{
    // Arrange: Disable SGR commands
    VT100CommandConfig config;
    config.enableSGR = false;
    m_parser->setCommandConfig(config);

    // Act: Send a set foreground color command
    m_parser->processData("\x1b[31m");

    // Assert: No SGR command should be generated
    QVERIFY(m_parser->takeCommands(10).isEmpty());
}

void TestVT100CommandDisable::testEnableAllCommands()
{
    // Arrange: Enable all commands (default state)
    VT100CommandConfig config;
    config.enableErase = true;
    config.enableCursorMovement = true;
    config.enableSGR = true;
    m_parser->setCommandConfig(config);

    // Act: Send various commands
    m_parser->processData("\x1b[2J");  // Clear screen
    m_parser->processData("\x1b[5A");  // Cursor up
    m_parser->processData("\x1b[31m"); // Red foreground

    // Assert: All commands should be generated
    auto commands = m_parser->takeCommands(10);
    QVERIFY(!commands.isEmpty());
    QCOMPARE(commands.size(), 3);
}

void TestVT100CommandDisable::testMixedCommandConfig()
{
    // Arrange: Enable some commands, disable others
    VT100CommandConfig config;
    config.enableErase = true;           // Enable erase
    config.enableCursorMovement = false; // Disable cursor movement
    config.enableSGR = true;             // Enable SGR
    m_parser->setCommandConfig(config);

    // Act: Send mixed commands
    m_parser->processData("\x1b[2J");  // Clear screen (should work)
    m_parser->processData("\x1b[5A");  // Cursor up (should be ignored)
    m_parser->processData("\x1b[31m"); // Red foreground (should work)

    // Assert: Only enabled commands should be generated
    auto commands = m_parser->takeCommands(10);
    QCOMPARE(commands.size(), 2); // Only erase and SGR commands
}

void TestVT100CommandDisable::testDefaultConfig()
{
    // Arrange: Use default configuration (all enabled)
    VT100CommandConfig config; // Default constructor enables all
    m_parser->setCommandConfig(config);

    // Act: Send various commands
    m_parser->processData("\x1b[2J\x1b[H\x1b[31mTest");

    // Assert: All commands should be processed
    auto commands = m_parser->takeCommands(20);
    QVERIFY(commands.size() > 0);

    // Should have erase, cursor position, SGR, and text commands
    bool hasErase = false, hasCursor = false, hasSGR = false, hasText = false;
    for (const auto &cmd : commands)
    {
        if (cmd.type == TerminalCommand::EraseInDisplay)
            hasErase = true;
        if (cmd.type == TerminalCommand::SetCursorPosition)
            hasCursor = true;
        if (cmd.type == TerminalCommand::ForegroundColorChanged)
            hasSGR = true;
        if (cmd.type == TerminalCommand::PrintableChar)
            hasText = true;
    }

    QVERIFY(hasErase);
    QVERIFY(hasCursor);
    QVERIFY(hasSGR);
    QVERIFY(hasText);
}

void TestVT100CommandDisable::testConfigPersistence()
{
    // Arrange: Set a specific configuration
    VT100CommandConfig config;
    config.enableErase = false;
    config.enableCursorMovement = true;
    config.enableSGR = false;
    m_parser->setCommandConfig(config);

    // Act: Process some data
    m_parser->processData("\x1b[2J\x1b[5A\x1b[31m");

    // Assert: Only cursor movement should work
    auto commands = m_parser->takeCommands(10);
    QCOMPARE(commands.size(), 1);
    QCOMPARE(commands[0].type, TerminalCommand::CursorUp);

    // Act: Process more data to verify config persists
    m_parser->processData("\x1b[K\x1b[3B\x1b[32m");

    // Assert: Only cursor movement should work again
    commands = m_parser->takeCommands(10);
    QCOMPARE(commands.size(), 1);
    QCOMPARE(commands[0].type, TerminalCommand::CursorDown);
}

#include "test_vt100_command_disable.moc"
QTEST_MAIN(TestVT100CommandDisable)
