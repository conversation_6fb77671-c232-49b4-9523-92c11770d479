#include <QSerialPort>
#include <QSignalSpy>
#include <QtTest/QtTest>
#include "../../src/SerialProcessor.h"
#include "../../src/core/AppTypes.h"

class TestSerialProcessor : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基础功能测试
    void testConstructor();
    void testPortConfiguration();
    void testConnectionSignals();
    void testNewLineMode();
    void testEchoMode();

    // 数据处理测试
    void testDataWriting();
    void testNewLineProcessing_data();
    void testNewLineProcessing();

    // 错误处理测试
    void testInvalidPortHandling();
    void testPortAlreadyInUse();

    // 数据统计测试
    void testDataStatistics();

    // 快捷键功能测试
    void testShortcutKeys();

private:
    SerialProcessor *m_processor;
};

void TestSerialProcessor::initTestCase()
{
    // 测试套件初始化
    qDebug() << "Starting SerialProcessor tests";
}

void TestSerialProcessor::cleanupTestCase()
{
    // 测试套件清理
    qDebug() << "SerialProcessor tests completed";
}

void TestSerialProcessor::init()
{
    // 每个测试前的初始化
    m_processor = new SerialProcessor(this);
}

void TestSerialProcessor::cleanup()
{
    // 每个测试后的清理
    if (m_processor)
    {
        m_processor->closePort();
        delete m_processor;
        m_processor = nullptr;
    }
}

void TestSerialProcessor::testConstructor()
{
    // 测试构造函数
    QVERIFY(m_processor != nullptr);
    QVERIFY(!m_processor->isOpen());
    QVERIFY(m_processor->parser() != nullptr);
}

void TestSerialProcessor::testPortConfiguration()
{
    // 测试端口配置（不实际打开端口）
    QString testPort = "COM999"; // 使用不存在的端口进行测试

    // 测试打开不存在的端口应该失败
    bool result = m_processor->openPort(testPort, 9600);
    QVERIFY(!result);
    QVERIFY(!m_processor->isOpen());
}

void TestSerialProcessor::testConnectionSignals()
{
    // 测试连接状态信号
    QSignalSpy spy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 尝试连接到不存在的端口
    m_processor->openPort("COM999", 9600);

    // 应该没有信号发出（因为连接失败）
    QCOMPARE(spy.count(), 0);
}

void TestSerialProcessor::testNewLineMode()
{
    // 测试换行模式设置
    m_processor->setNewLineMode(NewLineMode::CrLf);   // CR+LF
    m_processor->setNewLineMode(NewLineMode::Cr);   // CR
    m_processor->setNewLineMode(NewLineMode::Lf);   // LF
    // m_processor->setNewLineMode(999); // 重构后强制使用class枚举，不存在这种情况，会直接编译报错

    // 这里主要测试不会崩溃，具体行为在数据处理测试中验证
    QVERIFY(true);
}

void TestSerialProcessor::testEchoMode()
{
    // 测试回显模式
    m_processor->setEchoEnabled(true);
    m_processor->setEchoEnabled(false);

    // 基础功能测试，确保不会崩溃
    QVERIFY(true);
}

void TestSerialProcessor::testDataWriting()
{
    // 测试数据写入（端口未打开时）
    QByteArray testData = "Hello, World!";

    // 端口未打开时写入应该安全地失败
    m_processor->writeData(testData);
    m_processor->writeDataRaw(testData);

    // 测试不会崩溃
    QVERIFY(true);
}

void TestSerialProcessor::testNewLineProcessing_data()
{
    // 为参数化测试准备数据
    QTest::addColumn<int>("newLineMode");
    QTest::addColumn<QString>("expectedSuffix");

    QTest::newRow("CR+LF mode") << 0 << "\r\n";
    QTest::newRow("CR mode") << 1 << "\r";
    QTest::newRow("LF mode") << 2 << "\n";
}

void TestSerialProcessor::testNewLineProcessing()
{
    // 参数化测试换行处理
    QFETCH(int, newLineMode);
    QFETCH(QString, expectedSuffix);

    m_processor->setNewLineMode(static_cast<NewLineMode>(newLineMode));

    // 这里我们无法直接测试内部的换行处理逻辑
    // 但可以确保设置不会导致崩溃
    QVERIFY(true);
}

void TestSerialProcessor::testInvalidPortHandling()
{
    // 测试无效端口处理
    bool result1 = m_processor->openPort("", 9600);
    QVERIFY(!result1);

    bool result2 = m_processor->openPort("INVALID_PORT", 9600);
    QVERIFY(!result2);

    // 测试无效波特率
    bool result3 = m_processor->openPort("COM1", -1);
    QVERIFY(!result3);
}

void TestSerialProcessor::testPortAlreadyInUse()
{
    // 测试端口已被占用的情况
    // 这个测试需要真实的硬件环境，这里只做基础测试

    QString testPort = "COM1";
    m_processor->openPort(testPort, 9600);

    // 创建另一个处理器尝试打开同一端口
    SerialProcessor *processor2 = new SerialProcessor(this);
    bool result = processor2->openPort(testPort, 9600);
    Q_UNUSED(result) // 在测试环境中，结果可能不确定

    // 清理
    processor2->closePort();
    delete processor2;

    // 注意：这个测试的结果取决于系统和硬件
    // 在没有真实硬件的情况下，两次打开都会失败
    QVERIFY(true); // 基础的不崩溃测试
}

void TestSerialProcessor::testDataStatistics()
{
    // 测试初始状态
    QCOMPARE(m_processor->getBytesReceived(), 0);
    QCOMPARE(m_processor->getBytesSent(), 0);
    QCOMPARE(m_processor->getReceiveRate(), 0.0);
    QCOMPARE(m_processor->getSendRate(), 0.0);
    QCOMPARE(m_processor->getConnectionDuration(), 0);
    QVERIFY(!m_processor->getConnectionTime().isValid());

    // 测试重置统计功能
    m_processor->resetStatistics();
    QCOMPARE(m_processor->getBytesReceived(), 0);
    QCOMPARE(m_processor->getBytesSent(), 0);
    QCOMPARE(m_processor->getReceiveRate(), 0.0);
    QCOMPARE(m_processor->getSendRate(), 0.0);

    // 注意：由于没有真实的串口连接，我们无法测试实际的数据传输统计
    // 但我们可以验证API的存在和基本功能
    QVERIFY(true); // 基础的API存在性测试
}

void TestSerialProcessor::testShortcutKeys()
{
    // 这个测试主要验证快捷键功能不会导致程序崩溃
    // 实际的快捷键测试需要在集成测试中进行

    // 验证SerialProcessor的基本功能在快捷键操作下仍然正常
    QVERIFY(m_processor != nullptr);
    QVERIFY(!m_processor->isOpen());

    // 测试基本的API调用不会因为快捷键功能而出现问题
    QString lastError = m_processor->getLastError();
    Q_UNUSED(lastError) // 避免未使用变量警告

    QVERIFY(true); // 基础的兼容性测试
}

// 包含moc生成的代码
#include "test_serial_processor.moc"

QTEST_MAIN(TestSerialProcessor)
