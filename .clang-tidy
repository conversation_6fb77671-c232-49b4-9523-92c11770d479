---
# SerialT .clang-tidy 配置文件 (最终权威版)
#
# 本文件用于定义项目的静态代码分析规则，旨在保障代码质量、一致性和可维护性。
# 规则与 SERIALT_DEVELOPMENT_SPECIFICATION.md 开发规范保持一致。

# 可选的禁用规则说明，在必要的时候可以启用：
# -cppcoreguidelines-owning-memory: 与Qt父子对象模型冲突，Qt通过parent指针管理内存
# -misc-include-cleaner: 与Qt框架兼容性不佳，Qt设计依赖间接包含，强制直接包含过于严格
# -modernize-use-trailing-return-type: 避免强制使用尾随返回类型，保持代码风格一致性
# -readability-redundant-access-specifiers: 允许显式访问说明符，提高代码可读性
# -readability-function-cognitive-complexity: 禁用函数复杂度检查，避免对合理复杂逻辑的误报
# -modernize-raw-string-literal: 不强制使用原始字符串字面量，保持字符串风格灵活性
# -cppcoreguidelines-pro-type-const-cast: 允许必要的const_cast，特别是与C API交互时
# -cppcoreguidelines-pro-type-vararg: 允许可变参数，支持printf风格函数和Qt信号槽机制

Checks:
  # 基础策略：先禁用所有规则，然后启用需要的检查集
  - -*
  
  # 启用的检查集
  - bugprone-*              # 错误倾向检查：检测可能导致错误的代码模式
  - clang-analyzer-*         # 静态分析器：深度代码分析
  - performance-*            # 性能检查：识别性能问题
  - portability-*            # 可移植性检查：跨平台兼容性
  - readability-*            # 可读性检查：代码风格和清晰度
  - modernize-*              # 现代化检查：推荐现代C++特性
  - cppcoreguidelines-*      # C++核心指南：最佳实践
  - misc-*                   # 杂项检查：其他有用的检查
  
  # 明确禁用的规则（与项目需求冲突）
  - -cppcoreguidelines-owning-memory           # 与Qt父子对象模型冲突
  - -misc-include-cleaner                      # 与Qt框架兼容性不佳，过于严格
  - -readability-redundant-access-specifiers   # 允许显式访问说明符

HeaderFilterRegex: "^(src|tests|include)/.*"
WarningsAsErrors: "*"

CheckOptions:
  # --- 命名规范检查 (readability-identifier-naming) ---
  # 使用 clang-tidy 官方支持的关键字和前缀/后缀选项来精确定义命名风格。
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.EnumCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: readability-identifier-naming.MethodCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.ParameterCase
    value: camelBack
  - key: readability-identifier-naming.PrivateMemberPrefix
    value: "m_"
  - key: readability-identifier-naming.PrivateMemberCase
    value: camelBack
  - key: readability-identifier-naming.ConstantPrefix
    value: "k"
  - key: readability-identifier-naming.ConstantCase
    value: CamelCase
  - key: readability-identifier-naming.EnumConstantCase
    value: CamelCase

  # --- 魔法数检查配置 (readability-magic-numbers) ---
  # 允许在代码中使用一些常见的、无害的整数值。
  - key: readability-magic-numbers.IgnoredIntegerValues
    value: "0;1;2;3;4;5;8;10;16;32;64;100;255;256;1000;1024"

  # --- 标识符长度检查 (readability-identifier-length) ---
  # 强制使用有意义的变量名，但允许在特定情况下使用短名称。
  - key: readability-identifier-length.MinimumVariableNameLength
    value: 2
  - key: readability-identifier-length.IgnoredVariableNames
    value: "^[ijkxyz]$"
  - key: readability-identifier-length.MinimumParameterNameLength
    value: 2
  - key: readability-identifier-length.IgnoredParameterNames
    value: "^[ijkxyz]|id$"
