#!/usr/bin/env python3
"""
虚拟串口检测工具
自动检测系统中的虚拟串口并测试配对关系
"""

import sys
import time
import threading
import serial
import serial.tools.list_ports
from pathlib import Path

def list_all_ports():
    """列出所有串口"""
    print("🔍 扫描系统串口...")
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("❌ 未发现任何串口")
        return []
    
    print(f"📋 发现 {len(ports)} 个串口:")
    port_list = []
    for port in ports:
        print(f"  - {port.device}: {port.description}")
        if port.manufacturer:
            print(f"    制造商: {port.manufacturer}")
        if port.hwid:
            print(f"    硬件ID: {port.hwid}")
        port_list.append(port.device)
        print()
    
    return port_list

def test_port_pair(port1, port2, baudrate=115200):
    """测试两个端口是否为配对的虚拟串口"""
    print(f"🔗 测试端口对: {port1} ↔ {port2}")
    
    received_data = []
    test_message = f"TEST_{int(time.time())}"
    
    def receiver(port):
        """接收数据的线程函数"""
        try:
            with serial.Serial(port, baudrate, timeout=2) as ser:
                start_time = time.time()
                while time.time() - start_time < 3:  # 等待3秒
                    if ser.in_waiting > 0:
                        data = ser.read(ser.in_waiting)
                        received_data.append(data.decode('utf-8', errors='replace'))
                        break
                    time.sleep(0.1)
        except Exception as e:
            print(f"  ❌ {port} 接收失败: {e}")
    
    try:
        # 启动接收线程
        receiver_thread = threading.Thread(target=receiver, args=(port2,), daemon=True)
        receiver_thread.start()
        
        # 等待接收线程启动
        time.sleep(0.5)
        
        # 发送测试数据
        with serial.Serial(port1, baudrate, timeout=1) as ser:
            ser.write(test_message.encode('utf-8'))
            ser.flush()
            print(f"  📤 从 {port1} 发送: {test_message}")
        
        # 等待接收完成
        receiver_thread.join(timeout=4)
        
        # 检查结果
        if received_data and test_message in received_data[0]:
            print(f"  ✅ 配对成功！{port2} 接收到: {received_data[0]}")
            return True
        else:
            print(f"  ❌ 配对失败，未接收到数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return False

def find_virtual_port_pairs():
    """查找虚拟串口对"""
    ports = list_all_ports()
    
    if len(ports) < 2:
        print("❌ 端口数量不足，无法进行配对测试")
        return []
    
    print("🔍 开始配对测试...")
    pairs = []
    
    # 测试所有可能的端口对
    for i, port1 in enumerate(ports):
        for j, port2 in enumerate(ports):
            if i >= j:  # 避免重复测试
                continue
            
            print(f"\n--- 测试 {port1} 和 {port2} ---")
            
            # 双向测试
            test1 = test_port_pair(port1, port2)
            time.sleep(0.5)
            test2 = test_port_pair(port2, port1)
            
            if test1 and test2:
                print(f"🎉 发现虚拟串口对: {port1} ↔ {port2}")
                pairs.append((port1, port2))
            elif test1 or test2:
                print(f"⚠️ 单向通信: {port1} → {port2}" if test1 else f"⚠️ 单向通信: {port2} → {port1}")
    
    return pairs

def suggest_configuration(pairs):
    """建议配置"""
    if not pairs:
        print("\n❌ 未发现虚拟串口对")
        print("\n💡 建议:")
        print("1. 安装虚拟串口软件:")
        print("   - com0com: https://sourceforge.net/projects/com0com/")
        print("   - Virtual Serial Port Driver")
        print("2. 创建虚拟串口对，例如 COM30 ↔ COM31")
        print("3. 重新运行此检测工具")
        return
    
    print(f"\n✅ 发现 {len(pairs)} 个虚拟串口对:")
    for i, (port1, port2) in enumerate(pairs, 1):
        print(f"  {i}. {port1} ↔ {port2}")
    
    print("\n📝 配置建议:")
    
    # 如果找到了COM30和COM31的组合
    target_pair = None
    for port1, port2 in pairs:
        if (port1 == 'COM30' and port2 == 'COM31') or (port1 == 'COM31' and port2 == 'COM30'):
            target_pair = (port1, port2)
            break
    
    if target_pair:
        print("🎯 完美！发现了目标端口对 COM30 ↔ COM31")
        print("当前配置无需修改，可以直接进行测试")
    else:
        # 建议使用第一个发现的端口对
        port1, port2 = pairs[0]
        print(f"💡 建议修改配置文件 tests/integration/config.py:")
        print(f"   SERIALT_PORT = '{port1}'")
        print(f"   TEST_TOOL_PORT = '{port2}'")
        print(f"   然后在SerialT中连接到 {port1}")

def test_specific_ports(port1, port2):
    """测试指定的端口对"""
    print(f"🎯 测试指定端口对: {port1} ↔ {port2}")
    
    # 检查端口是否存在
    available_ports = [port.device for port in serial.tools.list_ports.comports()]
    
    for port in [port1, port2]:
        if port not in available_ports:
            try:
                # 尝试打开端口，即使不在列表中
                with serial.Serial(port, 115200, timeout=0.1):
                    print(f"✅ {port} 可访问（虚拟端口）")
            except Exception as e:
                print(f"❌ {port} 不可访问: {e}")
                return False
    
    # 测试配对
    result1 = test_port_pair(port1, port2)
    time.sleep(0.5)
    result2 = test_port_pair(port2, port1)
    
    if result1 and result2:
        print(f"🎉 {port1} ↔ {port2} 配对成功！")
        return True
    else:
        print(f"❌ {port1} ↔ {port2} 配对失败")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='虚拟串口检测工具')
    parser.add_argument('--test-pair', nargs=2, metavar=('PORT1', 'PORT2'), 
                       help='测试指定的端口对，例如: --test-pair COM30 COM31')
    parser.add_argument('--scan-all', action='store_true', help='扫描所有可能的端口对')
    
    args = parser.parse_args()
    
    print("🔍 SerialT 虚拟串口检测工具")
    print("=" * 50)
    
    if args.test_pair:
        # 测试指定端口对
        port1, port2 = args.test_pair
        success = test_specific_ports(port1, port2)
        return success
    
    elif args.scan_all:
        # 扫描所有端口对
        pairs = find_virtual_port_pairs()
        suggest_configuration(pairs)
        return len(pairs) > 0
    
    else:
        # 默认行为：先列出端口，然后测试COM30和COM31
        list_all_ports()
        
        print("\n🎯 测试目标端口对 COM30 ↔ COM31...")
        success = test_specific_ports('COM30', 'COM31')
        
        if not success:
            print("\n🔍 自动扫描其他可能的端口对...")
            pairs = find_virtual_port_pairs()
            suggest_configuration(pairs)
            return len(pairs) > 0
        
        return success

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序异常: {e}")
        sys.exit(1)
