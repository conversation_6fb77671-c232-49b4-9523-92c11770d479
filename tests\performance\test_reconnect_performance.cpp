#include <QElapsedTimer>
#include <QSignalSpy>
#include <QTest>

#include "../../src/SerialProcessor.h"

class TestReconnectPerformance : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 性能测试
    void testStateCheckPerformance();
    void testSignalConnectionGuardPerformance();
    void testConfigurationPerformance();
    void testMemoryUsage();

private:
    SerialProcessor *m_processor;
};

void TestReconnectPerformance::initTestCase()
{
    qDebug() << "Starting performance tests for auto-reconnect functionality";
}

void TestReconnectPerformance::cleanupTestCase()
{
    qDebug() << "Performance tests completed";
}

void TestReconnectPerformance::init()
{
    m_processor = new SerialProcessor(this);
}

void TestReconnectPerformance::cleanup()
{
    if (m_processor)
    {
        delete m_processor;
        m_processor = nullptr;
    }
}

void TestReconnectPerformance::testStateCheckPerformance()
{
    // 测试状态检查方法的性能
    const int iterations = 1000000;

    QElapsedTimer timer;
    timer.start();

    // 测试 isReconnecting() 方法的性能
    for (int i = 0; i < iterations; ++i)
    {
        volatile bool result = m_processor->isReconnecting();
        Q_UNUSED(result);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "isReconnecting() method:" << iterations << "calls in" << elapsed << "ms";
    qDebug() << "Average time per call:" << (double)elapsed / iterations * 1000 << "microseconds";

    // 性能应该非常好，每次调用应该在纳秒级别
    QVERIFY(elapsed < 100); // 100万次调用应该在100ms内完成
}

void TestReconnectPerformance::testSignalConnectionGuardPerformance()
{
    // 测试信号连接保护类的性能开销
    const int iterations = 10000;

    QElapsedTimer timer;
    timer.start();

    // 模拟SignalConnectionGuard的使用场景
    for (int i = 0; i < iterations; ++i)
    {
        // 注意：这里不能直接测试SignalConnectionGuard，因为它是私有类
        // 我们测试相关的操作性能
        m_processor->connectionState();
        m_processor->isReconnecting();
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "State operations:" << iterations << "iterations in" << elapsed << "ms";
    qDebug() << "Average time per iteration:" << (double)elapsed / iterations << "ms";

    // 状态操作应该非常快
    QVERIFY(elapsed < 50); // 1万次操作应该在50ms内完成
}

void TestReconnectPerformance::testConfigurationPerformance()
{
    // 测试配置方法的性能
    const int iterations = 100000;

    QElapsedTimer timer;
    timer.start();

    for (int i = 0; i < iterations; ++i)
    {
        m_processor->setAutoReconnectEnabled(i % 2 == 0);
        m_processor->setMaxReconnectAttempts(5);
        m_processor->setReconnectInterval(2000);
        m_processor->setReconnectIntervalMultiplier(1.5);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "Configuration methods:" << iterations << "iterations in" << elapsed << "ms";
    qDebug() << "Average time per iteration:" << (double)elapsed / iterations << "ms";

    // 配置操作应该很快
    QVERIFY(elapsed < 200); // 10万次配置操作应该在200ms内完成
}

void TestReconnectPerformance::testMemoryUsage()
{
    // 测试内存使用情况
    // 这个测试主要是确保我们的优化没有引入内存泄漏

    const int iterations = 1000;
    QList<SerialProcessor *> processors;

    // 创建多个SerialProcessor实例
    for (int i = 0; i < iterations; ++i)
    {
        SerialProcessor *processor = new SerialProcessor();

        // 配置自动重连
        processor->setAutoReconnectEnabled(true);
        processor->setMaxReconnectAttempts(3);
        processor->setReconnectInterval(1000);
        processor->setReconnectIntervalMultiplier(1.2);

        processors.append(processor);
    }

    // 清理
    for (SerialProcessor *processor : processors)
    {
        delete processor;
    }
    processors.clear();

    // 如果能执行到这里没有崩溃，说明内存管理是正确的
    QVERIFY(true);

    qDebug() << "Memory test completed: created and destroyed" << iterations << "SerialProcessor instances";
}

// 基准测试辅助函数
void benchmarkFunction(const QString &name, std::function<void()> func, int iterations = 100000)
{
    QElapsedTimer timer;
    timer.start();

    for (int i = 0; i < iterations; ++i)
    {
        func();
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << name << ":" << iterations << "iterations in" << elapsed << "ms";
    qDebug() << "Average time per call:" << (double)elapsed / iterations * 1000 << "microseconds";
}

QTEST_MAIN(TestReconnectPerformance)
#include "test_reconnect_performance.moc"

/*
预期性能基准:

1. isReconnecting() 方法:
   - 100万次调用应该在100ms内完成
   - 平均每次调用 < 0.1微秒

2. 状态操作:
   - 1万次迭代应该在50ms内完成
   - 平均每次迭代 < 0.005ms

3. 配置方法:
   - 10万次配置操作应该在200ms内完成
   - 平均每次操作 < 0.002ms

4. 内存使用:
   - 创建和销毁1000个实例应该没有内存泄漏
   - 内存使用应该稳定

这些基准确保我们的优化没有引入性能回归，
并且新的实现在性能上是可接受的。
*/