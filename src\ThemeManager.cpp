#include "ThemeManager.h"

#include "core/SettingsConstants.h"

#include <QApplication>
#include <QSettings>
#include <QStyleHints>

namespace {
/**
 * @brief 内部嵌套的命名空间，用于对常量进行逻辑分组。
 *
 * 尽管所有常量已通过匿名命名空间与外界隔离，但为了提高内部代码的可读性和
 * 可维护性，我们进一步使用嵌套的命名空间（如 Colors, Style）将它们
 * 按类别（颜色定义、样式尺寸等）组织起来。
 * 这使得代码意图更加清晰，例如 `Colors::kLightPrimary` 比一个
 * 扁平的 `kLightPrimary` 提供了更强的上下文。
 */
// =================================================================================
// == 颜色定义 (Color Definitions)
// =================================================================================
namespace Colors {
// --- 核心调色板 ---
constexpr QColor kColorWhite(255, 255, 255);

// --- 亮色主题：仿 Windows 11 风格 ---
constexpr QColor kLightBackground(255, 255, 255);
constexpr QColor kLightSurface(249, 249, 249);
constexpr QColor kLightSurfaceVariant(243, 243, 243);
constexpr QColor kLightOnBackground(32, 32, 32);
constexpr QColor kLightOnSurface(32, 32, 32);
constexpr QColor kLightOnSurfaceVariant(96, 96, 96);
constexpr QColor kLightDisabled(161, 161, 161);
constexpr QColor kLightPrimary(0, 120, 212);
constexpr QColor kLightPrimaryVariant(0, 99, 177);
constexpr QColor kLightSecondary(96, 165, 250);
constexpr QColor kLightSuccess(16, 124, 16);
constexpr QColor kLightWarning(157, 93, 0);
constexpr QColor kLightError(196, 43, 28);
constexpr QColor kLightBorder(225, 225, 225);
constexpr QColor kLightDivider(243, 243, 243);
constexpr QColor kLightOutline(161, 161, 161);
constexpr QColor kLightTerminalSelection(0, 120, 212, 51); // 半透明蓝色选择
constexpr QColor kLightSearchHighlight(255, 235, 59, 128); // 半透明黄色
constexpr QColor kLightSearchActive(0, 120, 212, 128);     // 半透明蓝色

// --- 暗色主题：仿 Windows 11 风格 ---
constexpr QColor kDarkBackground(32, 32, 32);
constexpr QColor kDarkSurface(43, 43, 43);
constexpr QColor kDarkSurfaceVariant(54, 54, 54);
constexpr QColor kDarkOnBackground(255, 255, 255);
constexpr QColor kDarkOnSurface(255, 255, 255);
constexpr QColor kDarkOnSurfaceVariant(204, 204, 204);
constexpr QColor kDarkDisabled(109, 109, 109);
constexpr QColor kDarkPrimary(96, 165, 250);
constexpr QColor kDarkPrimaryVariant(59, 130, 246);
constexpr QColor kDarkOnSecondary(32, 32, 32);
constexpr QColor kDarkSecondary(147, 197, 253);
constexpr QColor kDarkSuccess(34, 197, 94);
constexpr QColor kDarkWarning(251, 146, 60);
constexpr QColor kDarkError(239, 68, 68);
constexpr QColor kDarkBorder(64, 64, 64);
constexpr QColor kDarkDivider(54, 54, 54);
constexpr QColor kDarkOutline(109, 109, 109);
constexpr QColor kDarkTerminalBackground(12, 12, 12);
constexpr QColor kDarkTerminalForeground(204, 204, 204);
constexpr QColor kDarkTerminalSelection(96, 165, 250, 51); // 半透明蓝色选择
constexpr QColor kDarkSearchHighlight(251, 191, 36, 128);  // 半透明黄色
constexpr QColor kDarkSearchActive(96, 165, 250, 128);     // 半透明蓝色

} // namespace Colors

// =================================================================================
// == 样式尺寸常量 (Style Value AppConstants)
// =================================================================================
namespace Style {
// --- 边框与圆角 ---
constexpr int kBorderWidthThin = 1;
constexpr int kBorderRadius = 4;
constexpr int kBorderRadiusSmall = 3;
constexpr int kBorderRadiusLarge = 6;
constexpr int kArrowBorderWidth = 4;
constexpr int kSmallArrowBorderWidth = 3;

// --- 内外边距 ---
constexpr int kPadding4 = 4;
constexpr int kPadding6 = 6;
constexpr int kPadding8 = 8;
constexpr int kPadding12 = 12;
constexpr int kPadding16 = 16;

// --- 尺寸 ---
constexpr int kButtonMinWidth = 60;
constexpr int kButtonMaxHeight = 28;
constexpr int kCompactButtonMaxHeight = 24;
constexpr int kComboBoxMinWidth = 50;
constexpr int kDropDownWidth = 20;

// --- 字重 ---
constexpr int kFontWeightNormal = 400;
constexpr int kFontWeightMedium = 500;

} // namespace Style
} // namespace

ThemeManager::ThemeManager(QObject *parent)
    : QObject(parent), m_currentTheme(ThemeType::Light), m_followSystemTheme(true)
{
    initializeThemes();
    loadThemeSettings();

    // 连接系统主题变化信号
    connect(QApplication::styleHints(), &QStyleHints::colorSchemeChanged, this, &ThemeManager::onSystemThemeChanged);
}

void ThemeManager::setTheme(ThemeType theme)
{
    if (m_currentTheme == theme)
    {
        return;
    }

    m_currentTheme = theme;
    updateColors();
    saveThemeSettings();

    emit themeChanged(theme);
    emit colorsChanged();
}

void ThemeManager::toggleTheme()
{
    const ThemeType kNewTheme = (m_currentTheme == ThemeType::Light) ? ThemeType::Dark : ThemeType::Light;
    setTheme(kNewTheme);
}

auto ThemeManager::color(const QString &colorName) const -> QColor
{
    // 提供通过字符串名称访问颜色的便捷方法
    if (colorName == kColorBackground)
    {
        return m_colors.background;
    }
    if (colorName == kColorSurface)
    {
        return m_colors.surface;
    }
    if (colorName == kColorPrimary)
    {
        return m_colors.primary;
    }
    if (colorName == kColorOnBackground)
    {
        return m_colors.onBackground;
    }
    if (colorName == kColorOnSurface)
    {
        return m_colors.onSurface;
    }
    if (colorName == kColorBorder)
    {
        return m_colors.border;
    }
    if (colorName == kColorTerminalBackground)
    {
        return m_colors.terminalBackground;
    }
    if (colorName == kColorTerminalForeground)
    {
        return m_colors.terminalForeground;
    }

    return {}; // 返回无效颜色作为默认值
}

auto ThemeManager::detectSystemTheme() -> ThemeType
{
    // 检测系统主题
    const auto kColorScheme = QApplication::styleHints()->colorScheme();
    return (kColorScheme == Qt::ColorScheme::Dark) ? ThemeType::Dark : ThemeType::Light;
}

void ThemeManager::followSystemTheme(bool follow)
{
    m_followSystemTheme = follow;
    if (follow)
    {
        setTheme(detectSystemTheme());
    }
    saveThemeSettings();
}

void ThemeManager::onSystemThemeChanged()
{
    if (m_followSystemTheme)
    {
        setTheme(detectSystemTheme());
    }
}

void ThemeManager::saveThemeSettings()
{
    QSettings settings;
    settings.beginGroup(App::Settings::Keys::kThemeGroup);
    settings.setValue(App::Settings::Keys::kCurrentTheme, static_cast<int>(m_currentTheme));
    settings.setValue(App::Settings::Keys::kFollowSystemTheme, m_followSystemTheme);
    settings.endGroup();
}

void ThemeManager::loadThemeSettings()
{
    QSettings settings;
    settings.beginGroup(App::Settings::Keys::kThemeGroup);

    m_followSystemTheme = settings.value(App::Settings::Keys::kFollowSystemTheme, true).toBool();

    if (m_followSystemTheme)
    {
        m_currentTheme = detectSystemTheme();
    }
    else
    {
        const int kThemeValue =
            settings.value(App::Settings::Keys::kCurrentTheme, static_cast<int>(ThemeType::Light)).toInt();
        m_currentTheme = static_cast<ThemeType>(kThemeValue);
    }

    settings.endGroup();
    updateColors();
}

void ThemeManager::initializeThemes()
{
    updateColors();
}

void ThemeManager::updateColors()
{
    switch (m_currentTheme)
    {
        case ThemeType::Light:
            m_colors = createLightTheme();
            break;
        case ThemeType::Dark:
            m_colors = createDarkTheme();
            break;
    }
}

auto ThemeManager::createLightTheme() -> ThemeColors
{
    ThemeColors colors;
    // === 基础颜色（Windows 11 亮色主题） ===
    colors.background = Colors::kLightBackground;
    colors.surface = Colors::kLightSurface;
    colors.surfaceVariant = Colors::kLightSurfaceVariant;

    // === 文本颜色 ===
    colors.onBackground = Colors::kLightOnBackground;
    colors.onSurface = Colors::kLightOnSurface;
    colors.onSurfaceVariant = Colors::kLightOnSurfaceVariant;
    colors.disabled = Colors::kLightDisabled;

    // === 蓝色强调色系统 ===
    colors.primary = Colors::kLightPrimary;
    colors.primaryVariant = Colors::kLightPrimaryVariant;
    colors.onPrimary = Colors::kColorWhite;
    colors.secondary = Colors::kLightSecondary;
    colors.onSecondary = Colors::kColorWhite;

    // === 状态颜色 ===
    colors.success = Colors::kLightSuccess;
    colors.warning = Colors::kLightWarning;
    colors.error = Colors::kLightError;
    colors.info = Colors::kLightPrimary;

    // === 边框和分割线 ===
    colors.border = Colors::kLightBorder;
    colors.divider = Colors::kLightDivider;
    colors.outline = Colors::kLightOutline;

    // === 终端特定颜色 ===
    colors.terminalBackground = Colors::kColorWhite;
    colors.terminalForeground = Colors::kLightOnBackground;
    colors.terminalCursor = Colors::kLightPrimary;
    colors.terminalSelection = Colors::kLightTerminalSelection;

    // === 搜索高亮 ===
    colors.searchHighlight = Colors::kLightSearchHighlight;
    colors.searchActive = Colors::kLightSearchActive;
    return colors;
}

auto ThemeManager::createDarkTheme() -> ThemeColors
{
    ThemeColors colors;
    // === 基础颜色（Windows 11 暗色主题） ===
    colors.background = Colors::kDarkBackground;
    colors.surface = Colors::kDarkSurface;
    colors.surfaceVariant = Colors::kDarkSurfaceVariant;

    // === 文本颜色 ===
    colors.onBackground = Colors::kDarkOnBackground;
    colors.onSurface = Colors::kDarkOnSurface;
    colors.onSurfaceVariant = Colors::kDarkOnSurfaceVariant;
    colors.disabled = Colors::kDarkDisabled;

    // === 蓝色强调色系统 ===
    colors.primary = Colors::kDarkPrimary;
    colors.primaryVariant = Colors::kDarkPrimaryVariant;
    colors.onPrimary = Colors::kColorWhite;
    colors.secondary = Colors::kDarkSecondary;
    colors.onSecondary = Colors::kDarkOnSecondary;

    // === 状态颜色 ===
    colors.success = Colors::kDarkSuccess;
    colors.warning = Colors::kDarkWarning;
    colors.error = Colors::kDarkError;
    colors.info = Colors::kDarkPrimary;

    // === 边框和分割线 ===
    colors.border = Colors::kDarkBorder;
    colors.divider = Colors::kDarkDivider;
    colors.outline = Colors::kDarkOutline;

    // === 终端特定颜色 ===
    colors.terminalBackground = Colors::kDarkTerminalBackground;
    colors.terminalForeground = Colors::kDarkTerminalForeground;
    colors.terminalCursor = Colors::kDarkPrimary;
    colors.terminalSelection = Colors::kDarkTerminalSelection;

    // === 搜索高亮 ===
    colors.searchHighlight = Colors::kDarkSearchHighlight;
    colors.searchActive = Colors::kDarkSearchActive;
    return colors;
}

auto ThemeManager::generateStyleSheet() const -> QString
{
    // 注意: 这是用于主窗口、对话框等顶层组件的基础样式表。
    // 更具体的控件样式由其他 generate 方法生成。
    return QString(R"(
        QMainWindow, QDialog {
            background-color: %1;
            color: %2;
        }
    )")
        .arg(m_colors.background.name())    // %1 - 主背景
        .arg(m_colors.onBackground.name()); // %2 - 主文本
}

auto ThemeManager::generateButtonStyle() const -> QString
{
    // 标准按钮样式
    return QString(R"(
        QPushButton {
            background-color: %1;  /* surface */
            color: %2;             /* onSurface */
            border: %3px solid %4; /* 1px, outline */
            border-radius: %5px;
            padding: %6px %7px;
            font-weight: %8;
            min-width: %9px;
            max-height: %10px;
        }
        QPushButton:hover {
            background-color: %11; /* surfaceVariant */
            border-color: %12;     /* primary */
        }
        QPushButton:pressed {
            background-color: %12; /* primary */
            border-color: %12;
            color: %13;            /* onPrimary */
        }
        QPushButton:disabled {
            background-color: %11; /* surfaceVariant */
            color: %14;            /* disabled */
            border-color: %11;
        }
        /* 主按钮样式 */
        QPushButton[primary="true"] {
            background-color: %12; /* primary */
            color: %13;            /* onPrimary */
            border-color: %12;
        }
        QPushButton[primary="true"]:hover {
            background-color: %15; /* primaryVariant */
            border-color: %15;
        }
        QPushButton[primary="true"]:pressed {
            background-color: %16; /* another variant could be defined */
            border-color: %16;
        }
    )")
        .arg(m_colors.surface.name())         // %1
        .arg(m_colors.onSurface.name())       // %2
        .arg(Style::kBorderWidthThin)         // %3
        .arg(m_colors.outline.name())         // %4
        .arg(Style::kBorderRadius)            // %5
        .arg(Style::kPadding4)                // %6
        .arg(Style::kPadding12)               // %7
        .arg(Style::kFontWeightMedium)        // %8
        .arg(Style::kButtonMinWidth)          // %9
        .arg(Style::kButtonMaxHeight)         // %10
        .arg(m_colors.surfaceVariant.name())  // %11
        .arg(m_colors.primary.name())         // %12
        .arg(m_colors.onPrimary.name())       // %13
        .arg(m_colors.disabled.name())        // %14
        .arg(m_colors.primaryVariant.name())  // %15
        .arg(m_colors.primaryVariant.name()); // %16
}

auto ThemeManager::generateComboBoxStyle() const -> QString
{
    // 下拉框样式
    return QString(R"(
        QComboBox {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: %5px;
            padding: %6px %7px;
            min-width: %8px;
        }
        QComboBox:hover, QComboBox:focus {
            border-color: %9;
            outline: none;
        }
        QComboBox::drop-down {
            border: none;
            width: %10px;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: %11px solid transparent;
            border-right: %11px solid transparent;
            border-top: %11px solid %2;
            margin-right: %11px;
        }
        QComboBox QAbstractItemView {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: %5px;
            selection-background-color: %12;
        }
    )")
        .arg(m_colors.surface.name())         // %1
        .arg(m_colors.onSurface.name())       // %2
        .arg(Style::kBorderWidthThin)         // %3
        .arg(m_colors.outline.name())         // %4
        .arg(Style::kBorderRadius)            // %5
        .arg(Style::kPadding4)                // %6
        .arg(Style::kPadding8)                // %7
        .arg(Style::kComboBoxMinWidth)        // %8
        .arg(m_colors.primary.name())         // %9
        .arg(Style::kDropDownWidth)           // %10
        .arg(Style::kArrowBorderWidth)        // %11
        .arg(m_colors.surfaceVariant.name()); // %12
}

auto ThemeManager::generateLineEditStyle() const -> QString
{
    // 单行输入框样式
    return QString(R"(
        QLineEdit {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: %5px;
            padding: %6px %7px;
        }
        QLineEdit:hover, QLineEdit:focus {
            border-color: %8;
            outline: none;
        }
        QLineEdit:disabled {
            background-color: %9;
            color: %10;
            border-color: %9;
        }
        QLineEdit[readOnly="true"] {
            background-color: %9;
            color: %2;
        }
    )")
        .arg(m_colors.surface.name())        // %1
        .arg(m_colors.onSurface.name())      // %2
        .arg(Style::kBorderWidthThin)        // %3
        .arg(m_colors.outline.name())        // %4
        .arg(Style::kBorderRadius)           // %5
        .arg(Style::kPadding6)               // %6
        .arg(Style::kPadding12)              // %7
        .arg(m_colors.primary.name())        // %8
        .arg(m_colors.surfaceVariant.name()) // %9
        .arg(m_colors.disabled.name());      // %10
}

auto ThemeManager::generateMenuStyle() const -> QString
{
    // 菜单样式
    return QString(R"(
        /* === 菜单栏样式 === */
        QMenuBar {
            background-color: %1;
            color: %2;
            border-bottom: %3px solid %4;
            padding: 2px;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 6px 12px;
            border-radius: %7px;
        }
        QMenuBar::item:selected {
            background-color: %8;
        }
        QMenuBar::item:pressed {
            background-color: %10;
        }
        QMenu {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: %5px;
            padding: %6px;
        }
        QMenu::item {
            background-color: transparent;
            padding: 6px 24px 6px 8px;
            border-radius: %7px;
            margin: 1px;
        }
        QMenu::item:selected {
            background-color: %8;
        }
        QMenu::item:disabled {
            color: %9;
        }
        QMenu::separator {
            height: %3px;
            background-color: %4;
            margin: 4px 8px;
        }
         QMenu::indicator {
            width: 16px;
            height: 16px;
            margin-left: 4px;
        }
        QMenu::indicator:checked {
            background-color: %10;
            border-radius: 2px;
        }
    )")
        .arg(m_colors.surface.name())        // %1
        .arg(m_colors.onSurface.name())      // %2
        .arg(Style::kBorderWidthThin)        // %3
        .arg(m_colors.border.name())         // %4
        .arg(Style::kBorderRadiusLarge)      // %5
        .arg(Style::kPadding4)               // %6
        .arg(Style::kBorderRadius)           // %7
        .arg(m_colors.surfaceVariant.name()) // %8
        .arg(m_colors.disabled.name())       // %9
        .arg(m_colors.primary.name());       // %10
}

auto ThemeManager::generateToolBarStyle() const -> QString
{
    // 工具栏样式
    return QString(R"(
        QToolBar {
            background-color: %1;
            border: none;
            spacing: 2px;
            padding: %2px;
        }
        QToolBar QLabel {
            color: %2;
            background-color: transparent;
            padding: 2px 4px;
            margin: 1px 2px;
        }
        QToolBar QComboBox {
            min-width: 60px;
            max-width: 120px;
            padding: 2px 4px;
            margin: 1px;
            min-height: 20px;
            max-height: %6px;
        }
        QToolBar QToolButton {
            background-color: transparent;
            color: %3;
            border: 1px solid transparent;
            border-radius: %4px;
            padding: %5px;
            margin: 1px;
        }
        QToolBar QToolButton:hover {
            background-color: %6;
        }
        QToolBar QToolButton:pressed, QToolBar QToolButton:checked {
            background-color: %7;
            color: %8;
        }
        QToolBar QToolButton:checked {
             border: 1px solid %7;
        }
        QToolBar::separator {
            background-color: %9;
            width: 1px;
            margin: 4px 2px;
        }
    )")
        .arg(m_colors.surface.name())        // %1
        .arg(Style::kPadding4)               // %2
        .arg(m_colors.onSurface.name())      // %3
        .arg(Style::kBorderRadius)           // %4
        .arg(Style::kPadding6)               // %5
        .arg(m_colors.surfaceVariant.name()) // %6
        .arg(m_colors.primary.name())        // %7
        .arg(m_colors.onPrimary.name())      // %8
        .arg(m_colors.border.name());        // %9
}

auto ThemeManager::generateStatusBarStyle() const -> QString
{
    // 状态栏样式
    return QString(R"(
        QStatusBar {
            background-color: %1;
            color: %2;
            border-top: %3px solid %4;
            padding: 2px;
        }
        QStatusBar::item {
            border: none;
            padding: 0 %5px;
        }
        QStatusBar QLabel {
            color: %2;
        }
    )")
        .arg(m_colors.surface.name())   // %1
        .arg(m_colors.onSurface.name()) // %2
        .arg(Style::kBorderWidthThin)   // %3
        .arg(m_colors.border.name())    // %4
        .arg(Style::kPadding8);         // %5
}

auto ThemeManager::generateDialogStyle() const -> QString
{
    // 对话框及内部控件的通用样式
    return QString(R"(
        QDialog, QGroupBox, QTabWidget, QWidget {
            background-color: %1;
            color: %2;
        }
        QTabWidget::pane {
            border: %3px solid %4;
            border-radius: %5px;
            background-color: %1;
        }
        QTabWidget::tab-bar {
            alignment: left;
        }
        QTabBar::tab {
            background-color: %6;
            color: %2;
            border: %3px solid %4;
            border-bottom: none;
            border-top-left-radius: %5px;
            border-top-right-radius: %5px;
            padding: %7px %8px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: %1;
            border-bottom-color: %1;
        }
        QTabBar::tab:hover:!selected {
            background-color: %9;
        }
        QGroupBox {
            border: %3px solid %4;
            border-radius: %5px;
            margin-top: %7px;
            padding-top: %7px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: %7px;
            padding: 0 %10px;
        }
        QLabel {
            color: %2;
            background-color: transparent;
        }
        QCheckBox {
            color: %2;
            spacing: %7px;
        }
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: %3px solid %4;
            border-radius: 2px;
            background-color: %1;
        }
        QCheckBox::indicator:checked {
            background-color: %11;
            border-color: %11;
            image: none;
        }
        QSpinBox, QDoubleSpinBox, QFontComboBox {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: %5px;
            padding: %10px %7px;
        }
        QSpinBox:focus, QDoubleSpinBox:focus, QFontComboBox:focus {
            border-color: %11;
        }
        QSpinBox::up-button, QDoubleSpinBox::up-button, QSpinBox::down-button, QDoubleSpinBox::down-button {
            background-color: %6;
            border: %3px solid %4;
            border-radius: 2px;
            width: 16px;
            margin: 1px;
        }
        QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover, QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
            background-color: %9;
        }
        QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed, QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
            background-color: %11;
        }
        QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
            border-left: %12px solid transparent;
            border-right: %12px solid transparent;
            border-bottom: %12px solid %2;
            width: 0px;
            height: 0px;
        }
        QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
            border-left: %12px solid transparent;
            border-right: %12px solid transparent;
            border-top: %12px solid %2;
            width: 0px;
            height: 0px;
        }
        QFrame[frameShape="4"] {
            border: %3px solid %4;
            border-radius: %5px;
        }
        QTableWidget {
            border: %3px solid %4;
            border-radius: %5px;
            gridline-color: %4;
            selection-background-color: %9;
        }
        QTableWidget::item {
            padding: %13px %7px;
        }
        QTableWidget::item:selected {
            background-color: %9;
            color: %2;
        }
        QHeaderView::section {
            background-color: %6;
            border: %3px solid %4;
            padding: %13px %7px;
            font-weight: bold;
        }
        /* 表格内的SpinBox样式 */
        QTableWidget QSpinBox, QTableWidget QDoubleSpinBox {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: 3px;
            padding: 3px 6px;
            min-width: 50px;
            max-width: 70px;
            min-height: 10px;
            max-height: 24px;
        }
        QTableWidget QSpinBox:focus, QTableWidget QDoubleSpinBox:focus {
            border-color: %11;
        }
        QTableWidget QSpinBox::up-button, QTableWidget QDoubleSpinBox::up-button,
        QTableWidget QSpinBox::down-button, QTableWidget QDoubleSpinBox::down-button {
            background-color: %6;
            border: %3px solid %4;
            border-radius: 2px;
            width: 12px;
            margin: 1px;
        }
        QTableWidget QSpinBox::up-button:hover, QTableWidget QDoubleSpinBox::up-button:hover,
        QTableWidget QSpinBox::down-button:hover, QTableWidget QDoubleSpinBox::down-button:hover {
            background-color: %9;
        }
        QTableWidget QSpinBox::up-arrow, QTableWidget QDoubleSpinBox::up-arrow,
        QTableWidget QSpinBox::down-arrow, QTableWidget QDoubleSpinBox::down-arrow {
            border-left: 2px solid transparent;
            border-right: 2px solid transparent;
            width: 0px;
            height: 0px;
        }
        QTableWidget QSpinBox::up-arrow, QTableWidget QDoubleSpinBox::up-arrow {
            border-bottom: 2px solid %2;
        }
        QTableWidget QSpinBox::down-arrow, QTableWidget QDoubleSpinBox::down-arrow {
            border-top: 2px solid %2;
        }

        /* 表格内的ComboBox样式 */
        QTableWidget QComboBox {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: 3px;
            padding: 3px 6px;
            min-width: 50px;
            max-width: 80px;
            min-height: 10px;
            max-height: 20px;
        }
        QTableWidget QComboBox:focus {
            border-color: %11;
        }
        QTableWidget QComboBox::drop-down {
            border: none;
            width: 16px;
        }
        QTableWidget QComboBox::down-arrow {
            border-left: 3px solid transparent;
            border-right: 3px solid transparent;
            border-top: 3px solid %2;
            width: 0px;
            height: 0px;
        }
    )")
        .arg(m_colors.background.name())     // %1
        .arg(m_colors.onBackground.name())   // %2
        .arg(Style::kBorderWidthThin)        // %3
        .arg(m_colors.border.name())         // %4
        .arg(Style::kBorderRadius)           // %5
        .arg(m_colors.surface.name())        // %6
        .arg(Style::kPadding8)               // %7
        .arg(Style::kPadding16)              // %8
        .arg(m_colors.surfaceVariant.name()) // %9
        .arg(Style::kPadding4)               // %10
        .arg(m_colors.primary.name())        // %11
        .arg(Style::kSmallArrowBorderWidth)  // %12
        .arg(Style::kPadding6);              // %13
}

auto ThemeManager::generateCompactButtonStyle() const -> QString
{
    // 紧凑型按钮样式，具有更小的内外边距和正常的字重
    return QString(R"(
        QPushButton {
            background-color: %1;
            color: %2;
            border: %3px solid %4;
            border-radius: %5px;
            padding: %6px %7px;
            font-weight: %8;
            min-width: %9px;
            max-height: %10px;
        }
        QPushButton:hover {
            background-color: %11;
            border-color: %12;
        }
        QPushButton:pressed {
            background-color: %12;
            border-color: %12;
            color: %13;
        }
        QPushButton:disabled {
            background-color: %11;
            color: %14;
            border-color: %11;
        }
    )")
        .arg(m_colors.surface.name())        // %1
        .arg(m_colors.onSurface.name())      // %2
        .arg(Style::kBorderWidthThin)        // %3
        .arg(m_colors.outline.name())        // %4
        .arg(Style::kBorderRadiusSmall)      // %5
        .arg(Style::kPadding4)               // %6
        .arg(Style::kPadding12)              // %7
        .arg(Style::kFontWeightNormal)       // %8
        .arg(Style::kComboBoxMinWidth)       // %9
        .arg(Style::kCompactButtonMaxHeight) // %10
        .arg(m_colors.surfaceVariant.name()) // %11
        .arg(m_colors.primary.name())        // %12
        .arg(m_colors.onPrimary.name())      // %13
        .arg(m_colors.disabled.name());      // %14
}

auto ThemeManager::generateCompactToolBarStyle() const -> QString
{
    // 紧凑型工具栏样式
    return QString(R"(
        QToolBar {
            background-color: %1;
            border: none;
            spacing: 3px;
            padding: 2px;
        }
        QToolBar QToolButton {
            background-color: transparent;
            color: %2;
            border: 1px solid transparent;
            border-radius: %3px;
            padding: %4px %5px;
            margin: 1px;
            min-height: 20px;
            max-height: %6px;
        }
        QToolBar QToolButton:hover {
            background-color: %7;
            border-color: %8;
        }
        QToolBar QToolButton:pressed, QToolBar QToolButton:checked {
            background-color: %8;
            color: %9;
            border-color: %8;
        }
        QToolBar QToolButton:disabled {
            color: %10;
        }
        /* 连接状态特殊样式 */
        QToolBar QToolButton[connectionState="connected"] {
            background-color: %11;
            color: %9;
            border-color: %11;
        }
        QToolBar QToolButton[connectionState="connected"]:hover {
            background-color: %12;
            border-color: %12;
        }
        QToolBar QToolButton[connectionState="disconnected"] {
            border: 1px solid %13;
        }
        QToolBar::separator {
            background-color: %14;
            width: 1px;
            margin: 4px 2px;
        }
    )")
        .arg(m_colors.surface.name())        // %1
        .arg(m_colors.onSurface.name())      // %2
        .arg(Style::kBorderRadiusSmall)      // %3
        .arg(Style::kPadding4)               // %4
        .arg(Style::kPadding8)               // %5
        .arg(Style::kCompactButtonMaxHeight) // %6
        .arg(m_colors.surfaceVariant.name()) // %7
        .arg(m_colors.primary.name())        // %8
        .arg(m_colors.onPrimary.name())      // %9
        .arg(m_colors.disabled.name())       // %10
        .arg(m_colors.success.name())        // %11
        .arg(m_colors.success.name())        // %12 - 悬停时保持相同颜色
        .arg(m_colors.outline.name())        // %13
        .arg(m_colors.border.name());        // %14
}

auto ThemeManager::generateScrollBarStyle() const -> QString
{
    // 滚动条样式
    return QString(R"(
        QScrollBar:vertical, QScrollBar:horizontal {
            background-color: %1;
            border: none;
            border-radius: %2px;
        }
        QScrollBar:vertical {
            width: 12px;
        }
        QScrollBar:horizontal {
            height: 12px;
        }
        QScrollBar::handle:vertical, QScrollBar::handle:horizontal {
            background-color: %3;
            border-radius: %2px;
            margin: 0px;
        }
        QScrollBar::handle:vertical {
            min-height: 20px;
        }
        QScrollBar::handle:horizontal {
            min-width: 20px;
        }
        QScrollBar::handle:vertical:hover, QScrollBar::handle:horizontal:hover {
            background-color: %4;
        }
        QScrollBar::handle:vertical:pressed, QScrollBar::handle:horizontal:pressed {
            background-color: %5;
        }
        QScrollBar::add-line, QScrollBar::sub-line,
        QScrollBar::add-page, QScrollBar::sub-page {
            height: 0px;
            width: 0px;
            background: transparent;
        }
    )")
        .arg(m_colors.surfaceVariant.name())   // %1 - 滚动条背景
        .arg(Style::kBorderRadiusLarge)        // %2 - 滚动条圆角
        .arg(m_colors.outline.name())          // %3 - 滑块颜色
        .arg(m_colors.onSurfaceVariant.name()) // %4 - 滑块悬停
        .arg(m_colors.primary.name());         // %5 - 滑块按下
}
