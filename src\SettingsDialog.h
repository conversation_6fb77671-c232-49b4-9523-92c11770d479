#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include <QDialog>
#include <QFont>
#include <QList>
#include <QSerialPort>
#include <QString>

#include "VT100Parser.h" // 包含VT100配置结构体
#include "core/AppTypes.h"
#include "core/ReconnectConstants.h"

// 定义快捷命令的格式
enum class CommandFormat : std::uint8_t
{
    ASCII,
    HEX
};

// 用于快捷命令功能的结构体
struct QuickCommand
{
    QString uuid; // 用于唯一标识
    QString name;
    QString command;
    CommandFormat format; // "ASCII" or "HEX"
    bool enabled;
    bool isCycle;
    int cycleInterval;
};
Q_DECLARE_METATYPE(QuickCommand)

// 前向声明UI控件
class QTabWidget;
class QCheckBox;
class QSpinBox;
class QComboBox;
class QFontComboBox;
class QLineEdit;
class QPushButton;
class QTableWidget;
class QGroupBox;

// 定义一个结构体来聚合所有设置，方便传递
struct AppSettings
{
    // 基本设置
    bool minimizeToTrayOnClose;
    bool alwaysShowInTray; // 新增：始终在托盘显示图标
    bool windowStaysOnTop; // 新增：窗口置顶
    int terminalBufferSize;
    int terminalScrollPolicy; // 可能是枚举
    bool scrollOnInput;       // 新增：输入后滚动
    bool pauseOnResize;       // 新增：拖动时暂停渲染
    bool wordWrapEnabled;     // 新增：自动换行模式
    DisplayMode displayMode;  // 新增：数据显示模式

    // 串口设置
    QString portName;
    int baudRate;
    QSerialPort::DataBits dataBits;
    QSerialPort::Parity parity;
    QSerialPort::StopBits stopBits;
    int sendNewLineMode; // 0: \\r\n, 1: \\r, 2: \n
    bool serialEchoEnabled;

    // 日志设置
    QString logPath;
    QString logFileNameFormat;
    bool logSplitEnabled;
    int logSplitSizeMB;
    bool logTimestampEnabled;
    QString logTimestampFormat;
    bool autoEnableLogOnPortOpen; // 新增：打开串口时自动启用日志

    bool useDefaultLogViewer;
    QString externalLogViewerPath;

    // 字体设置 (从 MainWindow 移入)
    QFont terminalFont;

    // 快捷命令
    QList<QuickCommand> quickCommands;

    // VT100 命令控制
    VT100CommandConfig vt100CommandConfig;

    // 自动重连设置
    bool autoReconnectEnabled = App::Reconnect::kDefaultAutoReconnectEnabled;
    int maxReconnectAttempts = App::Reconnect::kDefaultMaxReconnectAttempts;
    int reconnectIntervalMs = App::Reconnect::kDefaultReconnectIntervalMs;
    double reconnectIntervalMultiplier = App::Reconnect::kDefaultReconnectIntervalMultiplier;
};

enum QuickCommandColumn : std::uint8_t
{
    UuidColumn = 0,
    EnabledColumn = 1,
    NameColumn = 2,
    CommandColumn = 3,
    FormatColumn = 4,
    CycleColumn = 5,
    IntervalColumn = 6,
    ColumnCount = 7 // Keep this last for column count
};

class SettingsDialog : public QDialog {
    Q_OBJECT

public:
    explicit SettingsDialog(QWidget *parent = nullptr);
    ~SettingsDialog() override;

    SettingsDialog(const SettingsDialog &) = delete;
    auto operator=(const SettingsDialog &) -> SettingsDialog & = delete;
    SettingsDialog(SettingsDialog &&) = delete;
    auto operator=(SettingsDialog &&) -> SettingsDialog & = delete;

    void setSettings(const AppSettings &settings);
    [[nodiscard]] auto getSettings() const -> AppSettings;
    void setSerialSettingsEnabled(bool enabled);
    void switchToQuickCommandsTab();

signals:
    void settingsApplied();

private slots:
    void onOkButtonClicked();
    void onApplyButtonClicked();
    void onLogPathBrowseClicked();
    void onLogViewerBrowseClicked();
    void onAddQuickCommandClicked();
    void onDeleteQuickCommandClicked();
    void updateFontPreview();

private:
    // --- UI 常量 ---
    static constexpr int kMinDialogWidth = 500;
    static constexpr int kMinDialogHeight = 400;

    // 字体
    static constexpr int kMinFontSize = 6;
    static constexpr int kMaxFontSize = 72;
    static constexpr int kPreviewLabelMinHeight = 60;
    static constexpr int kFontComboBoxExtraWidth = 50;

    // 缓冲区
    static constexpr int kMinBufferSize = 1000;
    static constexpr int kMaxBufferSize = 100000;
    static constexpr int kBufferSizeStep = 1000;

    // 日志
    static constexpr int kMinLogSplitSize = 1;
    static constexpr int kMaxLogSplitSize = 1024;
    static constexpr int kLogPathButtonWidth = 40;

    // 快捷命令
    static constexpr int kQuickCommandRowHeight = 32;
    static constexpr int kMinQuickCommandInterval = 10;
    static constexpr int kMaxQuickCommandInterval = 60000;
    static constexpr int kDefaultQuickCommandInterval = 1000;

    // 布局
    static constexpr int kLayoutSpacing = 10;

    void setupUi();
    void createGeneralTab();
    void createSerialTab();
    void createAppearanceTab();
    void createLogTab();
    void createQuickCommandsTab();
    void createVt100Tab();
    void createScriptTab();

    void loadSettingsToUi();
    void saveUiToSettings();

    QTabWidget *m_tabWidget;

    // --- 基本设置页控件 ---
    QCheckBox *m_minimizeToTrayCheckBox;
    QCheckBox *m_alwaysShowInTrayCheckBox; // 新增
    QSpinBox *m_bufferSizeSpinBox;
    QComboBox *m_scrollPolicyComboBox;
    QCheckBox *m_scrollOnInputCheckBox; // 新增
    QCheckBox *m_pauseOnResizeCheckBox; // 新增
    QCheckBox *m_wordWrapCheckBox;      // 新增：自动换行
    QComboBox *m_displayModeComboBox;   // 新增：数据显示模式

    // --- 外观设置页控件 ---
    class QFontComboBox *m_fontComboBox;
    class QSpinBox *m_fontSizeSpinBox;
    class QLabel *m_fontPreviewLabel;

    // --- 串口设置页控件 ---
    QComboBox *m_portComboBox;
    QComboBox *m_baudRateComboBox;
    QComboBox *m_dataBitsComboBox;
    QComboBox *m_parityComboBox;
    QComboBox *m_stopBitsComboBox;
    QComboBox *m_newLineModeComboBox;
    QCheckBox *m_serialEchoCheckBox;

    // --- 自动重连设置控件 ---
    QGroupBox *m_autoReconnectGroupBox;
    QCheckBox *m_autoReconnectEnabledCheckBox;
    QSpinBox *m_maxReconnectAttemptsSpinBox;
    QSpinBox *m_reconnectIntervalSpinBox;
    class QDoubleSpinBox *m_reconnectIntervalMultiplierSpinBox;

    // --- 日志设置页控件 ---
    QLineEdit *m_logPathEdit;
    QPushButton *m_logPathBrowseButton;
    QLineEdit *m_logFileNameFormatEdit;
    QCheckBox *m_logSplitCheckBox;
    QSpinBox *m_logSplitSizeSpinBox;
    QGroupBox *m_logTimestampGroupBox;
    QCheckBox *m_logTimestampCheckBox;
    QLineEdit *m_logTimestampFormatEdit;
    QCheckBox *m_useDefaultLogViewerCheckBox;
    QLineEdit *m_externalLogViewerPathEdit;
    QPushButton *m_externalLogViewerBrowseButton;
    QCheckBox *m_autoEnableLogOnOpenCheckBox; // 新增

    // --- 快捷命令页控件 ---
    QTableWidget *m_quickCommandsTable;
    QPushButton *m_addCommandButton;
    QPushButton *m_deleteCommandButton;

    // --- VT100 命令控制页控件 ---
    QCheckBox *m_enableSgrCheckBox;
    QCheckBox *m_enableEraseCheckBox;
    QCheckBox *m_enableCursorMovementCheckBox;

    // ASCII控制字符控件
    QCheckBox *m_enableBackspaceCheckBox;
    QCheckBox *m_enableHorizontalTabCheckBox;
    QCheckBox *m_enableBellCheckBox;
    QCheckBox *m_enableFormFeedCheckBox;
    QCheckBox *m_enableVerticalTabCheckBox;

    // 换行符控件 (强制启用，不可禁用)
    QCheckBox *m_enableLineFeedCheckBox;
    QCheckBox *m_enableCarriageReturnCheckBox;

    // --- 按钮 ---
    QPushButton *m_okButton;
    QPushButton *m_cancelButton;
    QPushButton *m_applyButton;

    AppSettings m_currentSettings;
};

#endif // SETTINGSDIALOG_H
