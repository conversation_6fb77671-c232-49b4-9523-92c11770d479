#include "FindWidget.h"
#include "HighPerformanceTerminal.h"

#include <QApplication>
#include <QBoxLayout>
#include <QCheckBox>
#include <QColor>
#include <QEvent>
#include <QLabel>
#include <QLineEdit>
#include <QMouseEvent>
#include <QPainter>
#include <QPainterPath>
#include <QPushButton>
#include <QStyle>
#include <QWidget>
#include <Qt>
#include <QtGlobal>

FindWidget::FindWidget(QWidget *parent)
    : QWidget(parent),
      m_searchLineEdit(nullptr),
      m_findPrevButton(nullptr),
      m_findNextButton(nullptr),
      m_caseSensitiveButton(nullptr),
      m_wholeWordButton(nullptr),
      m_regexButton(nullptr),
      m_findInSelectionButton(nullptr),
      m_closeButton(nullptr),
      m_searchCountLabel(nullptr)
{
    // 设置窗口属性，使其成为一个无边框的浮动工具窗口
    setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    // setAttribute(Qt::WA_DeleteOnClose); // <--- 移除此行以防止关闭时删除对象

    setupUi();

    connect(m_searchLineEdit, &QLineEdit::textChanged, this, &FindWidget::onSearchTextChanged);
    connect(m_searchLineEdit, &QLineEdit::returnPressed, this, &FindWidget::onFindNext);

    connect(m_findNextButton, &QPushButton::clicked, this, &FindWidget::onFindNext);
    connect(m_findPrevButton, &QPushButton::clicked, this, &FindWidget::onFindPrevious);

    connect(m_caseSensitiveButton, &QPushButton::clicked, this, &FindWidget::onOptionChanged);
    connect(m_wholeWordButton, &QPushButton::clicked, this, &FindWidget::onOptionChanged);
    connect(m_regexButton, &QPushButton::clicked, this, &FindWidget::onOptionChanged);
    connect(m_findInSelectionButton, &QPushButton::clicked, this, &FindWidget::onOptionChanged);

    connect(m_closeButton, &QPushButton::clicked, this, &QWidget::close);
}

void FindWidget::setupUi()
{
    auto *layout = new QHBoxLayout(this);
    layout->setContentsMargins(4, 4, 4, 4);
    layout->setSpacing(4);

    m_searchLineEdit = new QLineEdit(this);
    m_searchLineEdit->setPlaceholderText("搜索");
    m_searchLineEdit->setClearButtonEnabled(true);
    layout->addWidget(m_searchLineEdit);

    // --- 搜索选项按钮 ---
    m_caseSensitiveButton = new QPushButton(QIcon(":/icons/case-sensitive.svg"), "", this);
    m_caseSensitiveButton->setCheckable(true);
    m_caseSensitiveButton->setToolTip("区分大小写");
    layout->addWidget(m_caseSensitiveButton);

    m_wholeWordButton = new QPushButton(QIcon(":/icons/whole-word.svg"), "", this);
    m_wholeWordButton->setCheckable(true);
    m_wholeWordButton->setToolTip("全词匹配");
    m_wholeWordButton->setEnabled(true);
    layout->addWidget(m_wholeWordButton);

    m_regexButton = new QPushButton(QIcon(":/icons/regex.svg"), "", this);
    m_regexButton->setCheckable(true);
    m_regexButton->setToolTip("使用正则表达式 (未实现)");
    m_regexButton->setEnabled(false); // 禁用
    layout->addWidget(m_regexButton);

    m_searchCountLabel = new QLabel(this);
    m_searchCountLabel->setText("无结果");
    layout->addWidget(m_searchCountLabel);

    // --- 上一个/下一个 ---
    m_findPrevButton = new QPushButton(QIcon(":/icons/arrow-up.svg"), "", this);
    m_findPrevButton->setToolTip("上一个");
    layout->addWidget(m_findPrevButton);

    m_findNextButton = new QPushButton(QIcon(":/icons/arrow-down.svg"), "", this);
    m_findNextButton->setToolTip("下一个");
    layout->addWidget(m_findNextButton);

    // --- 在选定内容中查找 ---
    m_findInSelectionButton = new QPushButton(QIcon(":/icons/find-in-selection.svg"), "", this);
    m_findInSelectionButton->setCheckable(true);
    m_findInSelectionButton->setToolTip("在选定内容中查找 (未实现)");
    m_findInSelectionButton->setEnabled(false); // 禁用
    layout->addWidget(m_findInSelectionButton);

    // --- 关闭按钮 ---
    m_closeButton = new QPushButton(QIcon(":/icons/close.svg"), "", this);
    m_closeButton->setToolTip("关闭");
    layout->addWidget(m_closeButton);

    // 为所有可拖动的子控件安装事件过滤器
    const auto kChildrenToFilter = findChildren<QWidget *>();
    for (QWidget *child : kChildrenToFilter)
    {
        if (child != m_searchLineEdit)
        { // 输入框本身不用于拖动
            child->installEventFilter(this);
        }
    }

    // --- 样式表 ---
    setStyleSheet(R"(
        FindWidget {
            background-color: #3c3c3c;
            border: 1px solid #555;
            border-radius: 4px;
        }
        QLineEdit {
            background-color: #2c2c2c;
            border: 1px solid #555;
            color: #f0f0f0;
            padding: 4px;
        }
        QPushButton {
            background-color: transparent;
            color: #f0f0f0;
            border: 1px solid transparent;
            padding: 4px;
            min-width: 20px;
        }
        QPushButton:hover {
            background-color: #4a4a4a;
        }
        QPushButton:checked {
            background-color: #007acc;
            border-color: #005a9e;
        }
        QPushButton:disabled {
            color: #888;
        }
        QLabel {
            color: #f0f0f0;
            padding: 0 4px;
        }
    )");
}

void FindWidget::updateResults(int matchCount, int currentIndex)
{
    if (matchCount == 0)
    {
        m_searchCountLabel->setText("无结果");
    }
    else
    {
        m_searchCountLabel->setText(QString("%1 / %2").arg(currentIndex + 1).arg(matchCount));
    }
}

void FindWidget::setFocusOnLineEdit()
{
    m_searchLineEdit->setFocus();
    m_searchLineEdit->selectAll();
}

void FindWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 使用QSS中定义的背景色和边框色
    const QColor kBackgroundColor = palette().color(QPalette::Window);
    const QColor kBorderColor = palette().color(QPalette::WindowText); // 使用一个可区分的颜色

    // 创建一个圆角矩形路径
    QPainterPath path;
    path.addRoundedRect(this->rect(), 4, 4); // 4是圆角半径

    // 填充背景
    painter.fillPath(path, kBackgroundColor);

    // 绘制边框
    painter.setPen(QPen(kBorderColor, 1));
    painter.drawPath(path);
}

void FindWidget::mousePressEvent(QMouseEvent *event)
{
    // 只有当鼠标左键点击在背景上时才启动拖动
    if (event->button() == Qt::LeftButton && childAt(event->pos()) == nullptr)
    {
        m_dragStartPosition = event->globalPosition().toPoint() - frameGeometry().topLeft();
        m_isDragging = false; // 明确初始状态
        event->accept();
    }
    else
    {
        QWidget::mousePressEvent(event);
    }
}

void FindWidget::mouseMoveEvent(QMouseEvent *event)
{
    // 只有在左键按下且拖动起始位置有效时才移动
    if (((event->buttons() & Qt::LeftButton) != 0) && !m_dragStartPosition.isNull())
    {
        // 检查是否开始拖动
        if (!m_isDragging
            && (event->globalPosition().toPoint() - (this->frameGeometry().topLeft() + m_dragStartPosition))
                       .manhattanLength()
                   > QApplication::startDragDistance())
        {
            m_isDragging = true;
        }

        if (m_isDragging)
        {
            move(event->globalPosition().toPoint() - m_dragStartPosition);
            event->accept();
        }
    }
    else
    {
        QWidget::mouseMoveEvent(event);
    }
}

void FindWidget::mouseReleaseEvent(QMouseEvent *event)
{
    // 鼠标释放后，重置拖动起始位置
    if (event->button() == Qt::LeftButton)
    {
        m_dragStartPosition = QPoint();
        m_isDragging = false;
    }
    QWidget::mouseReleaseEvent(event);
}

auto FindWidget::eventFilter(QObject *watched, QEvent *event) -> bool
{
    if (watched->parent() != this || qobject_cast<QWidget *>(watched) == m_searchLineEdit)
    {
        return QWidget::eventFilter(watched, event);
    }

    // NOLINTBEGIN(clang-diagnostic-switch-enum)
    if (auto *mouseEvent = dynamic_cast<QMouseEvent *>(event))
    {
        switch (mouseEvent->type())
        {
            case QEvent::MouseButtonPress:
                return handleDragPress(mouseEvent);
            case QEvent::MouseMove:
                return handleDragMove(mouseEvent);
            case QEvent::MouseButtonRelease:
                return handleDragRelease(mouseEvent, watched);
            default:
                break;
        }
    }
    // NOLINTEND(clang-diagnostic-switch-enum)

    return QWidget::eventFilter(watched, event);
}

auto FindWidget::handleDragPress(QMouseEvent *event) -> bool
{
    if (event->button() == Qt::LeftButton)
    {
        m_dragStartPosition = event->globalPosition().toPoint() - frameGeometry().topLeft();
        m_isDragging = false;
        return true;
    }
    return false;
}

auto FindWidget::handleDragMove(QMouseEvent *event) -> bool
{
    if (((event->buttons() & Qt::LeftButton) != 0) && !m_dragStartPosition.isNull())
    {
        if (!m_isDragging
            && (event->globalPosition().toPoint() - (frameGeometry().topLeft() + m_dragStartPosition)).manhattanLength()
                   > QApplication::startDragDistance())
        {
            m_isDragging = true;
        }

        if (m_isDragging)
        {
            move(event->globalPosition().toPoint() - m_dragStartPosition);
        }
        return true;
    }
    return false;
}

auto FindWidget::handleDragRelease(QMouseEvent *event, QObject *watched) -> bool
{
    if (event->button() == Qt::LeftButton)
    {
        if (!m_isDragging)
        {
            if (auto *btn = qobject_cast<QPushButton *>(watched))
            {
                btn->click();
            }
        }
        m_dragStartPosition = QPoint();
        m_isDragging = false;
        return true;
    }
    return false;
}

void FindWidget::onSearchTextChanged()
{
    onOptionChanged(); // 文本改变时也触发搜索
}

void FindWidget::onFindNext()
{
    if (!m_searchLineEdit->text().isEmpty())
    {
        emit findNextTriggered();
    }
}

void FindWidget::onFindPrevious()
{
    if (!m_searchLineEdit->text().isEmpty())
    {
        emit findPreviousTriggered();
    }
}

void FindWidget::onOptionChanged()
{
    HighPerformanceTerminal::FindFlags flags = HighPerformanceTerminal::FindFlag::NoFlags;
    if (m_caseSensitiveButton->isChecked())
    {
        flags |= HighPerformanceTerminal::FindFlag::CaseSensitive;
    }
    if (m_wholeWordButton->isChecked())
    {
        flags |= HighPerformanceTerminal::FindFlag::WholeWord;
    }
    if (m_regexButton->isChecked())
    {
        flags |= HighPerformanceTerminal::FindFlag::Regex;
    }
    if (m_findInSelectionButton->isChecked())
    {
        flags |= HighPerformanceTerminal::FindFlag::InSelection;
    }

    emit findTriggered(m_searchLineEdit->text(), flags);
}

void FindWidget::triggerSearch()
{
    onOptionChanged();
}

void FindWidget::closeEvent(QCloseEvent *event)
{
    emit widgetClosed();
    QWidget::closeEvent(event);
}
