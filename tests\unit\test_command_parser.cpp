#include <QtTest/QtTest>
#include "../../src/CommandParser.h"

class TestCommandParser : public QObject
{
    Q_OBJECT

private slots:
    void testHexParsing_data();
    void testHexParsing();
    void testAsciiParsing_data();
    void testAsciiParsing();
};

void TestCommandParser::testHexParsing_data()
{
    QTest::addColumn<QString>("input");
    QTest::addColumn<QByteArray>("expected");

    QTest::newRow("Basic Hex") << "48 65 6C 6C 6F" << QByteArray::fromHex("48656C6C6F");
    QTest::newRow("Hex with Mixed Case") << "48e56C6c6F" << QByteArray::fromHex("48e56C6c6F");
    QTest::newRow("Hex with Spaces and Newlines") << "48 65\n6C\t6C 6F" << QByteArray::fromHex("48656C6C6F");
    QTest::newRow("Empty Input") << "" << QByteArray();
    QTest::newRow("Invalid Hex Chars") << "48 65 6C 6C 6F G" << QByteArray::fromHex("48656C6C6F");
    QTest::newRow("Odd Number of Hex Chars") << "48 65 6C 6C 6" << QByteArray::fromHex("48656C6C06");
}

void TestCommandParser::testHexParsing()
{
    QFETCH(QString, input);
    QFETCH(QByteArray, expected);

    QuickCommand cmd;
    cmd.command = input;
    cmd.format = CommandFormat::HEX;

    QCOMPARE(CommandParser::parse(cmd), expected);
}

void TestCommandParser::testAsciiParsing_data()
{
    QTest::addColumn<QString>("input");
    QTest::addColumn<QByteArray>("expected");

    QTest::newRow("Simple ASCII") << "Hello" << QByteArray("Hello");
    QTest::newRow("CRLF") << "Hello\\r\\nWorld" << QByteArray("Hello\r\nWorld");
    QTest::newRow("Mixed Escapes") << "\\tTab\\rReturn\\nNewline" << QByteArray("\tTab\rReturn\nNewline");
    QTest::newRow("Hex Escape") << "A\\x42" << QByteArray("A\x42");
    QTest::newRow("Incomplete Hex Escape") << "A\\x4" << QByteArray("A\\x4");
    QTest::newRow("Invalid Hex Escape") << "A\\xFG" << QByteArray("A\\xFG");
    QTest::newRow("Backslash Escape") << "C:\\\\Path" << QByteArray("C:\\Path");
    QTest::newRow("Unrecognized Escape") << "Hello\\zWorld" << QByteArray("Hello\\zWorld");
    QTest::newRow("Trailing Backslash") << "Hello\\" << QByteArray("Hello\\");
    QTest::newRow("Empty Input") << "" << QByteArray("");
}

void TestCommandParser::testAsciiParsing()
{
    QFETCH(QString, input);
    QFETCH(QByteArray, expected);

    QuickCommand cmd;
    cmd.command = input;
    cmd.format = CommandFormat::ASCII;

    QCOMPARE(CommandParser::parse(cmd), expected);
}

QTEST_MAIN(TestCommandParser)
#include "test_command_parser.moc"
