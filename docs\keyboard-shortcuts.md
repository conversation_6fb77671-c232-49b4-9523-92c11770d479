# SerialT 快捷键指南

## 🔧 VT100兼容性设计

为了避免与VT100终端控制序列冲突，SerialT采用了智能快捷键设计：

- **主要快捷键使用Alt组合键**：避免与VT100的Ctrl组合键冲突
- **安全的Ctrl+Shift组合键**：用于不会与终端输入冲突的功能
- **智能快捷键管理**：未来可根据连接状态动态调整

## 串口操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Alt+C` | 连接/断开切换 | 智能切换串口连接状态：未连接时连接，已连接时断开 |
| `Alt+R` | 刷新端口列表 | 重新扫描可用的串口设备 |

## 视图操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Alt+L` | 清空视图 | 清除终端显示的所有内容 |
| `Ctrl+Shift+F` | 查找 | 打开查找对话框，搜索终端内容 |
| `Alt+W` | 自动换行 | 切换自动换行模式 |

## 显示模式
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Alt+1` | ASCII模式 | 切换到ASCII文本显示模式 |
| `Alt+2` | 十六进制模式 | 切换到十六进制显示模式 |
| `Alt+3` | 混合模式 | 切换到ASCII+十六进制混合显示模式 |

## 缩放操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+=` | 放大 | 增大终端字体大小 |
| `Ctrl+-` | 缩小 | 减小终端字体大小 |
| `Ctrl+0` | 重置缩放 | 恢复默认字体大小 |

## 主题切换
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+Shift+L` | 亮色主题 | 切换到亮色主题 |
| `Ctrl+Shift+D` | 暗色主题 | 切换到暗色主题 |
| `Ctrl+Shift+A` | 跟随系统 | 自动跟随系统主题设置 |

## 日志功能
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+Shift+G` | 切换日志记录 | 启用或禁用数据日志记录 |
| `Ctrl+Shift+O` | 打开日志文件 | 在默认编辑器中打开当前日志文件 |

## 窗口操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+Shift+T` | 窗口置顶 | 切换窗口始终置顶状态 |

## 应用程序
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+,` | 设置 | 打开应用程序设置对话框 |
| `Ctrl+Q` | 退出 | 退出应用程序 |

## 使用说明

### VT100兼容性
- **Alt组合键设计**：主要快捷键使用Alt组合键，避免与VT100终端控制序列冲突
- **智能冲突避免**：程序会自动处理可能的按键冲突
- **终端优先**：当焦点在终端区域时，终端输入优先于快捷键

### 快捷键冲突处理
- 如果快捷键与系统快捷键冲突，系统快捷键优先
- 在某些输入法状态下，快捷键可能不响应，请切换到英文输入法
- Alt组合键在大多数系统上都是安全的，不会与VT100冲突

### 上下文相关性
- 串口操作快捷键在串口未连接时可能无效
- 某些功能快捷键需要相应的功能模块已启用
- 程序具备智能快捷键管理，可根据连接状态调整

### 为什么选择Alt组合键？
1. **VT100兼容性**：VT100终端主要使用Ctrl组合键，Alt组合键冲突风险极低
2. **用户习惯**：Alt组合键在现代应用中广泛使用，用户容易接受
3. **扩展性**：为将来的功能扩展预留了Ctrl组合键空间
4. **智能管理**：程序内置快捷键管理系统，可根据需要动态调整

### 自定义快捷键
目前版本不支持自定义快捷键，所有快捷键都是预设的。如需修改，请联系开发者或查看源代码。

## 更新历史
- v1.4: 解决快捷键冲突
  - 将“查找”快捷键从 `Alt+F` 改为 `Ctrl+Shift+F`
  - 将“置顶”快捷键从 `Alt+T` 改为 `Ctrl+Shift+T`
- v1.1: VT100兼容性改进
  - 将主要快捷键从Ctrl改为Alt组合键
  - 添加智能快捷键管理系统
  - 完善VT100冲突避免机制
- v1.0: 初始快捷键支持
  - 基础串口操作快捷键
  - 视图和显示模式快捷键
  - 主题和窗口操作快捷键

## 技术说明

### VT100控制序列冲突分析
以下是常见的VT100控制序列，我们的Alt组合键设计避免了这些冲突：

**高冲突风险的Ctrl组合键（已避免）：**
- `Ctrl+L` - VT100清屏命令 → 改为 `Alt+L`
- `Ctrl+D` - EOF信号 → 改为 `Alt+D`
- `Ctrl+F` - 光标前进 → 改为 `Ctrl+Shift+F`
- `Ctrl+W` - 删除单词 → 改为 `Alt+W`
- `Ctrl+T` - 交换字符 → 改为 `Ctrl+Shift+T`

**安全的组合键（继续使用）：**
- `Ctrl+Shift+*` - VT100很少使用
- `Ctrl+,` - 现代应用约定
- `Ctrl+Q` - 应用程序退出约定
