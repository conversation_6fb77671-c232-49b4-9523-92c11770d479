#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QSignalSpy>
#include <QTest>
#include <QThread>
#include <QTimer>
#include "../../src/SerialProcessor.h"
#include "../../src/VT100Parser.h"

class TestRealisticSerialUsage : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 真实使用场景测试
    void testNormalSerialCommunication();
    void testLogOutputScenario();
    void testInteractiveTerminalSession();
    void testMixedDataTypes();

private:
    SerialProcessor *m_processor;
    VT100Parser *m_parser;

    // 模拟真实数据
    QByteArray generateLogData(int lineCount);
    QByteArray generateInteractiveSession();
    QByteArray generateMixedData();
};

void TestRealisticSerialUsage::initTestCase()
{
    qDebug() << "\n=== 真实串口使用场景测试 ===";
    qDebug() << "测试目标：验证SerialT在真实使用场景下的性能和稳定性";
}

void TestRealisticSerialUsage::cleanupTestCase()
{
    qDebug() << "=== 真实串口使用场景测试完成 ===\n";
}

void TestRealisticSerialUsage::init()
{
    m_processor = new SerialProcessor(this);
    m_parser = m_processor->parser();

    // 使用默认配置
    VT100CommandConfig config;
    m_parser->setCommandConfig(config);
}

void TestRealisticSerialUsage::cleanup()
{
    if (m_processor)
    {
        m_processor->closePort();
        delete m_processor;
        m_processor = nullptr;
        m_parser = nullptr;
    }
}

QByteArray TestRealisticSerialUsage::generateLogData(int lineCount)
{
    QByteArray data;
    for (int i = 0; i < lineCount; ++i)
    {
        QString line = QString("[%1] INFO: Processing item %2 - Status: OK\n")
                           .arg(QDateTime::currentDateTime().toString("hh:mm:ss.zzz"))
                           .arg(i + 1);
        data.append(line.toLatin1());
    }
    return data;
}

QByteArray TestRealisticSerialUsage::generateInteractiveSession()
{
    QByteArray data;

    // 模拟命令提示符
    data.append("$ ");

    // 模拟用户输入命令
    data.append("ls -la\n");

    // 模拟命令输出
    data.append("total 1024\n");
    data.append("drwxr-xr-x  2 <USER> <GROUP>  4096 Jan 24 10:30 .\n");
    data.append("drwxr-xr-x  3 <USER> <GROUP>  4096 Jan 24 10:29 ..\n");
    data.append("-rw-r--r--  1 <USER> <GROUP>   220 Jan 24 10:30 .bashrc\n");
    data.append("-rw-r--r--  1 <USER> <GROUP>   807 Jan 24 10:30 .profile\n");

    // 新的提示符
    data.append("$ ");

    return data;
}

QByteArray TestRealisticSerialUsage::generateMixedData()
{
    QByteArray data;

    // 普通文本
    data.append("Starting system initialization...\n");

    // 带颜色的文本
    data.append("\x1b[32mSUCCESS:\x1b[0m Module loaded\n");
    data.append("\x1b[31mERROR:\x1b[0m Failed to connect\n");
    data.append("\x1b[33mWARNING:\x1b[0m Low memory\n");

    // 光标移动
    data.append("\x1b[2J\x1b[H"); // 清屏并移动到左上角
    data.append("Screen cleared\n");

    // 制表符和退格
    data.append("Column1\tColumn2\tColumn3\n");
    data.append("Test\b\b\b\bDemo\n");

    return data;
}

void TestRealisticSerialUsage::testNormalSerialCommunication()
{
    qDebug() << "\n--- 测试：正常串口通信 ---";

    // 模拟正常的串口数据接收（小数据包，间隔发送）
    QSignalSpy parseFinishedSpy(m_parser, &VT100Parser::parseFinished);

    QElapsedTimer timer;
    timer.start();

    int totalPackets = 50;
    qint64 totalBytes = 0;

    for (int i = 0; i < totalPackets; ++i)
    {
        QByteArray packet = QString("Packet %1: Hello World!\n").arg(i + 1).toLatin1();
        totalBytes += packet.size();

        m_parser->processData(packet);

        // 模拟正常的数据间隔（10ms）
        QTest::qWait(10);
    }

    qint64 elapsedMs = timer.elapsed();

    // 获取所有命令
    auto allCommands = m_parser->takeAllCommands();

    // 重建数据
    QByteArray receivedData;
    for (const auto &command : allCommands)
    {
        if (command.type == TerminalCommand::PrintableChar && !command.params.isEmpty())
        {
            QChar ch = command.params.first().toChar();
            receivedData.append(ch.toLatin1());
        }
    }

    bool integrityOk = (receivedData.size() == totalBytes);
    double throughputKBps = (static_cast<double>(totalBytes) / 1024.0) / (static_cast<double>(elapsedMs) / 1000.0);

    qDebug() << "数据包数量:" << totalPackets;
    qDebug() << "总数据量:" << totalBytes << "字节";
    qDebug() << "接收数据量:" << receivedData.size() << "字节";
    qDebug() << "传输时间:" << elapsedMs << "毫秒";
    qDebug() << "吞吐量:" << QString::number(throughputKBps, 'f', 2) << "KB/s";
    qDebug() << "数据完整性:" << (integrityOk ? "通过" : "失败");
    qDebug() << "解析信号次数:" << parseFinishedSpy.count();

    QVERIFY2(integrityOk, "正常串口通信数据完整性失败");
    QVERIFY2(parseFinishedSpy.count() == static_cast<qsizetype>(totalPackets), "解析信号次数不匹配");
}

void TestRealisticSerialUsage::testLogOutputScenario()
{
    qDebug() << "\n--- 测试：日志输出场景 ---";

    // 模拟设备持续输出日志的场景
    QSignalSpy parseFinishedSpy(m_parser, &VT100Parser::parseFinished);

    QElapsedTimer timer;
    timer.start();

    int logBatches = 10;
    int linesPerBatch = 20;
    qint64 totalBytes = 0;

    for (int batch = 0; batch < logBatches; ++batch)
    {
        QByteArray logData = generateLogData(linesPerBatch);
        totalBytes += logData.size();

        m_parser->processData(logData);

        // 模拟日志输出间隔（100ms）
        QTest::qWait(100);
    }

    qint64 elapsedMs = timer.elapsed();

    // 获取所有命令
    auto allCommands = m_parser->takeAllCommands();

    // 重建数据
    QByteArray receivedData;
    for (const auto &command : allCommands)
    {
        if (command.type == TerminalCommand::PrintableChar && !command.params.isEmpty())
        {
            QChar ch = command.params.first().toChar();
            receivedData.append(ch.toLatin1());
        }
    }

    bool integrityOk = (receivedData.size() == totalBytes);
    double throughputKBps = (static_cast<double>(totalBytes) / 1024.0) / (static_cast<double>(elapsedMs) / 1000.0);

    qDebug() << "日志批次:" << logBatches;
    qDebug() << "每批行数:" << linesPerBatch;
    qDebug() << "总数据量:" << totalBytes << "字节";
    qDebug() << "接收数据量:" << receivedData.size() << "字节";
    qDebug() << "传输时间:" << elapsedMs << "毫秒";
    qDebug() << "吞吐量:" << QString::number(throughputKBps, 'f', 2) << "KB/s";
    qDebug() << "数据完整性:" << (integrityOk ? "通过" : "失败");

    QVERIFY2(integrityOk, "日志输出场景数据完整性失败");
}

void TestRealisticSerialUsage::testInteractiveTerminalSession()
{
    qDebug() << "\n--- 测试：交互式终端会话 ---";

    // 模拟用户与设备的交互式会话
    QSignalSpy parseFinishedSpy(m_parser, &VT100Parser::parseFinished);

    QElapsedTimer timer;
    timer.start();

    int sessionCount = 5;
    qint64 totalBytes = 0;

    for (int session = 0; session < sessionCount; ++session)
    {
        QByteArray sessionData = generateInteractiveSession();
        totalBytes += sessionData.size();

        m_parser->processData(sessionData);

        // 模拟用户思考时间（500ms）
        QTest::qWait(500);
    }

    qint64 elapsedMs = timer.elapsed();

    // 获取所有命令
    auto allCommands = m_parser->takeAllCommands();

    // 重建数据
    QByteArray receivedData;
    for (const auto &command : allCommands)
    {
        if (command.type == TerminalCommand::PrintableChar && !command.params.isEmpty())
        {
            QChar ch = command.params.first().toChar();
            receivedData.append(ch.toLatin1());
        }
    }

    bool integrityOk = (receivedData.size() == totalBytes);
    double throughputKBps = (static_cast<double>(totalBytes) / 1024.0) / (static_cast<double>(elapsedMs) / 1000.0);

    qDebug() << "交互会话数:" << sessionCount;
    qDebug() << "总数据量:" << totalBytes << "字节";
    qDebug() << "接收数据量:" << receivedData.size() << "字节";
    qDebug() << "传输时间:" << elapsedMs << "毫秒";
    qDebug() << "吞吐量:" << QString::number(throughputKBps, 'f', 2) << "KB/s";
    qDebug() << "数据完整性:" << (integrityOk ? "通过" : "失败");

    QVERIFY2(integrityOk, "交互式终端会话数据完整性失败");
}

void TestRealisticSerialUsage::testMixedDataTypes()
{
    qDebug() << "\n--- 测试：混合数据类型 ---";

    // 测试包含VT100控制序列的混合数据
    QSignalSpy parseFinishedSpy(m_parser, &VT100Parser::parseFinished);

    QElapsedTimer timer;
    timer.start();

    int iterations = 20;
    qint64 totalBytes = 0;

    for (int i = 0; i < iterations; ++i)
    {
        QByteArray mixedData = generateMixedData();
        totalBytes += mixedData.size();

        m_parser->processData(mixedData);

        // 模拟数据间隔（50ms）
        QTest::qWait(50);
    }

    qint64 elapsedMs = timer.elapsed();

    // 获取所有命令
    auto allCommands = m_parser->takeAllCommands();

    // 统计不同类型的命令
    int printableChars = 0;
    int colorCommands = 0;
    int cursorCommands = 0;
    int eraseCommands = 0;
    int controlCommands = 0;
    int otherCommands = 0;

    for (const auto &command : allCommands)
    {
        switch (command.type)
        {
            case TerminalCommand::PrintableChar:
                printableChars++;
                break;
            case TerminalCommand::ForegroundColorChanged:
            case TerminalCommand::BackgroundColorChanged:
            case TerminalCommand::BoldChanged:
            case TerminalCommand::AttributesReset:
                colorCommands++;
                break;
            case TerminalCommand::CursorUp:
            case TerminalCommand::CursorDown:
            case TerminalCommand::CursorForward:
            case TerminalCommand::CursorBack:
            case TerminalCommand::SetCursorPosition:
                cursorCommands++;
                break;
            case TerminalCommand::EraseInDisplay:
            case TerminalCommand::EraseInLine:
                eraseCommands++;
                break;
            case TerminalCommand::Backspace:
            case TerminalCommand::HorizontalTab:
            case TerminalCommand::Bell:
            case TerminalCommand::FormFeed:
            case TerminalCommand::VerticalTab:
                controlCommands++;
                break;
        }
    }

    double throughputKBps = (static_cast<double>(totalBytes) / 1024.0) / (static_cast<double>(elapsedMs) / 1000.0);

    qDebug() << "迭代次数:" << iterations;
    qDebug() << "总数据量:" << totalBytes << "字节";
    qDebug() << "传输时间:" << elapsedMs << "毫秒";
    qDebug() << "吞吐量:" << QString::number(throughputKBps, 'f', 2) << "KB/s";
    qDebug() << "可打印字符命令:" << printableChars;
    qDebug() << "颜色/属性命令:" << colorCommands;
    qDebug() << "光标命令:" << cursorCommands;
    qDebug() << "擦除命令:" << eraseCommands;
    qDebug() << "控制字符命令:" << controlCommands;
    qDebug() << "其他命令:" << otherCommands;
    qDebug() << "总命令数:" << allCommands.size();

    QVERIFY2(allCommands.size() > 0, "没有解析到任何命令");
    QVERIFY2(printableChars > 0, "没有解析到可打印字符");
    QVERIFY2(colorCommands > 0, "没有解析到颜色/属性命令");
    QVERIFY2(eraseCommands > 0, "没有解析到擦除命令");

    // 验证命令分类的完整性
    int totalCategorized =
        printableChars + colorCommands + cursorCommands + eraseCommands + controlCommands + otherCommands;
    QVERIFY2(totalCategorized == allCommands.size(), "命令分类统计不完整");
}

QTEST_MAIN(TestRealisticSerialUsage)
#include "test_realistic_serial_usage.moc"
