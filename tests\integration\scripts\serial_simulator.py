#!/usr/bin/env python3
"""
SerialT 集成测试 - 串口模拟器
用于模拟串口设备，向SerialT发送各种测试数据
"""

import serial
import time
import threading
import random
import string
from typing import Optional, Callable
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SerialSimulator:
    """串口模拟器类"""
    
    def __init__(self, port: str, baudrate: int = 115200, timeout: float = 1.0):
        """
        初始化串口模拟器
        
        Args:
            port: 串口名称 (如 'COM11')
            baudrate: 波特率
            timeout: 超时时间
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial: Optional[serial.Serial] = None
        self.running = False
        self.receive_thread: Optional[threading.Thread] = None
        self.receive_callback: Optional[Callable[[bytes], None]] = None
        
    def connect(self) -> bool:
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            logger.info(f"已连接到串口 {self.port}, 波特率: {self.baudrate}")
            return True
        except Exception as e:
            logger.error(f"连接串口失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        self.stop_receiving()
        if self.serial and self.serial.is_open:
            self.serial.close()
            logger.info(f"已断开串口 {self.port}")
    
    def send_text(self, text: str, delay: float = 0.0) -> bool:
        """
        发送文本数据
        
        Args:
            text: 要发送的文本
            delay: 发送延迟（秒）
        """
        if not self.serial or not self.serial.is_open:
            logger.error("串口未连接")
            return False
        
        try:
            if delay > 0:
                time.sleep(delay)
            
            data = text.encode('utf-8')
            bytes_sent = self.serial.write(data)
            self.serial.flush()
            
            logger.info(f"发送文本: {repr(text)} ({bytes_sent} 字节)")
            return True
        except Exception as e:
            logger.error(f"发送文本失败: {e}")
            return False
    
    def send_bytes(self, data: bytes, delay: float = 0.0) -> bool:
        """
        发送字节数据
        
        Args:
            data: 要发送的字节数据
            delay: 发送延迟（秒）
        """
        if not self.serial or not self.serial.is_open:
            logger.error("串口未连接")
            return False
        
        try:
            if delay > 0:
                time.sleep(delay)
            
            bytes_sent = self.serial.write(data)
            self.serial.flush()
            
            logger.info(f"发送字节: {data.hex()} ({bytes_sent} 字节)")
            return True
        except Exception as e:
            logger.error(f"发送字节失败: {e}")
            return False
    
    def send_vt100_sequence(self, sequence: str, delay: float = 0.0) -> bool:
        """
        发送VT100控制序列
        
        Args:
            sequence: VT100控制序列
            delay: 发送延迟（秒）
        """
        return self.send_text(sequence, delay)
    
    def send_large_data(self, size_kb: int, chunk_size: int = 1024, delay_between_chunks: float = 0.01) -> bool:
        """
        发送大量数据
        
        Args:
            size_kb: 数据大小（KB）
            chunk_size: 每次发送的块大小
            delay_between_chunks: 块之间的延迟
        """
        logger.info(f"开始发送大量数据: {size_kb}KB")
        
        total_bytes = size_kb * 1024
        sent_bytes = 0
        
        while sent_bytes < total_bytes:
            remaining = min(chunk_size, total_bytes - sent_bytes)
            
            # 生成随机文本数据
            chunk_data = ''.join(random.choices(string.ascii_letters + string.digits + ' \n', k=remaining))
            
            if not self.send_text(chunk_data, delay_between_chunks):
                logger.error(f"发送大数据失败，已发送: {sent_bytes}/{total_bytes} 字节")
                return False
            
            sent_bytes += remaining
            
            # 进度报告
            if sent_bytes % (10 * 1024) == 0:  # 每10KB报告一次
                progress = (sent_bytes / total_bytes) * 100
                logger.info(f"发送进度: {progress:.1f}% ({sent_bytes}/{total_bytes} 字节)")
        
        logger.info(f"大量数据发送完成: {sent_bytes} 字节")
        return True
    
    def start_receiving(self, callback: Callable[[bytes], None]):
        """
        开始接收数据
        
        Args:
            callback: 接收到数据时的回调函数
        """
        if self.running:
            logger.warning("接收线程已在运行")
            return
        
        self.receive_callback = callback
        self.running = True
        self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
        self.receive_thread.start()
        logger.info("开始接收数据")
    
    def stop_receiving(self):
        """停止接收数据"""
        if self.running:
            self.running = False
            if self.receive_thread:
                self.receive_thread.join(timeout=2.0)
            logger.info("停止接收数据")
    
    def _receive_loop(self):
        """接收数据循环"""
        while self.running and self.serial and self.serial.is_open:
            try:
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data and self.receive_callback:
                        self.receive_callback(data)
                else:
                    time.sleep(0.01)  # 避免CPU占用过高
            except Exception as e:
                logger.error(f"接收数据错误: {e}")
                break
    
    def __enter__(self):
        """上下文管理器入口"""
        if self.connect():
            return self
        else:
            raise RuntimeError("无法连接串口")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


def main():
    """主函数 - 演示用法"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SerialT 串口模拟器')
    parser.add_argument('--port', default='COM31', help='串口名称 (测试工具端)')
    parser.add_argument('--baudrate', type=int, default=115200, help='波特率')
    parser.add_argument('--test', choices=['text', 'vt100', 'large', 'basic_text', 'high_frequency'], default='text', help='测试类型')
    parser.add_argument('--duration', type=int, default=0, help='测试持续时间（秒），0表示无限制')
    
    args = parser.parse_args()
    
    def on_receive(data: bytes):
        """接收数据回调"""
        try:
            text = data.decode('utf-8', errors='replace')
            logger.info(f"接收到数据: {repr(text)}")
        except Exception as e:
            logger.error(f"解码接收数据失败: {e}")
    
    try:
        with SerialSimulator(args.port, args.baudrate) as sim:
            sim.start_receiving(on_receive)

            start_time = time.time()

            if args.test in ['text', 'basic_text']:
                # 基础文本测试
                sim.send_text("Hello SerialT!\n")
                time.sleep(0.5)
                sim.send_text("ASCII text test\n")
                time.sleep(0.5)
                sim.send_text("Special chars: !@#$%^&*()\n")
                time.sleep(0.5)
                sim.send_text("Numbers: 1234567890\n")

            elif args.test == 'vt100':
                # VT100测试
                sim.send_vt100_sequence("\033[2J")  # 清屏
                time.sleep(0.2)
                sim.send_vt100_sequence("\033[H")   # 光标回到原点
                time.sleep(0.2)
                sim.send_vt100_sequence("\033[31mRed Text\033[0m\n")  # 红色文本
                time.sleep(0.2)
                sim.send_vt100_sequence("\033[42mGreen Background\033[0m\n")  # 绿色背景
                time.sleep(0.2)
                sim.send_vt100_sequence("\033[1mBold Text\033[0m\n")  # 粗体
                time.sleep(0.2)
                sim.send_vt100_sequence("\033[4mUnderlined Text\033[0m\n")  # 下划线

            elif args.test == 'large':
                # 大数据测试
                sim.send_large_data(100)  # 发送100KB数据

            elif args.test == 'high_frequency':
                # 高频数据测试
                logger.info("开始高频数据测试...")
                for i in range(100):
                    sim.send_text(f"Packet {i:03d}: {time.time():.3f}\n")
                    time.sleep(0.05)  # 50ms间隔

            # 根据duration参数决定运行时间
            if args.duration > 0:
                logger.info(f"测试将运行 {args.duration} 秒...")
                while time.time() - start_time < args.duration:
                    time.sleep(0.1)
                logger.info("测试时间到，退出")
            else:
                # 保持运行以接收回应
                logger.info("按 Ctrl+C 退出...")
                while True:
                    time.sleep(1)
                
    except KeyboardInterrupt:
        logger.info("用户中断，退出程序")
    except Exception as e:
        logger.error(f"程序错误: {e}")


if __name__ == '__main__':
    main()
