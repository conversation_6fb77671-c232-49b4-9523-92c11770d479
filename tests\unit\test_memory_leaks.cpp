#include <QApplication>
#include <QDebug>
#include <QEventLoop>
#include <QThread>
#include <QTimer>
#include <QtTest/QtTest>
#include "../../src/HighPerformanceTerminal.h"
#include "../../src/SerialProcessor.h"
#include "../../src/SettingsDialog.h"
#include "../../src/VT100Parser.h"

#ifdef _WIN32
// clang-format off
#define WIN32_LEAN_AND_MEAN  // Reduce Windows header bloat
#include <windows.h>         // Must be included before psapi.h
#include <psapi.h>
// clang-format on
#endif

class TestMemoryLeaks : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 内存泄漏检测测试
    void testSerialProcessorMemoryLeaks();
    void testVT100ParserMemoryLeaks();
    void testTerminalMemoryLeaks();
    void testSettingsDialogMemoryLeaks();
    void testRepeatedOperationsMemoryLeaks();
    void testLongRunningOperationsMemoryLeaks();

    // 资源管理测试
    void testProperResourceCleanup();
    void testExceptionSafetyMemoryLeaks();

private:
    // 辅助方法
    size_t getCurrentMemoryUsage();
    void performGarbageCollection();
    bool isMemoryLeakDetected(size_t initialMemory, size_t finalMemory,
                              size_t threshold = static_cast<size_t>(1024) * 1024); // 1MB threshold
    void createAndDestroyObjects(int count);
};

void TestMemoryLeaks::initTestCase()
{
    qDebug() << "Starting Memory Leak Detection tests";

    // 确保有QApplication实例
    if (QApplication::instance() == nullptr)
    {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestMemoryLeaks::cleanupTestCase()
{
    qDebug() << "Memory Leak Detection tests completed";
}

void TestMemoryLeaks::init()
{
    // 每个测试前进行垃圾回收
    performGarbageCollection();
}

void TestMemoryLeaks::cleanup()
{
    // 每个测试后进行垃圾回收
    performGarbageCollection();
}

void TestMemoryLeaks::testSerialProcessorMemoryLeaks()
{
    size_t initialMemory = getCurrentMemoryUsage();

    // 创建和销毁多个SerialProcessor实例
    for (int i = 0; i < 100; ++i)
    {
        SerialProcessor *processor = new SerialProcessor();

        // 模拟一些操作
        processor->setEchoEnabled(true);
        processor->setNewLineMode(NewLineMode::Cr);

        // 尝试打开不存在的端口（应该失败但不泄漏）
        processor->openPort("COM999", 9600);
        processor->closePort();

        delete processor;

        // 每10次迭代进行一次垃圾回收
        if (i % 10 == 0)
        {
            performGarbageCollection();
        }
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "SerialProcessor test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory), "Potential memory leak detected in SerialProcessor");
}

void TestMemoryLeaks::testVT100ParserMemoryLeaks()
{
    size_t initialMemory = getCurrentMemoryUsage();

    // 创建和销毁多个VT100Parser实例
    for (int i = 0; i < 100; ++i)
    {
        VT100Parser *parser = new VT100Parser();

        // 模拟解析大量数据
        QByteArray testData;
        for (int j = 0; j < 100; ++j)
        {
            testData += QString("Line %1 with \x1b[31mcolor\x1b[0m text\n").arg(j).toLatin1();
        }

        parser->processData(testData);

        // 获取并清空命令
        parser->takeAllCommands();

        delete parser;

        if (i % 10 == 0)
        {
            performGarbageCollection();
        }
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "VT100Parser test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory), "Potential memory leak detected in VT100Parser");
}

void TestMemoryLeaks::testTerminalMemoryLeaks()
{
    size_t initialMemory = getCurrentMemoryUsage();

    // 创建和销毁多个Terminal实例
    for (int i = 0; i < 50; ++i)
    { // 减少数量，因为GUI组件更重
        HighPerformanceTerminal *terminal = new HighPerformanceTerminal();
        VT100Parser *parser = new VT100Parser();

        terminal->setParser(parser);
        terminal->setBufferSize(1000);
        terminal->setWordWrapEnabled(true);

        // 模拟一些数据处理
        parser->processData("Test data for terminal\n");
        terminal->scheduleUpdate();

        // 等待处理完成
        QTest::qWait(10);

        delete terminal;
        delete parser;

        if (i % 5 == 0)
        {
            performGarbageCollection();
        }
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "Terminal test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory,
                                   static_cast<size_t>(10) * 1024 * 1024), // 10MB threshold for GUI
             "Potential memory leak detected in HighPerformanceTerminal");
}

void TestMemoryLeaks::testSettingsDialogMemoryLeaks()
{
    // 测试单个SettingsDialog实例的创建和销毁
    size_t initialMemory = getCurrentMemoryUsage();

    // 先创建一个实例来"预热"Qt的内存分配器
    {
        SettingsDialog *warmupDialog = new SettingsDialog();
        delete warmupDialog;
        performGarbageCollection();
        QThread::msleep(100);
        performGarbageCollection();
    }

    // 重新测量基准内存
    size_t baselineMemory = getCurrentMemoryUsage();

    // 测试多次创建和销毁是否会持续增长内存
    size_t memoryAfterFirst = 0;
    size_t memoryAfterLast = 0;

    for (int i = 0; i < 5; ++i) {
        SettingsDialog *dialog = new SettingsDialog();

        // 模拟设置操作
        AppSettings settings;
        settings.terminalBufferSize = 5000;
        settings.baudRate = 115200;
        settings.wordWrapEnabled = true;

        dialog->setSettings(settings);
        AppSettings retrievedSettings = dialog->getSettings();
        Q_UNUSED(retrievedSettings)

        delete dialog;

        // 强制垃圾回收
        performGarbageCollection();
        QThread::msleep(100);
        performGarbageCollection();

        if (i == 0) {
            memoryAfterFirst = getCurrentMemoryUsage();
        }
        if (i == 4) {
            memoryAfterLast = getCurrentMemoryUsage();
        }
    }

    qDebug() << "SettingsDialog test - Initial memory:" << initialMemory
             << "Baseline memory:" << baselineMemory
             << "After first:" << memoryAfterFirst
             << "After last:" << memoryAfterLast;

    // 检查是否有持续的内存增长（而不是一次性的分配）
    size_t memoryGrowth = memoryAfterLast > memoryAfterFirst ?
                         memoryAfterLast - memoryAfterFirst : 0;

    qDebug() << "Memory growth during repeated operations:" << memoryGrowth;

    // 允许一些内存增长，但不应该有大量持续增长
    QVERIFY2(memoryGrowth < static_cast<size_t>(10) * 1024 * 1024, // 10MB growth limit
             "Excessive memory growth detected in repeated SettingsDialog operations");
}

void TestMemoryLeaks::testRepeatedOperationsMemoryLeaks()
{
    size_t initialMemory = getCurrentMemoryUsage();

    SerialProcessor processor;
    VT100Parser parser;

    // 重复执行相同操作
    for (int i = 0; i < 1000; ++i)
    {
        // 模拟数据处理
        QByteArray data = QString("Repeated operation %1\n").arg(i).toLatin1();
        parser.processData(data);

        // 获取命令
        auto commands = parser.takeAllCommands();
        Q_UNUSED(commands)

        // 模拟串口操作
        processor.setNewLineMode(static_cast<NewLineMode>(i % 3));
        processor.setEchoEnabled(i % 2 == 0);

        if (i % 100 == 0)
        {
            performGarbageCollection();
        }
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "Repeated operations test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory),
             "Potential memory leak detected in repeated operations");
}

void TestMemoryLeaks::testLongRunningOperationsMemoryLeaks()
{
    size_t initialMemory = getCurrentMemoryUsage();

    HighPerformanceTerminal terminal;
    VT100Parser parser;
    terminal.setParser(&parser);

    // 模拟长时间运行的操作
    for (int i = 0; i < 100; ++i)
    { // 减少迭代次数
        // 生成大量数据
        QString largeData;
        for (int j = 0; j < 20; ++j)
        { // 减少内部循环
            largeData += QString("Long running test line %1-%2 with some content\n").arg(i).arg(j);
        }

        parser.processData(largeData.toLatin1());
        terminal.scheduleUpdate();

        // 减少等待时间
        if (i % 10 == 0)
        {
            QTest::qWait(1);
        }

        if (i % 20 == 0)
        {
            performGarbageCollection();
        }
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "Long running operations test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory, static_cast<size_t>(50) * 1024 * 1024), // 50MB threshold
             "Potential memory leak detected in long running operations");
}

void TestMemoryLeaks::testProperResourceCleanup()
{
    size_t initialMemory = getCurrentMemoryUsage();

    // 测试资源的正确清理
    {
        // 使用RAII确保资源清理
        SerialProcessor processor;
        VT100Parser parser;

        // 模拟资源分配
        for (int i = 0; i < 100; ++i)
        {
            QByteArray data = QString("Resource test %1\n").arg(i).toLatin1();
            parser.processData(data);

            auto commands = parser.takeAllCommands();
            Q_UNUSED(commands)
        }

        // 对象在作用域结束时自动销毁
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "Resource cleanup test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory), "Resources not properly cleaned up");
}

void TestMemoryLeaks::testExceptionSafetyMemoryLeaks()
{
    size_t initialMemory = getCurrentMemoryUsage();

    // 测试异常安全性
    for (int i = 0; i < 50; ++i)
    {
        try
        {
            SerialProcessor *processor = new SerialProcessor();
            VT100Parser *parser = new VT100Parser();

            // 模拟可能抛出异常的操作
            processor->openPort("", -1); // 无效参数

            // 即使没有异常，也要清理资源
            delete parser;
            delete processor;
        }
        catch (...)
        {
            // 如果有异常，确保不会导致内存泄漏
            // 在实际代码中应该有适当的异常处理
            // 这里故意捕获所有异常以测试异常安全性
            qDebug() << "Exception caught in memory leak test - this is expected for testing";
        }

        if (i % 10 == 0)
        {
            performGarbageCollection();
        }
    }

    performGarbageCollection();
    size_t finalMemory = getCurrentMemoryUsage();

    qDebug() << "Exception safety test - Initial memory:" << initialMemory << "Final memory:" << finalMemory
             << "Difference:" << (finalMemory - initialMemory);

    QVERIFY2(!isMemoryLeakDetected(initialMemory, finalMemory), "Memory leak detected in exception scenarios");
}

// 辅助方法实现
size_t TestMemoryLeaks::getCurrentMemoryUsage()
{
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc)))
    {
        return pmc.WorkingSetSize;
    }
#endif
    return 0; // 如果无法获取，返回0
}

void TestMemoryLeaks::performGarbageCollection()
{
    // 强制Qt进行事件处理和清理
    QApplication::processEvents();
    QThread::msleep(10);
    QApplication::processEvents();
}

bool TestMemoryLeaks::isMemoryLeakDetected(size_t initialMemory, size_t finalMemory, size_t threshold)
{
    if (initialMemory == 0 || finalMemory == 0)
    {
        return false; // 无法检测，假设没有泄漏
    }

    return (finalMemory > initialMemory + threshold);
}

void TestMemoryLeaks::createAndDestroyObjects(int count)
{
    for (int i = 0; i < count; ++i)
    {
        SerialProcessor *processor = new SerialProcessor();
        VT100Parser *parser = new VT100Parser();

        // 模拟一些操作
        parser->processData("Test data");
        auto commands = parser->takeAllCommands();
        Q_UNUSED(commands)

        delete parser;
        delete processor;
    }
}

#include "test_memory_leaks.moc"
QTEST_MAIN(TestMemoryLeaks)
