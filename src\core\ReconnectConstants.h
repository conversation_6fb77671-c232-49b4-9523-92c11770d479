/**
 * @file ReconnectConstants.h
 * @brief 定义了自动重连策略相关的所有常量，包括重连参数、策略配置和UI控制常量。
 *
 * 本文件包含了SerialT项目中智能重连系统的核心常量定义，涵盖重连尝试次数、
 * 时间间隔、指数退避算法和用户界面配置等方面的参数。
 *
 * @note 这些常量直接影响重连行为和用户体验，修改时需要考虑网络环境和用户期望。
 */
#ifndef RECONNECTCONSTANTS_H
#define RECONNECTCONSTANTS_H

namespace App::Reconnect {

// #############################################################################
// # 默认重连设置常量 (Default Reconnection Settings Constants)
// #############################################################################

/**
 * @brief 默认自动重连功能启用状态
 *
 * 控制应用启动时是否默认启用自动重连功能，提升用户体验。
 */
inline constexpr bool kDefaultAutoReconnectEnabled = true;

/**
 * @brief 默认最大重连尝试次数
 *
 * 连接失败后的默认重连尝试次数，平衡持久性和资源消耗。
 */
inline constexpr int kDefaultMaxReconnectAttempts = 5;

/**
 * @brief 默认重连间隔时间（毫秒）
 *
 * 两次重连尝试之间的默认等待时间，避免过于频繁的重连。
 */
inline constexpr int kDefaultReconnectIntervalMs = 2000;

/**
 * @brief 默认重连间隔递增倍数
 *
 * 指数退避算法的默认倍数，每次失败后间隔时间的增长因子。
 * 1.5倍的增长提供了合理的退避策略。
 */
inline constexpr double kDefaultReconnectIntervalMultiplier = 1.5;

// #############################################################################
// # 扩展重连策略参数常量 (Extended Reconnection Strategy Parameters Constants)
// #############################################################################

/**
 * @brief 允许的最小重连尝试次数
 *
 * 重连尝试次数的下限，确保至少进行一次重连尝试。
 */
inline constexpr int kMinReconnectAttempts = 1;

/**
 * @brief 允许的最大重连尝试次数
 *
 * 重连尝试次数的上限，防止无限重连消耗系统资源。
 * 注意：此值不同于 kDefaultMaxReconnectAttempts，这是系统允许的绝对上限。
 */
inline constexpr int kMaxReconnectAttempts = 10;

/**
 * @brief 允许的最小重连间隔时间（毫秒）
 *
 * 重连间隔的下限，防止过于频繁的重连尝试影响系统性能。
 */
inline constexpr int kMinReconnectIntervalMs = 500;

/**
 * @brief 允许的最大重连间隔时间（毫秒）
 *
 * 重连间隔的上限，避免用户等待时间过长。
 */
inline constexpr int kMaxReconnectIntervalMs = 10000;

/**
 * @brief 允许的最小重连间隔倍数
 *
 * 指数退避算法倍数的下限，1.0表示不增长（固定间隔）。
 */
inline constexpr double kMinReconnectMultiplier = 1.0;

/**
 * @brief 允许的最大重连间隔倍数
 *
 * 指数退避算法倍数的上限，防止间隔时间增长过快。
 */
inline constexpr double kMaxReconnectMultiplier = 3.0;

// #############################################################################
// # UI与对话框相关常量 (UI & Dialog-related Constants)
// #############################################################################

/**
 * @brief 重连间隔调整步长
 *
 * 在设置对话框中调整重连间隔时的步长值，提供合适的精度。
 */
inline constexpr double kReconnectIntervalStep = 0.1;

/**
 * @brief 重连间隔显示小数位数
 *
 * 在用户界面中显示重连间隔时的小数位数，平衡精度和可读性。
 */
inline constexpr int kReconnectIntervalDecimals = 1;

} // namespace App::Reconnect

#endif // RECONNECTCONSTANTS_H
