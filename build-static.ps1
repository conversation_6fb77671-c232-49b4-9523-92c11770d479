#requires -Version 5.1
# build-static.ps1
#
# A self-contained script to build the project statically using MSVC.
# It automatically activates the Visual Studio compilation environment.

# --- Script Configuration ---
$ErrorActionPreference = "Stop" # Exit on the first error

# --- Project Configuration ---
$BuildType = "Release"
$Qt6StaticPath = "D:\qt-self-built\qt-6.8.3-static"
$Qt6Dir = Join-Path $Qt6StaticPath "lib\cmake\Qt6"
$BuildDir = "build-static"

# --- Find and Activate Visual Studio Environment ---
try {
    # Use vswhere.exe to find the latest Visual Studio installation, which is the most reliable method.
    $vswherePath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (-not (Test-Path $vswherePath)) {
        throw "vswhere.exe not found at '$vswherePath'. Please ensure Visual Studio is installed correctly."
    }

    # Find the installation path that includes the C++ compiler tools.
    $vsInstallPath = & $vswherePath -latest -property installationPath -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64
    
    if (-not $vsInstallPath) {
        throw "vswhere.exe did not find a Visual Studio installation with C++ build tools."
    }

    $vsDevShellModule = Join-Path $vsInstallPath "Common7\Tools\Microsoft.VisualStudio.DevShell.dll"
    if (-not (Test-Path $vsDevShellModule)) {
        throw "Visual Studio Developer Shell module not found at '$vsDevShellModule'."
    }
    
    Import-Module $vsDevShellModule
    
    # Enter the x64 developer environment
    Enter-VsDevShell -VsInstallPath $vsInstallPath -SkipAutomaticLocation -DevCmdArguments "-arch=amd64"

    Write-Host "Visual Studio x64 build environment activated successfully." -ForegroundColor Green
}
catch {
    Write-Error "Failed to activate Visual Studio environment. $_"
    exit 1
}

# --- Build Process ---
Write-Host "`n--- Starting static build ---"

# 1. Clean up the old build directory
if (Test-Path $BuildDir) {
    Write-Host "Removing old build directory: $BuildDir"
    Remove-Item -Recurse -Force $BuildDir
}

# 2. Run CMake configuration
Write-Host "`nConfiguring project for static build..."
$cmakeArgs = @(
    "-B", $BuildDir,
    "-S", ".",
    "-G", "Ninja",
    "-DCMAKE_BUILD_TYPE=$BuildType",
    "-DSTATIC_BUILD=$true",
    "-DBUILD_TESTING=OFF", # Disable building tests for faster static builds
    "-DQt6_DIR=$Qt6Dir"
)
& cmake $cmakeArgs
if ($LASTEXITCODE -ne 0) {
    throw "CMake configuration failed with exit code $LASTEXITCODE."
}

# 3. Run the build
Write-Host "`nBuilding the project..."
& cmake --build $BuildDir
if ($LASTEXITCODE -ne 0) {
    throw "CMake build failed with exit code $LASTEXITCODE."
}

Write-Host "`nStatic build completed successfully! Executable is in the `"$BuildDir`" directory." -ForegroundColor Green
