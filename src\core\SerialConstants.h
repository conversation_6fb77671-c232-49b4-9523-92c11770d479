/**
 * @file SerialConstants.h
 * @brief 定义了所有与串口通信相关的默认值和常量。
 */
#ifndef SERIALCONSTANTS_H
#define SERIALCONSTANTS_H

#include <array>

namespace App::Serial {
    inline constexpr int kDefaultBaudRate = 115200;
    inline const std::array<const char *, 12> kBaudRateList = {"1200",   "2400",   "4800",   "9600",   "19200",  "38400",
                                                               "57600",  "115200", "230400", "460800", "821600", "921600"};
    inline constexpr bool kDefaultSerialEchoEnabled = false;
    inline constexpr int kDefaultNewLineMode = 0; // 默认为 \\r\\n (CrLf)

} // namespace App::Serial

#endif // SERIALCONSTANTS_H
