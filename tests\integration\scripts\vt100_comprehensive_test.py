#!/usr/bin/env python3
"""
VT100控制序列综合测试
全面测试SerialT对各种VT100控制序列的支持和渲染效果
"""

import sys
import time
import serial
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from config import SERIAL_CONFIG, VT100_SEQUENCES

class VT100ComprehensiveTester:
    """VT100综合测试器"""
    
    def __init__(self, port, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        
    def connect(self):
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1.0
            )
            print(f"✅ Connected to {self.port}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.serial and self.serial.is_open:
            self.serial.close()
            print("🔌 Disconnected")
    
    def send_sequence(self, sequence, description, delay=0.5):
        """发送VT100序列"""
        if not self.serial or not self.serial.is_open:
            print("❌ Serial port not connected")
            return False
        
        try:
            self.serial.write(sequence.encode('utf-8'))
            self.serial.flush()
            print(f"📤 {description}: {repr(sequence)}")
            time.sleep(delay)
            return True
        except Exception as e:
            print(f"❌ Failed to send {description}: {e}")
            return False
    
    def test_cursor_control(self):
        """测试光标控制"""
        print("\n🎯 Testing Cursor Control")
        print("=" * 40)
        
        tests = [
            (VT100_SEQUENCES['CLEAR_SCREEN'], "Clear screen"),
            (VT100_SEQUENCES['CURSOR_HOME'], "Cursor to home"),
            ("Line 1: Testing cursor movement\n", "Write line 1"),
            ("Line 2: Before cursor moves\n", "Write line 2"),
            (VT100_SEQUENCES['CURSOR_UP'], "Move cursor up"),
            (VT100_SEQUENCES['CURSOR_UP'], "Move cursor up again"),
            (VT100_SEQUENCES['CURSOR_RIGHT'] * 10, "Move cursor right 10 positions"),
            ("INSERTED", "Insert text at cursor position"),
            ("\n\n", "Move to new lines"),
        ]
        
        for sequence, description in tests:
            self.send_sequence(sequence, description, 0.8)
        
        return True
    
    def test_colors_and_attributes(self):
        """测试颜色和文本属性"""
        print("\n🌈 Testing Colors and Text Attributes")
        print("=" * 40)
        
        # 前景色测试
        colors = [
            ('FG_RED', 'Red text'),
            ('FG_GREEN', 'Green text'),
            ('FG_YELLOW', 'Yellow text'),
            ('FG_BLUE', 'Blue text'),
            ('FG_MAGENTA', 'Magenta text'),
            ('FG_CYAN', 'Cyan text'),
            ('FG_WHITE', 'White text'),
        ]
        
        self.send_sequence("\n--- Foreground Colors ---\n", "Section header")
        
        for color_key, description in colors:
            sequence = VT100_SEQUENCES[color_key] + f"{description}" + VT100_SEQUENCES['RESET'] + "\n"
            self.send_sequence(sequence, f"Test {description}")
        
        # 背景色测试
        self.send_sequence("\n--- Background Colors ---\n", "Section header")
        
        bg_colors = [
            ('BG_RED', 'Red background'),
            ('BG_GREEN', 'Green background'),
            ('BG_YELLOW', 'Yellow background'),
            ('BG_BLUE', 'Blue background'),
            ('BG_MAGENTA', 'Magenta background'),
            ('BG_CYAN', 'Cyan background'),
            ('BG_WHITE', 'White background'),
        ]
        
        for color_key, description in bg_colors:
            sequence = VT100_SEQUENCES[color_key] + f" {description} " + VT100_SEQUENCES['RESET'] + "\n"
            self.send_sequence(sequence, f"Test {description}")
        
        # 文本属性测试
        self.send_sequence("\n--- Text Attributes ---\n", "Section header")
        
        attributes = [
            ('BOLD', 'Bold text'),
            ('UNDERLINE', 'Underlined text'),
            ('REVERSE', 'Reversed text'),
        ]
        
        for attr_key, description in attributes:
            sequence = VT100_SEQUENCES[attr_key] + f"{description}" + VT100_SEQUENCES['RESET'] + "\n"
            self.send_sequence(sequence, f"Test {description}")
        
        return True
    
    def test_screen_control(self):
        """测试屏幕控制"""
        print("\n🖥️ Testing Screen Control")
        print("=" * 40)
        
        # 填充一些内容
        self.send_sequence("\n--- Screen Control Test ---\n", "Section header")
        for i in range(5):
            self.send_sequence(f"Line {i+1}: This line will be cleared\n", f"Write line {i+1}")
        
        time.sleep(1)
        
        # 测试清除功能
        tests = [
            (VT100_SEQUENCES['CURSOR_UP'] * 3, "Move cursor up 3 lines"),
            (VT100_SEQUENCES['CLEAR_LINE'], "Clear current line"),
            (VT100_SEQUENCES['CURSOR_DOWN'], "Move cursor down"),
            (VT100_SEQUENCES['CLEAR_TO_END'], "Clear from cursor to end of screen"),
            ("\n\nScreen control test completed\n", "Write completion message"),
        ]
        
        for sequence, description in tests:
            self.send_sequence(sequence, description, 1.0)
        
        return True
    
    def test_complex_sequences(self):
        """测试复杂的VT100序列组合"""
        print("\n🎭 Testing Complex VT100 Sequences")
        print("=" * 40)
        
        # 创建一个彩色表格
        self.send_sequence(VT100_SEQUENCES['CLEAR_SCREEN'], "Clear screen")
        self.send_sequence(VT100_SEQUENCES['CURSOR_HOME'], "Cursor to home")
        
        # 表格标题
        header = (VT100_SEQUENCES['BOLD'] + VT100_SEQUENCES['FG_WHITE'] + VT100_SEQUENCES['BG_BLUE'] +
                 "  VT100 Feature Test Table  " + VT100_SEQUENCES['RESET'] + "\n\n")
        self.send_sequence(header, "Table header")
        
        # 表格内容
        table_rows = [
            ("Feature", "Status", "Color"),
            ("Cursor Control", "PASS", "GREEN"),
            ("Text Colors", "PASS", "GREEN"),
            ("Background Colors", "PASS", "GREEN"),
            ("Text Attributes", "PASS", "GREEN"),
            ("Screen Control", "PASS", "GREEN"),
        ]
        
        for i, (feature, status, color) in enumerate(table_rows):
            if i == 0:  # Header row
                row = (VT100_SEQUENCES['BOLD'] + VT100_SEQUENCES['UNDERLINE'] +
                      f"{feature:<20} {status:<10} {color:<10}" + VT100_SEQUENCES['RESET'] + "\n")
            else:  # Data rows
                color_code = VT100_SEQUENCES['FG_GREEN'] if status == "PASS" else VT100_SEQUENCES['FG_RED']
                row = f"{feature:<20} {color_code}{status}{VT100_SEQUENCES['RESET']:<10} {color:<10}\n"
            
            self.send_sequence(row, f"Table row {i+1}")
        
        # 添加一些装饰
        self.send_sequence("\n" + "="*50 + "\n", "Separator line")
        
        # 测试光标定位
        self.send_sequence(VT100_SEQUENCES['CURSOR_POSITION'](10, 25), "Position cursor at (10,25)")
        self.send_sequence(VT100_SEQUENCES['FG_YELLOW'] + "← Positioned text" + VT100_SEQUENCES['RESET'], "Positioned text")
        
        self.send_sequence("\n\n" + VT100_SEQUENCES['FG_CYAN'] + "Complex VT100 test completed!" + VT100_SEQUENCES['RESET'] + "\n", "Completion message")
        
        return True
    
    def test_stress_sequences(self):
        """测试大量VT100序列的性能"""
        print("\n⚡ Testing VT100 Performance")
        print("=" * 40)
        
        self.send_sequence("\n--- Performance Test ---\n", "Section header")
        self.send_sequence("Sending rapid color changes...\n", "Info message")
        
        # 快速颜色变化
        colors = ['FG_RED', 'FG_GREEN', 'FG_BLUE', 'FG_YELLOW', 'FG_MAGENTA', 'FG_CYAN']
        
        for i in range(20):
            color = colors[i % len(colors)]
            text = VT100_SEQUENCES[color] + f"Color {i+1:02d} " + VT100_SEQUENCES['RESET']
            self.send_sequence(text, f"Rapid color {i+1}", 0.1)
            
            if (i + 1) % 6 == 0:
                self.send_sequence("\n", "New line", 0.1)
        
        self.send_sequence("\nPerformance test completed!\n", "Completion message")
        
        return True
    
    def run_all_tests(self):
        """运行所有VT100测试"""
        print("🧪 VT100 Comprehensive Test Suite")
        print("=" * 50)
        print("This test will verify SerialT's VT100 support")
        print("Please observe the terminal output in SerialT")
        print()
        
        if not self.connect():
            return False
        
        try:
            tests = [
                ("Cursor Control", self.test_cursor_control),
                ("Colors and Attributes", self.test_colors_and_attributes),
                ("Screen Control", self.test_screen_control),
                ("Complex Sequences", self.test_complex_sequences),
                ("Performance Test", self.test_stress_sequences),
            ]
            
            results = []
            for test_name, test_func in tests:
                print(f"\n🔄 Running {test_name}...")
                try:
                    result = test_func()
                    results.append((test_name, result))
                    if result:
                        print(f"✅ {test_name} completed")
                    else:
                        print(f"❌ {test_name} failed")
                except Exception as e:
                    print(f"❌ {test_name} error: {e}")
                    results.append((test_name, False))
                
                # 测试间暂停
                time.sleep(2)
            
            # 总结结果
            print(f"\n📊 Test Results Summary")
            print("=" * 30)
            passed = sum(1 for _, result in results if result)
            total = len(results)
            
            for test_name, result in results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {test_name}: {status}")
            
            print(f"\nTotal: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All VT100 tests passed!")
            else:
                print("⚠️ Some VT100 tests failed")
            
            return passed == total
            
        finally:
            self.disconnect()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='VT100 Comprehensive Test')
    parser.add_argument('--port', default=SERIAL_CONFIG['TEST_TOOL_PORT'], help='Serial port')
    parser.add_argument('--baudrate', type=int, default=SERIAL_CONFIG['DEFAULT_BAUDRATE'], help='Baud rate')
    
    args = parser.parse_args()
    
    print("🎨 VT100 Comprehensive Test for SerialT")
    print("=" * 50)
    print(f"Port: {args.port}")
    print(f"Baud rate: {args.baudrate}")
    print()
    print("⚠️ Make sure SerialT is running and connected to COM30")
    print("   You should see colorful output in SerialT terminal")
    print()
    
    input("Press Enter to start VT100 tests...")
    
    tester = VT100ComprehensiveTester(args.port, args.baudrate)
    
    try:
        success = tester.run_all_tests()
        return success
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
