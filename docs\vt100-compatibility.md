# VT100兼容性设计文档

## 概述

SerialT作为串口终端工具，需要处理VT100终端控制序列。为了避免应用程序快捷键与VT100控制序列冲突，我们采用了智能的快捷键设计策略。

## VT100控制序列分析

### 常用的VT100 Ctrl组合键
| 组合键 | VT100功能 | 冲突风险 | SerialT解决方案 |
|--------|-----------|----------|----------------|
| `Ctrl+@` | NUL字符 | 低 | 不使用 |
| `Ctrl+A` | SOH，行首 | 中 | 不使用 |
| `Ctrl+B` | STX，后退 | 中 | 不使用 |
| `Ctrl+C` | ETX，中断 | 高 | 改用Alt+C |
| `Ctrl+D` | EOT，EOF | 高 | 改用Alt+D |
| `Ctrl+E` | ENQ，行尾 | 中 | 不使用 |
| `Ctrl+F` | ACK，前进 | 高 | 改用Alt+F |
| `Ctrl+G` | BEL，响铃 | 中 | 不使用 |
| `Ctrl+H` | BS，退格 | 高 | 不使用 |
| `Ctrl+I` | HT，制表符 | 高 | 不使用 |
| `Ctrl+J` | LF，换行 | 高 | 不使用 |
| `Ctrl+K` | VT，删除到行尾 | 高 | 不使用 |
| `Ctrl+L` | FF，清屏 | 高 | 改用Alt+L |
| `Ctrl+M` | CR，回车 | 高 | 不使用 |
| `Ctrl+N` | SO，下一行 | 中 | 不使用 |
| `Ctrl+O` | SI，输出控制 | 中 | 改用Alt+C |
| `Ctrl+P` | DLE，上一行 | 中 | 不使用 |
| `Ctrl+Q` | DC1，XON | 中 | 保留（应用退出） |
| `Ctrl+R` | DC2，重绘 | 中 | 不使用 |
| `Ctrl+S` | DC3，XOFF | 中 | 保留（设置） |
| `Ctrl+T` | DC4，交换字符 | 中 | 改用Alt+T |
| `Ctrl+U` | NAK，删除行 | 高 | 不使用 |
| `Ctrl+V` | SYN，字面输入 | 中 | 不使用 |
| `Ctrl+W` | ETB，删除单词 | 高 | 改用Alt+W |
| `Ctrl+X` | CAN，取消 | 中 | 不使用 |
| `Ctrl+Y` | EM，粘贴 | 中 | 不使用 |
| `Ctrl+Z` | SUB，挂起 | 高 | 不使用 |

### 功能键分析
| 功能键 | VT100功能 | 冲突风险 | SerialT解决方案 |
|--------|-----------|----------|----------------|
| `F1-F12` | 功能键序列 | 中 | 谨慎使用 |
| `F5` | 刷新（某些终端） | 中 | 改用Alt+R |
| `Esc` | 转义序列开始 | 高 | 不使用 |

## SerialT快捷键设计策略

### 1. Alt组合键优先
- **原理**：VT100标准主要使用Ctrl组合键，Alt组合键冲突风险极低
- **优势**：现代应用广泛使用Alt组合键，用户习惯良好
- **实现**：所有主要功能使用Alt组合键

### 2. Ctrl+Shift组合键作为补充
- **原理**：VT100很少使用Ctrl+Shift组合键
- **用途**：主题切换、日志功能等不频繁操作
- **安全性**：冲突风险很低

### 3. 保留关键的Ctrl组合键
- **Ctrl+Q**：应用程序退出（通用约定）
- **Ctrl+,**：设置对话框（现代应用约定）
- **Ctrl+=/-/0**：缩放操作（通用约定）

### 4. 智能快捷键管理
- **动态调整**：可根据连接状态调整快捷键
- **用户控制**：预留接口供用户自定义
- **冲突检测**：自动检测和处理冲突

## 实现细节

### 快捷键分类
```cpp
// 可能与VT100冲突的快捷键（使用Alt组合键）
QList<QAction*> m_vt100ConflictActions;

// 安全的快捷键（使用Ctrl+Shift或特殊组合键）
QList<QAction*> m_safeActions;
```

### 动态管理
```cpp
void updateShortcutsForConnectionState(bool connected);
void enableGlobalShortcuts(bool enabled);
```

## 测试验证

### 兼容性测试场景
1. **基本VT100序列**：确保常用控制序列正常工作
2. **快捷键响应**：验证Alt组合键正常响应
3. **冲突检测**：测试是否存在意外冲突
4. **用户体验**：确保快捷键直观易用

### 测试用例
- 连接串口后测试各种VT100控制序列
- 验证Alt组合键在连接状态下的响应
- 测试Ctrl组合键是否正确传递给终端
- 验证快捷键在不同输入法下的表现

## 未来扩展

### 用户自定义
- 允许用户自定义快捷键
- 提供冲突检测和警告
- 支持快捷键配置导入/导出

### 高级功能
- 上下文相关的快捷键
- 模式切换（终端模式 vs 应用模式）
- 快捷键学习和提示系统

## 最佳实践建议

### 对于开发者
1. 新增快捷键时优先考虑Alt组合键
2. 避免使用VT100常用的Ctrl组合键
3. 为快捷键提供清晰的文档说明
4. 考虑国际化和不同键盘布局

### 对于用户
1. 熟悉Alt组合键的使用
2. 在VT100应用中谨慎使用Ctrl组合键
3. 遇到冲突时优先考虑终端功能
4. 及时反馈快捷键使用体验

## 总结

通过采用Alt组合键为主、Ctrl+Shift为辅的设计策略，SerialT成功避免了与VT100控制序列的冲突，同时保持了良好的用户体验。智能快捷键管理系统为未来的功能扩展提供了灵活的基础。
