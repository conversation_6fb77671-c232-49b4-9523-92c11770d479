#include <QSignalSpy>
#include <QtTest/QtTest>
#include "../../src/VT100Parser.h"

class TestVT100Parser : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基础功能测试
    void testConstructor();
    void testBasicTextParsing();
    void testNewLineNormalization();
    void testControlCharacters();

    // VT100控制序列测试
    void testSGRColors_data();
    void testSGRColors();
    void testSGRAttributes();
    void testCursorMovement_data();
    void testCursorMovement();
    void testCursorPositioning();
    void testEraseCommands_data();
    void testEraseCommands();

    // 复杂序列测试
    void testMixedContent();
    void testInvalidSequences();
    void testPartialSequences();
    void testCommandBuffer();

    // 线程安全测试
    void testThreadSafety();

private:
    VT100Parser *m_parser;

    // 辅助方法
    void processAndVerify(const QByteArray &input, const QList<TerminalCommand> &expected);
    TerminalCommand createCommand(TerminalCommand::CommandType type, const QVariantList &params = {});
};

void TestVT100Parser::initTestCase()
{
    qDebug() << "Starting VT100Parser tests";
}

void TestVT100Parser::cleanupTestCase()
{
    qDebug() << "VT100Parser tests completed";
}

void TestVT100Parser::init()
{
    m_parser = new VT100Parser(this);
}

void TestVT100Parser::cleanup()
{
    if (m_parser)
    {
        delete m_parser;
        m_parser = nullptr;
    }
}

void TestVT100Parser::testConstructor()
{
    QVERIFY(m_parser != nullptr);
    QVERIFY(!m_parser->hasPendingCommands());

    // 测试初始状态
    QList<TerminalCommand> commands = m_parser->takeAllCommands();
    QVERIFY(commands.isEmpty());
}

void TestVT100Parser::testBasicTextParsing()
{
    // 测试基本文本解析
    QByteArray input = "Hello, World!";
    m_parser->processData(input);

    QList<TerminalCommand> commands = m_parser->takeAllCommands();
    QCOMPARE(commands.size(), input.size());

    for (int i = 0; i < input.size(); ++i)
    {
        QCOMPARE(commands[i].type, TerminalCommand::PrintableChar);
        QCOMPARE(commands[i].params.size(), 1);
        QCOMPARE(commands[i].params[0].toChar(), QChar(input[i]));
    }
}

void TestVT100Parser::testNewLineNormalization()
{
    // 测试换行符规范化
    struct TestCase
    {
        QByteArray input;
        QString expectedOutput;
    };

    QList<TestCase> testCases = {{"Hello\r\nWorld", "Hello\nWorld"},
                                 {"Hello\rWorld", "Hello\nWorld"},
                                 {"Hello\nWorld", "Hello\nWorld"},
                                 {"A\r\nB\rC\nD", "A\nB\nC\nD"}};

    for (const auto &testCase : testCases)
    {
        m_parser->processData(testCase.input);
        QList<TerminalCommand> commands = m_parser->takeAllCommands();

        QString result;
        for (const auto &cmd : commands)
        {
            if (cmd.type == TerminalCommand::PrintableChar)
            {
                result.append(cmd.params[0].toChar());
            }
        }

        QCOMPARE(result, testCase.expectedOutput);
    }
}

void TestVT100Parser::testControlCharacters()
{
    // 测试控制字符
    struct TestCase
    {
        QByteArray input;
        TerminalCommand::CommandType expectedType;
    };

    QList<TestCase> testCases = {{"\b", TerminalCommand::Backspace},
                                 {"\t", TerminalCommand::HorizontalTab},
                                 {"\a", TerminalCommand::Bell},
                                 // {"\f", TerminalCommand::FormFeed},  // 默认关闭'\f'
                                 {"\v", TerminalCommand::VerticalTab}};

    for (const auto &testCase : testCases)
    {
        m_parser->processData(testCase.input);
        QList<TerminalCommand> commands = m_parser->takeAllCommands();

        QCOMPARE(commands.size(), 1);
        QCOMPARE(commands[0].type, testCase.expectedType);
    }
}

void TestVT100Parser::testSGRColors_data()
{
    QTest::addColumn<int>("colorCode");
    QTest::addColumn<TerminalCommand::CommandType>("commandType");
    QTest::addColumn<QColor>("expectedColor");

    // 前景色测试
    QTest::newRow("fg_black") << 30 << TerminalCommand::ForegroundColorChanged << QColor(Qt::black);
    QTest::newRow("fg_red") << 31 << TerminalCommand::ForegroundColorChanged << QColor(Qt::red);
    QTest::newRow("fg_green") << 32 << TerminalCommand::ForegroundColorChanged << QColor(Qt::green);
    QTest::newRow("fg_yellow") << 33 << TerminalCommand::ForegroundColorChanged << QColor(Qt::yellow);
    QTest::newRow("fg_blue") << 34 << TerminalCommand::ForegroundColorChanged << QColor(Qt::blue);
    QTest::newRow("fg_magenta") << 35 << TerminalCommand::ForegroundColorChanged << QColor(Qt::magenta);
    QTest::newRow("fg_cyan") << 36 << TerminalCommand::ForegroundColorChanged << QColor(Qt::cyan);
    QTest::newRow("fg_white") << 37 << TerminalCommand::ForegroundColorChanged << QColor(Qt::white);

    // 背景色测试
    QTest::newRow("bg_black") << 40 << TerminalCommand::BackgroundColorChanged << QColor(Qt::black);
    QTest::newRow("bg_red") << 41 << TerminalCommand::BackgroundColorChanged << QColor(Qt::red);
    QTest::newRow("bg_green") << 42 << TerminalCommand::BackgroundColorChanged << QColor(Qt::green);
    QTest::newRow("bg_yellow") << 43 << TerminalCommand::BackgroundColorChanged << QColor(Qt::yellow);
    QTest::newRow("bg_blue") << 44 << TerminalCommand::BackgroundColorChanged << QColor(Qt::blue);
    QTest::newRow("bg_magenta") << 45 << TerminalCommand::BackgroundColorChanged << QColor(Qt::magenta);
    QTest::newRow("bg_cyan") << 46 << TerminalCommand::BackgroundColorChanged << QColor(Qt::cyan);
    QTest::newRow("bg_white") << 47 << TerminalCommand::BackgroundColorChanged << QColor(Qt::white);
}

void TestVT100Parser::testSGRColors()
{
    QFETCH(int, colorCode);
    QFETCH(TerminalCommand::CommandType, commandType);
    QFETCH(QColor, expectedColor);

    QByteArray input = QString("\x1b[%1m").arg(colorCode).toLatin1();
    m_parser->processData(input);

    QList<TerminalCommand> commands = m_parser->takeAllCommands();
    QCOMPARE(commands.size(), 1);
    QCOMPARE(commands[0].type, commandType);
    QCOMPARE(commands[0].params.size(), 1);
    QCOMPARE(commands[0].params[0].value<QColor>(), expectedColor);
}

void TestVT100Parser::testSGRAttributes()
{
    // 测试SGR属性
    struct TestCase
    {
        QByteArray input;
        TerminalCommand::CommandType expectedType;
        QVariantList expectedParams;
    };

    QList<TestCase> testCases = {{"\x1b[0m", TerminalCommand::AttributesReset, {}},
                                 {"\x1b[m", TerminalCommand::AttributesReset, {}}, // 空参数默认为0
                                 {"\x1b[1m", TerminalCommand::BoldChanged, {true}},
                                 {"\x1b[22m", TerminalCommand::BoldChanged, {false}}};

    for (const auto &testCase : testCases)
    {
        m_parser->processData(testCase.input);
        QList<TerminalCommand> commands = m_parser->takeAllCommands();

        QCOMPARE(commands.size(), 1);
        QCOMPARE(commands[0].type, testCase.expectedType);
        QCOMPARE(commands[0].params, testCase.expectedParams);
    }
}

void TestVT100Parser::testCursorMovement_data()
{
    QTest::addColumn<QByteArray>("input");
    QTest::addColumn<TerminalCommand::CommandType>("expectedType");
    QTest::addColumn<int>("expectedCount");

    QTest::newRow("cursor_up_default") << QByteArray("\x1b[A") << TerminalCommand::CursorUp << 1;
    QTest::newRow("cursor_up_5") << QByteArray("\x1b[5A") << TerminalCommand::CursorUp << 5;
    QTest::newRow("cursor_down_default") << QByteArray("\x1b[B") << TerminalCommand::CursorDown << 1;
    QTest::newRow("cursor_down_3") << QByteArray("\x1b[3B") << TerminalCommand::CursorDown << 3;
    QTest::newRow("cursor_forward_default") << QByteArray("\x1b[C") << TerminalCommand::CursorForward << 1;
    QTest::newRow("cursor_forward_10") << QByteArray("\x1b[10C") << TerminalCommand::CursorForward << 10;
    QTest::newRow("cursor_back_default") << QByteArray("\x1b[D") << TerminalCommand::CursorBack << 1;
    QTest::newRow("cursor_back_2") << QByteArray("\x1b[2D") << TerminalCommand::CursorBack << 2;
}

void TestVT100Parser::testCursorMovement()
{
    QFETCH(QByteArray, input);
    QFETCH(TerminalCommand::CommandType, expectedType);
    QFETCH(int, expectedCount);

    m_parser->processData(input);
    QList<TerminalCommand> commands = m_parser->takeAllCommands();

    QCOMPARE(commands.size(), 1);
    QCOMPARE(commands[0].type, expectedType);
    QCOMPARE(commands[0].params.size(), 1);
    QCOMPARE(commands[0].params[0].toInt(), expectedCount);
}

void TestVT100Parser::testCursorPositioning()
{
    // 测试光标定位
    struct TestCase
    {
        QByteArray input;
        int expectedRow;
        int expectedCol;
    };

    QList<TestCase> testCases = {
        {"\x1b[H", 0, 0},     // 默认位置 (1,1) -> (0,0)
        {"\x1b[5;10H", 4, 9}, // 第5行第10列 -> (4,9)
        {"\x1b[;5H", 0, 4},   // 默认行，第5列 -> (0,4)
        {"\x1b[10;H", 9, 0},  // 第10行，默认列 -> (9,0)
        {"\x1b[1;1H", 0, 0}   // 明确的第1行第1列 -> (0,0)
    };

    for (const auto &testCase : testCases)
    {
        m_parser->processData(testCase.input);
        QList<TerminalCommand> commands = m_parser->takeAllCommands();

        QCOMPARE(commands.size(), 1);
        QCOMPARE(commands[0].type, TerminalCommand::SetCursorPosition);
        QCOMPARE(commands[0].params.size(), 2);
        QCOMPARE(commands[0].params[0].toInt(), testCase.expectedRow);
        QCOMPARE(commands[0].params[1].toInt(), testCase.expectedCol);
    }
}

void TestVT100Parser::testEraseCommands_data()
{
    QTest::addColumn<QByteArray>("input");
    QTest::addColumn<TerminalCommand::CommandType>("expectedType");
    QTest::addColumn<int>("expectedMode");

    // 擦除显示测试
    QTest::newRow("erase_display_default") << QByteArray("\x1b[J") << TerminalCommand::EraseInDisplay << 0;
    QTest::newRow("erase_display_0") << QByteArray("\x1b[0J") << TerminalCommand::EraseInDisplay << 0;
    QTest::newRow("erase_display_1") << QByteArray("\x1b[1J") << TerminalCommand::EraseInDisplay << 1;
    QTest::newRow("erase_display_2") << QByteArray("\x1b[2J") << TerminalCommand::EraseInDisplay << 2;

    // 擦除行测试
    QTest::newRow("erase_line_default") << QByteArray("\x1b[K") << TerminalCommand::EraseInLine << 0;
    QTest::newRow("erase_line_0") << QByteArray("\x1b[0K") << TerminalCommand::EraseInLine << 0;
    QTest::newRow("erase_line_1") << QByteArray("\x1b[1K") << TerminalCommand::EraseInLine << 1;
    QTest::newRow("erase_line_2") << QByteArray("\x1b[2K") << TerminalCommand::EraseInLine << 2;
}

void TestVT100Parser::testEraseCommands()
{
    QFETCH(QByteArray, input);
    QFETCH(TerminalCommand::CommandType, expectedType);
    QFETCH(int, expectedMode);

    m_parser->processData(input);
    QList<TerminalCommand> commands = m_parser->takeAllCommands();

    QCOMPARE(commands.size(), 1);
    QCOMPARE(commands[0].type, expectedType);
    QCOMPARE(commands[0].params.size(), 1);
    QCOMPARE(commands[0].params[0].toInt(), expectedMode);
}

void TestVT100Parser::testMixedContent()
{
    // 测试混合内容（文本+控制序列）
    QByteArray input = "Hello \x1b[31mRed\x1b[0m World!";
    m_parser->processData(input);

    QList<TerminalCommand> commands = m_parser->takeAllCommands();

    // 验证命令序列
    int index = 0;

    // "Hello "
    for (const char ch : QByteArray("Hello "))
    {
        QCOMPARE(commands[index].type, TerminalCommand::PrintableChar);
        QCOMPARE(commands[index].params[0].toChar(), QChar(ch));
        index++;
    }

    // 红色前景
    QCOMPARE(commands[index].type, TerminalCommand::ForegroundColorChanged);
    QCOMPARE(commands[index].params[0].value<QColor>(), QColor(Qt::red));
    index++;

    // "Red"
    for (const char ch : QByteArray("Red"))
    {
        QCOMPARE(commands[index].type, TerminalCommand::PrintableChar);
        QCOMPARE(commands[index].params[0].toChar(), QChar(ch));
        index++;
    }

    // 重置属性
    QCOMPARE(commands[index].type, TerminalCommand::AttributesReset);
    index++;

    // " World!"
    for (const char ch : QByteArray(" World!"))
    {
        QCOMPARE(commands[index].type, TerminalCommand::PrintableChar);
        QCOMPARE(commands[index].params[0].toChar(), QChar(ch));
        index++;
    }

    QCOMPARE(index, commands.size());
}

void TestVT100Parser::testInvalidSequences()
{
    // 测试无效序列的处理
    QList<QByteArray> invalidSequences = {
        "\x1b[999m",   // 无效的SGR代码
        "\x1b[999A",   // 无效的光标移动
        "\x1b[;;;;;m", // 多个分号
        "\x1b[abc",    // 非数字参数
        "\x1b[",       // 不完整序列
        "\x1b",        // 只有ESC
    };

    for (const auto &seq : invalidSequences)
    {
        m_parser->processData(seq);
        // 无效序列应该被忽略或安全处理，不应该崩溃
        QList<TerminalCommand> commands = m_parser->takeAllCommands();
        // 可能有命令也可能没有，但不应该崩溃
        QVERIFY(true); // 基本的不崩溃测试
    }
}

void TestVT100Parser::testPartialSequences()
{
    // 测试分段发送的序列
    m_parser->processData("\x1b");
    QVERIFY(!m_parser->hasPendingCommands());

    m_parser->processData("[");
    QVERIFY(!m_parser->hasPendingCommands());

    m_parser->processData("31");
    QVERIFY(!m_parser->hasPendingCommands());

    m_parser->processData("m");
    QVERIFY(m_parser->hasPendingCommands());

    QList<TerminalCommand> commands = m_parser->takeAllCommands();
    QCOMPARE(commands.size(), 1);
    QCOMPARE(commands[0].type, TerminalCommand::ForegroundColorChanged);
    QCOMPARE(commands[0].params[0].value<QColor>(), QColor(Qt::red));
}

void TestVT100Parser::testCommandBuffer()
{
    // 测试命令缓冲区功能
    m_parser->processData("ABC");
    QVERIFY(m_parser->hasPendingCommands());

    // 测试takeCommands限制数量
    QList<TerminalCommand> commands = m_parser->takeCommands(2);
    QCOMPARE(commands.size(), 2);
    QVERIFY(m_parser->hasPendingCommands());

    // 取剩余的命令
    commands = m_parser->takeAllCommands();
    QCOMPARE(commands.size(), 1);
    QVERIFY(!m_parser->hasPendingCommands());
}

void TestVT100Parser::testThreadSafety()
{
    // 基础的线程安全测试
    // 在实际应用中，这需要更复杂的多线程测试
    m_parser->processData("Test");

    // 测试信号发射
    QSignalSpy spy(m_parser, &VT100Parser::parseFinished);
    m_parser->processData("More data");

    QCOMPARE(spy.count(), 1);
}

// 辅助方法实现
void TestVT100Parser::processAndVerify(const QByteArray &input, const QList<TerminalCommand> &expected)
{
    m_parser->processData(input);
    QList<TerminalCommand> actual = m_parser->takeAllCommands();

    QCOMPARE(actual.size(), expected.size());

    for (int i = 0; i < expected.size(); ++i)
    {
        QCOMPARE(actual[i].type, expected[i].type);
        QCOMPARE(actual[i].params, expected[i].params);
    }
}

TerminalCommand TestVT100Parser::createCommand(TerminalCommand::CommandType type, const QVariantList &params)
{
    TerminalCommand cmd;
    cmd.type = type;
    cmd.params = params;
    return cmd;
}

#include "test_vt100_parser.moc"
QTEST_MAIN(TestVT100Parser)
