#!/usr/bin/env python3
"""
交互式串口测试工具
允许用户手动输入数据并观察SerialT的反应
"""

import sys
import time
import threading
import serial
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from config import SERIAL_CONFIG, VT100_SEQUENCES

class InteractiveTester:
    """交互式测试器"""
    
    def __init__(self, port, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        self.running = False
        self.receive_thread = None
        
    def connect(self):
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1.0
            )
            print(f"✅ 已连接到 {self.port}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.stop_receiving()
        if self.serial and self.serial.is_open:
            self.serial.close()
            print("🔌 已断开连接")
    
    def start_receiving(self):
        """开始接收数据"""
        self.running = True
        self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
        self.receive_thread.start()
    
    def stop_receiving(self):
        """停止接收数据"""
        self.running = False
        if self.receive_thread:
            self.receive_thread.join(timeout=2)
    
    def _receive_loop(self):
        """接收数据循环"""
        while self.running and self.serial and self.serial.is_open:
            try:
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    if data:
                        try:
                            text = data.decode('utf-8', errors='replace')
                            print(f"📥 接收: {repr(text)}")
                        except:
                            print(f"📥 接收 (hex): {data.hex()}")
                time.sleep(0.01)
            except Exception as e:
                print(f"❌ 接收错误: {e}")
                break
    
    def send_text(self, text):
        """发送文本"""
        if not self.serial or not self.serial.is_open:
            print("❌ 串口未连接")
            return False
        
        try:
            data = text.encode('utf-8')
            self.serial.write(data)
            self.serial.flush()
            print(f"📤 发送: {repr(text)}")
            return True
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 交互式测试帮助:")
        print("直接输入文本 - 发送文本到SerialT")
        print("特殊命令:")
        print("  /help     - 显示此帮助")
        print("  /clear    - 发送清屏序列")
        print("  /red      - 发送红色文本测试")
        print("  /green    - 发送绿色背景测试")
        print("  /bold     - 发送粗体文本测试")
        print("  /cursor   - 发送光标移动测试")
        print("  /large    - 发送大量数据测试")
        print("  /binary   - 发送二进制数据测试")
        print("  /quit     - 退出程序")
        print()
    
    def handle_command(self, command):
        """处理特殊命令"""
        command = command.lower().strip()
        
        if command == '/help':
            self.show_help()
            
        elif command == '/clear':
            self.send_text(VT100_SEQUENCES['CLEAR_SCREEN'])
            self.send_text(VT100_SEQUENCES['CURSOR_HOME'])
            
        elif command == '/red':
            self.send_text(VT100_SEQUENCES['FG_RED'] + "Red text example" + VT100_SEQUENCES['RESET'] + "\n")

        elif command == '/green':
            self.send_text(VT100_SEQUENCES['BG_GREEN'] + "Green background" + VT100_SEQUENCES['RESET'] + "\n")

        elif command == '/bold':
            self.send_text(VT100_SEQUENCES['BOLD'] + "Bold text example" + VT100_SEQUENCES['RESET'] + "\n")
            
        elif command == '/cursor':
            # 光标移动测试
            self.send_text("Cursor movement test:\n")
            time.sleep(0.2)
            self.send_text("A")
            time.sleep(0.2)
            self.send_text(VT100_SEQUENCES['CURSOR_LEFT'])
            time.sleep(0.2)
            self.send_text("B")
            time.sleep(0.2)
            self.send_text(VT100_SEQUENCES['CURSOR_RIGHT'])
            time.sleep(0.2)
            self.send_text("C\n")

        elif command == '/large':
            print("📦 Sending large data...")
            large_text = "Large data test: " + "A" * 1000 + "\n"
            self.send_text(large_text)
            
        elif command == '/binary':
            print("🔢 发送二进制数据...")
            binary_data = bytes(range(256))
            if self.serial and self.serial.is_open:
                self.serial.write(binary_data)
                self.serial.flush()
                print(f"📤 发送二进制数据: {len(binary_data)} 字节")
            
        elif command == '/quit':
            return False
            
        else:
            print(f"❓ 未知命令: {command}")
            print("输入 /help 查看可用命令")
        
        return True
    
    def run_interactive(self):
        """运行交互式会话"""
        print("🎮 交互式测试模式")
        print("=" * 40)
        print(f"连接到: {self.port}")
        print("输入 /help 查看帮助，输入 /quit 退出")
        print()
        
        if not self.connect():
            return False
        
        self.start_receiving()
        
        try:
            while True:
                try:
                    user_input = input("📝 输入: ").strip()
                    
                    if not user_input:
                        continue
                    
                    if user_input.startswith('/'):
                        if not self.handle_command(user_input):
                            break
                    else:
                        # 普通文本，添加换行符
                        if not user_input.endswith('\n'):
                            user_input += '\n'
                        self.send_text(user_input)
                        
                except EOFError:
                    break
                except KeyboardInterrupt:
                    break
                    
        finally:
            self.disconnect()
        
        print("👋 交互式测试结束")
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SerialT 交互式测试工具')
    parser.add_argument('--port', default=SERIAL_CONFIG['TEST_TOOL_PORT'], help='串口名称')
    parser.add_argument('--baudrate', type=int, default=SERIAL_CONFIG['DEFAULT_BAUDRATE'], help='波特率')
    
    args = parser.parse_args()
    
    tester = InteractiveTester(args.port, args.baudrate)
    
    try:
        tester.run_interactive()
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
