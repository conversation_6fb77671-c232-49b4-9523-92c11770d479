#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QEventLoop>
#include <QRandomGenerator>
#include <QThread>
#include <QTimer>
#include <QtTest/QtTest>
#include "../../src/HighPerformanceTerminal.h"
#include "../../src/SerialProcessor.h"
#include "../../src/VT100Parser.h"

class TestLongRunningStability : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 长时间运行稳定性测试
    void testContinuousDataProcessing();
    void testHighFrequencyOperations();
    void testLargeDataBursts();
    void testMixedOperationPatterns();
    void testResourceStabilityUnderLoad();

    // 压力测试
    void testMemoryStabilityUnderLoad();
    void testPerformanceDegradation();
    void testConcurrentOperations();

private:
    HighPerformanceTerminal *m_terminal;
    VT100Parser *m_parser;
    SerialProcessor *m_serialProcessor;

    // 辅助方法
    void runStabilityTest(int durationSeconds, std::function<void()> testFunction);
    QByteArray generateRandomData(int size);
    QByteArray generateVT100Data(int lines);
    void simulateRealWorldUsage(int iterations);
    bool checkSystemStability();
};

void TestLongRunningStability::initTestCase()
{
    qDebug() << "Starting Long Running Stability tests";

    // 确保有QApplication实例
    if (QApplication::instance() == nullptr)
    {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestLongRunningStability::cleanupTestCase()
{
    qDebug() << "Long Running Stability tests completed";
}

void TestLongRunningStability::init()
{
    m_terminal = new HighPerformanceTerminal();
    m_parser = new VT100Parser(this);
    m_serialProcessor = new SerialProcessor(this);

    m_terminal->setParser(m_parser);
    m_terminal->setBufferSize(10000); // 较大的缓冲区
    m_terminal->resize(800, 600);
}

void TestLongRunningStability::cleanup()
{
    if (m_terminal != nullptr)
    {
        delete m_terminal;
        m_terminal = nullptr;
    }

    if (m_parser != nullptr)
    {
        delete m_parser;
        m_parser = nullptr;
    }

    if (m_serialProcessor != nullptr)
    {
        delete m_serialProcessor;
        m_serialProcessor = nullptr;
    }
}

void TestLongRunningStability::testContinuousDataProcessing()
{
    qDebug() << "Testing continuous data processing...";

    QElapsedTimer timer;
    timer.start();

    int totalDataProcessed = 0;
    const int testDurationMs = 10000; // 10秒测试

    while (timer.elapsed() < testDurationMs)
    {
        // 生成连续的数据流
        QByteArray data = generateVT100Data(10);
        m_parser->processData(data);

        // 定期清理命令缓冲区
        if (m_parser->hasPendingCommands())
        {
            auto commands = m_parser->takeAllCommands();
            totalDataProcessed += commands.size();
        }

        // 更新终端显示
        m_terminal->scheduleUpdate();

        // 处理事件
        QApplication::processEvents();

        // 短暂休息以避免100%CPU使用
        QThread::msleep(1);
    }

    qDebug() << "Processed" << totalDataProcessed << "commands in" << timer.elapsed() << "ms";

    // 验证系统仍然稳定
    QVERIFY(checkSystemStability());
    QVERIFY(totalDataProcessed > 0);
}

void TestLongRunningStability::testHighFrequencyOperations()
{
    qDebug() << "Testing high frequency operations...";

    QElapsedTimer timer;
    timer.start();

    int operationCount = 0;
    const int testDurationMs = 5000; // 5秒测试

    while (timer.elapsed() < testDurationMs)
    {
        // 高频率的小数据包
        for (int i = 0; i < 10; ++i)
        {
            QByteArray smallData = QString("Data%1\n").arg(operationCount).toLatin1();
            m_parser->processData(smallData);
            operationCount++;
        }

        // 批量处理命令
        if (m_parser->hasPendingCommands())
        {
            m_parser->takeAllCommands();
        }

        // 模拟串口操作
        m_serialProcessor->setNewLineMode(static_cast<NewLineMode>(operationCount % 3));
        m_serialProcessor->setEchoEnabled(operationCount % 2 == 0);

        QApplication::processEvents();
    }

    qDebug() << "Performed" << operationCount << "operations in" << timer.elapsed() << "ms";
    qDebug() << "Average operations per second:" << (operationCount * 1000.0 / timer.elapsed());

    QVERIFY(checkSystemStability());
    QVERIFY(operationCount > 1000); // 应该能处理大量操作
}

void TestLongRunningStability::testLargeDataBursts()
{
    qDebug() << "Testing large data bursts...";

    QElapsedTimer timer;
    timer.start();

    int burstCount = 0;
    const int testDurationMs = 8000; // 8秒测试

    while (timer.elapsed() < testDurationMs)
    {
        // 生成大数据包
        QByteArray largeData = generateRandomData(5000); // 5KB数据包
        m_parser->processData(largeData);
        burstCount++;

        // 处理命令
        if (m_parser->hasPendingCommands())
        {
            auto commands = m_parser->takeAllCommands();
            qDebug() << "Processed burst" << burstCount << "with" << commands.size() << "commands";
        }

        // 更新显示
        m_terminal->scheduleUpdate();
        QApplication::processEvents();

        // 在大数据包之间稍作休息
        QThread::msleep(100);
    }

    qDebug() << "Processed" << burstCount << "large data bursts";

    QVERIFY(checkSystemStability());
    QVERIFY(burstCount > 0);
}

void TestLongRunningStability::testMixedOperationPatterns()
{
    qDebug() << "Testing mixed operation patterns...";

    QElapsedTimer timer;
    timer.start();

    int patternCycle = 0;
    const int testDurationMs = 12000; // 12秒测试

    while (timer.elapsed() < testDurationMs)
    {
        int pattern = patternCycle % 4;

        switch (pattern)
        {
            case 0: // 小数据包模式
                for (int i = 0; i < 20; ++i)
                {
                    m_parser->processData(QString("Small%1 ").arg(i).toLatin1());
                }
                break;

            case 1:                                     // VT100控制序列模式
                m_parser->processData("\x1b[2J\x1b[H"); // 清屏并回到起始位置
                m_parser->processData("\x1b[31mRed text\x1b[0m\n");
                m_parser->processData("\x1b[42mGreen background\x1b[0m\n");
                break;

            case 2: // 大数据块模式
            {
                QByteArray bigBlock = generateVT100Data(50);
                m_parser->processData(bigBlock);
            }
            break;

            case 3: // 混合模式
                m_parser->processData("Mixed: ");
                m_parser->processData("\x1b[1mBold\x1b[0m ");
                m_parser->processData(generateRandomData(100));
                m_parser->processData("\n");
                break;
        }

        // 定期清理
        if (patternCycle % 10 == 0)
        {
            m_parser->takeAllCommands();
            m_terminal->scheduleUpdate();
        }

        patternCycle++;
        QApplication::processEvents();
        QThread::msleep(10);
    }

    qDebug() << "Completed" << patternCycle << "pattern cycles";

    QVERIFY(checkSystemStability());
    QVERIFY(patternCycle > 100);
}

void TestLongRunningStability::testResourceStabilityUnderLoad()
{
    qDebug() << "Testing resource stability under load...";

    // 创建多个组件实例来增加负载
    QList<VT100Parser *> parsers;
    QList<HighPerformanceTerminal *> terminals;

    for (int i = 0; i < 5; ++i)
    {
        VT100Parser *parser = new VT100Parser();
        HighPerformanceTerminal *terminal = new HighPerformanceTerminal();
        terminal->setParser(parser);

        parsers.append(parser);
        terminals.append(terminal);
    }

    QElapsedTimer timer;
    timer.start();

    int loadCycle = 0;
    const int testDurationMs = 6000; // 6秒测试

    while (timer.elapsed() < testDurationMs)
    {
        // 对所有实例施加负载
        for (int i = 0; i < parsers.size(); ++i)
        {
            QByteArray data = QString("Load test %1-%2\n").arg(i).arg(loadCycle).toLatin1();
            parsers[i]->processData(data);

            if (parsers[i]->hasPendingCommands())
            {
                parsers[i]->takeAllCommands();
            }

            terminals[i]->scheduleUpdate();
        }

        loadCycle++;
        QApplication::processEvents();
        QThread::msleep(5);
    }

    // 清理资源
    for (auto parser : parsers)
    {
        delete parser;
    }
    for (auto terminal : terminals)
    {
        delete terminal;
    }

    qDebug() << "Completed" << loadCycle << "load cycles with multiple instances";

    QVERIFY(checkSystemStability());
    QVERIFY(loadCycle > 100);
}

void TestLongRunningStability::testMemoryStabilityUnderLoad()
{
    qDebug() << "Testing memory stability under load...";

    QElapsedTimer timer;
    timer.start();

    int memoryTestCycle = 0;
    const int testDurationMs = 8000; // 8秒测试

    while (timer.elapsed() < testDurationMs)
    {
        // 创建和销毁大量对象
        for (int i = 0; i < 10; ++i)
        {
            VT100Parser *tempParser = new VT100Parser();

            // 处理一些数据
            QByteArray data = generateVT100Data(20);
            tempParser->processData(data);

            // 获取命令
            auto commands = tempParser->takeAllCommands();
            Q_UNUSED(commands);

            delete tempParser;
        }

        // 主要组件的持续负载
        QByteArray heavyData = generateRandomData(2000);
        m_parser->processData(heavyData);

        if (m_parser->hasPendingCommands())
        {
            m_parser->takeAllCommands();
        }

        memoryTestCycle++;

        // 定期垃圾回收
        if (memoryTestCycle % 10 == 0)
        {
            QApplication::processEvents();
        }

        QThread::msleep(20);
    }

    qDebug() << "Completed" << memoryTestCycle << "memory test cycles";

    QVERIFY(checkSystemStability());
    QVERIFY(memoryTestCycle > 50);
}

void TestLongRunningStability::testPerformanceDegradation()
{
    qDebug() << "Testing performance degradation over time...";

    QList<qint64> processingTimes;
    const int testRounds = 20;

    for (int round = 0; round < testRounds; ++round)
    {
        QElapsedTimer roundTimer;
        roundTimer.start();

        // 执行标准化的工作负载
        for (int i = 0; i < 100; ++i)
        {
            QByteArray data = generateVT100Data(10);
            m_parser->processData(data);

            if (m_parser->hasPendingCommands())
            {
                m_parser->takeAllCommands();
            }

            m_terminal->scheduleUpdate();
        }

        QApplication::processEvents();
        qint64 roundTime = roundTimer.elapsed();
        processingTimes.append(roundTime);

        qDebug() << "Round" << round + 1 << "completed in" << roundTime << "ms";

        // 短暂休息
        QThread::msleep(100);
    }

    // 分析性能趋势
    qint64 firstHalfAvg = 0, secondHalfAvg = 0;
    int halfPoint = testRounds / 2;

    for (int i = 0; i < halfPoint; ++i)
    {
        firstHalfAvg += processingTimes[i];
    }
    firstHalfAvg /= halfPoint;

    for (int i = halfPoint; i < testRounds; ++i)
    {
        secondHalfAvg += processingTimes[i];
    }
    secondHalfAvg /= (testRounds - halfPoint);

    qDebug() << "First half average:" << firstHalfAvg << "ms";
    qDebug() << "Second half average:" << secondHalfAvg << "ms";
    qDebug() << "Performance change:" << ((secondHalfAvg - firstHalfAvg) * 100.0 / firstHalfAvg) << "%";

    QVERIFY(checkSystemStability());

    // 允许一定程度的性能下降（不超过50%）
    QVERIFY2(secondHalfAvg < firstHalfAvg * 1.5, "Significant performance degradation detected");
}

void TestLongRunningStability::testConcurrentOperations()
{
    qDebug() << "Testing concurrent operations...";

    QElapsedTimer timer;
    timer.start();

    int concurrentCycle = 0;
    const int testDurationMs = 6000; // 6秒测试

    while (timer.elapsed() < testDurationMs)
    {
        // 模拟并发操作
        QList<QByteArray> dataPackets;

        // 准备多个数据包
        for (int i = 0; i < 5; ++i)
        {
            dataPackets.append(generateVT100Data(5));
        }

        // 快速连续处理
        for (const auto &packet : dataPackets)
        {
            m_parser->processData(packet);
        }

        // 批量获取命令
        if (m_parser->hasPendingCommands())
        {
            auto commands = m_parser->takeAllCommands();
            qDebug() << "Concurrent cycle" << concurrentCycle << "processed" << commands.size() << "commands";
        }

        // 同时进行其他操作
        m_terminal->setBufferSize(5000 + (concurrentCycle % 1000));
        m_terminal->setWordWrapEnabled(concurrentCycle % 2 == 0);
        m_terminal->scheduleUpdate();

        concurrentCycle++;
        QApplication::processEvents();
        QThread::msleep(50);
    }

    qDebug() << "Completed" << concurrentCycle << "concurrent operation cycles";

    QVERIFY(checkSystemStability());
    QVERIFY(concurrentCycle > 20);
}

// 辅助方法实现
void TestLongRunningStability::runStabilityTest(int durationSeconds, std::function<void()> testFunction)
{
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < durationSeconds * 1000)
    {
        testFunction();
        QApplication::processEvents();
        QThread::msleep(1);
    }
}

QByteArray TestLongRunningStability::generateRandomData(int size)
{
    QByteArray data;
    data.reserve(size);

    for (int i = 0; i < size; ++i)
    {
        // 生成可打印字符
        char ch = static_cast<char>(32 + (QRandomGenerator::global()->bounded(95)));
        data.append(ch);

        // 偶尔添加换行
        if (i > 0 && i % 80 == 0)
        {
            data.append('\n');
        }
    }

    return data;
}

QByteArray TestLongRunningStability::generateVT100Data(int lines)
{
    QByteArray data;

    for (int i = 0; i < lines; ++i)
    {
        // 随机选择VT100序列
        int seqType = QRandomGenerator::global()->bounded(4);

        switch (seqType)
        {
            case 0: // 颜色序列
                data += QString("\x1b[%1mLine %2 with color\x1b[0m\n").arg(31 + (i % 7)).arg(i).toLatin1();
                break;
            case 1: // 光标移动
                data += QString("Line %1 ").arg(i).toLatin1();
                data += "\x1b[10C"; // 向右移动10个位置
                data += "moved text\n";
                break;
            case 2: // 普通文本
                data += QString("Normal line %1 with some content\n").arg(i).toLatin1();
                break;
            case 3: // 混合内容
                data += QString("Mixed %1: \x1b[1mbold\x1b[0m and \x1b[4munderline\x1b[0m\n").arg(i).toLatin1();
                break;
        }
    }

    return data;
}

void TestLongRunningStability::simulateRealWorldUsage(int iterations)
{
    for (int i = 0; i < iterations; ++i)
    {
        // 模拟真实世界的使用模式
        int pattern = i % 6;

        switch (pattern)
        {
            case 0: // 启动序列
                m_parser->processData("System starting...\n");
                break;
            case 1: // 日志输出
                m_parser->processData(
                    QString("[%1] Log entry %2\n").arg(QTime::currentTime().toString()).arg(i).toLatin1());
                break;
            case 2: // 错误信息
                m_parser->processData("\x1b[31mERROR: Something went wrong\x1b[0m\n");
                break;
            case 3: // 进度指示
                m_parser->processData(QString("Progress: %1%\r").arg(i % 100).toLatin1());
                break;
            case 4: // 数据输出
                m_parser->processData(generateRandomData(200));
                break;
            case 5: // 清屏和重绘
                m_parser->processData("\x1b[2J\x1b[H");
                m_parser->processData("Screen cleared and redrawn\n");
                break;
        }

        if (i % 20 == 0)
        {
            m_parser->takeAllCommands();
            QApplication::processEvents();
        }
    }
}

bool TestLongRunningStability::checkSystemStability()
{
    // 基本的稳定性检查
    QApplication::processEvents();

    // 检查对象是否仍然有效
    if (m_parser == nullptr || m_terminal == nullptr || m_serialProcessor == nullptr)
    {
        return false;
    }

    // 检查基本功能是否正常
    m_parser->processData("stability check");
    bool hasCommands = m_parser->hasPendingCommands();
    if (hasCommands)
    {
        m_parser->takeAllCommands();
    }

    return true; // 如果到这里没有崩溃，认为是稳定的
}

#include "test_long_running_stability.moc"
QTEST_MAIN(TestLongRunningStability)
