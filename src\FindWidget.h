#ifndef FINDWIDGET_H
#define FINDWIDGET_H

#include "HighPerformanceTerminal.h" // For FindFlags

#include <QWidget>

class QLineEdit;
class QPushButton;
class QLabel;
class QCheckBox;

class FindWidget : public QWidget {
    Q_OBJECT

public:
    explicit FindWidget(QWidget *parent = nullptr);

signals:
    void findTriggered(const QString &term, HighPerformanceTerminal::FindFlags flags);
    void findNextTriggered();
    void findPreviousTriggered();
    void widgetClosed();

public slots:
    void updateResults(int matchCount, int currentIndex);
    void setFocusOnLineEdit();
    void triggerSearch();

protected:
    void closeEvent(QCloseEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    auto eventFilter(QObject *watched, QEvent *event) -> bool override;

private slots:
    void onSearchTextChanged();
    void onFindNext();
    void onFindPrevious();
    void onOptionChanged();

private:
    void setupUi();
    auto handleDragPress(QMouseEvent *event) -> bool;
    auto handleDragMove(QMouseEvent *event) -> bool;
    auto handleDragRelease(QMouseEvent *event, QObject *watched) -> bool;

    QLineEdit *m_searchLineEdit;
    QPushButton *m_findPrevButton;
    QPushButton *m_findNextButton;
    QPushButton *m_caseSensitiveButton;
    QPushButton *m_wholeWordButton;
    QPushButton *m_regexButton;
    QPushButton *m_findInSelectionButton;
    QPushButton *m_closeButton;
    QLabel *m_searchCountLabel;

    QPoint m_dragStartPosition;
    bool m_isDragging = false;
};

#endif // FINDWIDGET_H
