#ifndef VT100PARSER_H
#define VT100PARSER_H

#include "core/TerminalConstants.h"

#include <QByteArray>
#include <QColor>
#include <QMutex>
#include <QObject>
#include <QString>
#include <QVariant>
#include <QVector>

#include <cstdint>

// 定义一个结构体来聚合所有可配置的VT100 CSI命令的启用状态
struct VT100CommandConfig
{
    // CSI序列控制
    bool enableSGR{true};            // SGR - 选择图形再现 (颜色, 粗体等)
    bool enableErase{true};          // ED, EL - 擦除功能
    bool enableCursorMovement{true}; // CUU, CUD, CUF, CUB, CUP - 光标移动

    // ASCII控制字符控制
    bool enableBackspace{true};     // \b (0x08) - 退格
    bool enableHorizontalTab{true}; // \\t (0x09) - 水平制表符
    bool enableBell{true};          // \a (0x07) - 响铃
    bool enableFormFeed{false};     // \\f (0x0C) - 换页/清屏 (默认禁用)
    bool enableVerticalTab{true};   // \\v (0x0B) - 垂直制表符
};

struct TerminalCommand
{
    enum CommandType : std::uint8_t
    {
        PrintableChar,
        ForegroundColorChanged,
        BackgroundColorChanged,
        BoldChanged,
        AttributesReset,
        CursorUp,
        CursorDown,
        CursorForward,
        CursorBack,
        SetCursorPosition,
        EraseInDisplay,
        EraseInLine,
        Backspace,
        HorizontalTab,
        Bell,
        FormFeed,
        VerticalTab
    };

    CommandType type{PrintableChar}; // 默认初始化为PrintableChar
    QVariantList params;
};

class VT100Parser : public QObject {
    Q_OBJECT

public:
    explicit VT100Parser(QObject *parent = nullptr);
    void setCommandConfig(const VT100CommandConfig &config);
    auto takeAllCommands() -> QList<TerminalCommand>;
    auto takeCommands(int maxCount) -> QList<TerminalCommand>;
    auto hasPendingCommands() const -> bool;

    // 缓冲区保护
    void setMaxCommandBufferSize(int maxSize);
    auto getMaxCommandBufferSize() const -> int;
    auto getCurrentCommandCount() const -> int;

public slots:
    void processData(const QByteArray &data);

signals:
    void parseFinished();
    void bufferOverflowWarning(qsizetype currentSize, qsizetype maxSize);

private:
    void parseByte(uchar byte);
    void handleCSISequence(uchar finalByte);
    void handleEscapeSequence();
    void flushTextBuffer();

    // 辅助函数用于降低认知复杂度
    void handleNormalState(uchar byte);
    void handleControlCharacter(uchar byte);
    void handleSGRSequence(const QString &paramsStr);
    void handleCursorMovement(uchar finalByte, const QString &paramsStr);
    void handleEraseCommands(uchar finalByte, const QString &paramsStr);

    enum State : std::uint8_t
    {
        Normal,
        Escape,         // 收到 ESC
        CSIEntry,       // 收到 ESC [
        CSIParam,       // 正在解析参数 (例如, ESC[31m 中的 '31')
        CSIIntermediate // 正在解析中间字节
    };

    State m_state;
    QByteArray m_paramsBuffer;
    QByteArray m_intermediateBuffer;
    QString m_textBuffer;
    QList<TerminalCommand> m_commandList;
    QList<TerminalCommand> m_commandBuffer;
    mutable QMutex m_bufferMutex;

    VT100CommandConfig m_config; // 存储命令启用/禁用状态

    // 缓冲区保护
    qsizetype m_maxCommandBufferSize = App::Terminal::kDefaultMaxCommandBufferSize; // 使用全局常量
    bool m_bufferOverflowProtectionEnabled = true;

    // 解析保护
    static constexpr int kMaxParamsBufferSize = App::Terminal::kMaxParamsBufferSize; // 最大参数缓冲区大小
    void resetParserState();                                                         // 重置解析器状态
};

#endif // VT100PARSER_H
