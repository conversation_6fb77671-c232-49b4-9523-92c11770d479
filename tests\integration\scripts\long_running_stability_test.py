#!/usr/bin/env python3
"""
长时间运行稳定性测试
模拟24小时连续运行，监控SerialT的稳定性、内存使用和性能表现
"""

import sys
import time
import serial
import threading
import psutil
import json
import random
import string
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from config import SERIAL_CONFIG, VT100_SEQUENCES

class LongRunningStabilityTester:
    """长时间运行稳定性测试器"""
    
    def __init__(self, test_duration_hours=24):
        self.test_duration_hours = test_duration_hours
        self.port = SERIAL_CONFIG['TEST_TOOL_PORT']
        self.baudrate = SERIAL_CONFIG['DEFAULT_BAUDRATE']
        self.serial = None
        self.running = False
        
        # 监控数据
        self.monitoring_data = []
        self.error_log = []
        self.performance_stats = {
            'start_time': None,
            'end_time': None,
            'total_data_sent': 0,
            'total_data_received': 0,
            'total_errors': 0,
            'connection_attempts': 0,
            'successful_connections': 0
        }
        
        # 测试配置
        self.check_interval_minutes = 5  # 每5分钟检查一次
        self.data_send_interval_seconds = 30  # 每30秒发送一次数据
        
    def log_event(self, level, message):
        """记录事件日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'message': message
        }
        
        if level == 'ERROR':
            self.error_log.append(log_entry)
        
        print(f"[{timestamp}] {level}: {message}")
    
    def get_system_resources(self):
        """获取系统资源信息"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_mb': memory.available / 1024 / 1024,
                'memory_used_mb': memory.used / 1024 / 1024
            }
        except Exception as e:
            self.log_event('ERROR', f"获取系统资源失败: {e}")
            return None
    
    def get_serialt_processes(self):
        """获取SerialT进程信息"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent', 'create_time']):
                if 'SerialT' in proc.info['name']:
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    uptime_hours = (time.time() - proc.info['create_time']) / 3600
                    
                    processes.append({
                        'pid': proc.info['pid'],
                        'memory_mb': memory_mb,
                        'cpu_percent': proc.info['cpu_percent'],
                        'uptime_hours': uptime_hours
                    })
        except Exception as e:
            self.log_event('ERROR', f"获取SerialT进程信息失败: {e}")
        
        return processes
    
    def connect_serial(self):
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1.0
            )
            self.performance_stats['successful_connections'] += 1
            self.log_event('INFO', f"成功连接到串口 {self.port}")
            return True
        except Exception as e:
            self.log_event('ERROR', f"串口连接失败: {e}")
            return False
        finally:
            self.performance_stats['connection_attempts'] += 1
    
    def disconnect_serial(self):
        """断开串口连接"""
        try:
            if self.serial and self.serial.is_open:
                self.serial.close()
                self.log_event('INFO', "串口连接已断开")
        except Exception as e:
            self.log_event('ERROR', f"断开串口连接失败: {e}")
    
    def send_test_data(self, data_type='mixed'):
        """发送测试数据"""
        if not self.serial or not self.serial.is_open:
            return False
        
        try:
            if data_type == 'text':
                data = f"Stability test {datetime.now().strftime('%H:%M:%S')}: " + \
                       ''.join(random.choices(string.ascii_letters, k=50)) + "\n"
            elif data_type == 'vt100':
                color = random.choice(['FG_RED', 'FG_GREEN', 'FG_BLUE', 'FG_YELLOW'])
                data = VT100_SEQUENCES[color] + f"Colored stability test {datetime.now().strftime('%H:%M:%S')}" + \
                       VT100_SEQUENCES['RESET'] + "\n"
            elif data_type == 'numbers':
                data = f"Numbers {datetime.now().strftime('%H:%M:%S')}: " + \
                       ''.join(random.choices(string.digits, k=30)) + "\n"
            else:  # mixed
                data_types = ['text', 'vt100', 'numbers']
                selected_type = random.choice(data_types)
                return self.send_test_data(selected_type)
            
            self.serial.write(data.encode('utf-8'))
            self.serial.flush()
            self.performance_stats['total_data_sent'] += len(data)
            
            # 检查回应数据
            if self.serial.in_waiting > 0:
                received = self.serial.read(self.serial.in_waiting)
                self.performance_stats['total_data_received'] += len(received)
            
            return True
            
        except Exception as e:
            self.log_event('ERROR', f"发送测试数据失败: {e}")
            self.performance_stats['total_errors'] += 1
            return False
    
    def collect_monitoring_data(self):
        """收集监控数据"""
        system_resources = self.get_system_resources()
        serialt_processes = self.get_serialt_processes()
        
        monitoring_entry = {
            'timestamp': datetime.now().isoformat(),
            'system_resources': system_resources,
            'serialt_processes': serialt_processes,
            'performance_stats': self.performance_stats.copy()
        }
        
        self.monitoring_data.append(monitoring_entry)
        
        # 输出当前状态
        if system_resources:
            self.log_event('INFO', 
                f"系统状态 - CPU: {system_resources['cpu_percent']:.1f}%, "
                f"内存: {system_resources['memory_percent']:.1f}%")
        
        if serialt_processes:
            for proc in serialt_processes:
                self.log_event('INFO', 
                    f"SerialT进程 PID {proc['pid']} - "
                    f"内存: {proc['memory_mb']:.1f}MB, "
                    f"运行时间: {proc['uptime_hours']:.1f}小时")
        else:
            self.log_event('WARNING', "未发现运行中的SerialT进程")
    
    def data_sender_thread(self):
        """数据发送线程"""
        self.log_event('INFO', "数据发送线程启动")
        
        while self.running:
            if self.send_test_data():
                self.log_event('DEBUG', "测试数据发送成功")
            else:
                self.log_event('WARNING', "测试数据发送失败")
            
            # 等待下次发送
            for _ in range(self.data_send_interval_seconds):
                if not self.running:
                    break
                time.sleep(1)
        
        self.log_event('INFO', "数据发送线程结束")
    
    def monitoring_thread(self):
        """监控线程"""
        self.log_event('INFO', "监控线程启动")
        
        while self.running:
            self.collect_monitoring_data()
            
            # 等待下次监控
            for _ in range(self.check_interval_minutes * 60):
                if not self.running:
                    break
                time.sleep(1)
        
        self.log_event('INFO', "监控线程结束")
    
    def simulate_stress_scenarios(self):
        """模拟压力场景"""
        self.log_event('INFO', "开始模拟压力场景")
        
        stress_scenarios = [
            ('大数据量发送', self.stress_large_data),
            ('高频数据发送', self.stress_high_frequency),
            ('连接断开重连', self.stress_reconnection),
            ('混合VT100序列', self.stress_vt100_sequences)
        ]
        
        for scenario_name, scenario_func in stress_scenarios:
            if not self.running:
                break
                
            self.log_event('INFO', f"执行压力场景: {scenario_name}")
            try:
                scenario_func()
                self.log_event('INFO', f"压力场景 '{scenario_name}' 完成")
            except Exception as e:
                self.log_event('ERROR', f"压力场景 '{scenario_name}' 失败: {e}")
            
            # 场景间休息
            time.sleep(60)
    
    def stress_large_data(self):
        """大数据量压力测试"""
        large_data = "Large data stress test: " + "X" * 5000 + "\n"
        for i in range(10):
            if not self.running:
                break
            self.serial.write(large_data.encode('utf-8'))
            self.serial.flush()
            self.performance_stats['total_data_sent'] += len(large_data)
            time.sleep(1)
    
    def stress_high_frequency(self):
        """高频数据压力测试"""
        for i in range(100):
            if not self.running:
                break
            data = f"High freq {i:03d}: {time.time():.3f}\n"
            self.serial.write(data.encode('utf-8'))
            self.serial.flush()
            self.performance_stats['total_data_sent'] += len(data)
            time.sleep(0.1)
    
    def stress_reconnection(self):
        """连接断开重连压力测试"""
        for i in range(5):
            if not self.running:
                break
            self.log_event('INFO', f"重连测试 {i+1}/5")
            self.disconnect_serial()
            time.sleep(2)
            if self.connect_serial():
                self.send_test_data('text')
            time.sleep(3)
    
    def stress_vt100_sequences(self):
        """VT100序列压力测试"""
        colors = ['FG_RED', 'FG_GREEN', 'FG_BLUE', 'FG_YELLOW', 'FG_MAGENTA', 'FG_CYAN']
        for i in range(50):
            if not self.running:
                break
            color = colors[i % len(colors)]
            data = VT100_SEQUENCES[color] + f"VT100 stress {i:02d}" + VT100_SEQUENCES['RESET'] + "\n"
            self.serial.write(data.encode('utf-8'))
            self.serial.flush()
            self.performance_stats['total_data_sent'] += len(data)
            time.sleep(0.2)
    
    def save_test_results(self):
        """保存测试结果"""
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存监控数据
        monitoring_file = results_dir / f"stability_monitoring_{timestamp}.json"
        with open(monitoring_file, 'w', encoding='utf-8') as f:
            json.dump(self.monitoring_data, f, indent=2, ensure_ascii=False)
        
        # 保存错误日志
        if self.error_log:
            error_file = results_dir / f"stability_errors_{timestamp}.json"
            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump(self.error_log, f, indent=2, ensure_ascii=False)
        
        # 保存性能统计
        stats_file = results_dir / f"stability_stats_{timestamp}.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.performance_stats, f, indent=2, ensure_ascii=False)
        
        self.log_event('INFO', f"测试结果已保存到 {results_dir}")
    
    def generate_summary_report(self):
        """生成总结报告"""
        if not self.monitoring_data:
            self.log_event('WARNING', "无监控数据，无法生成报告")
            return
        
        # 计算测试时长
        start_time = datetime.fromisoformat(self.monitoring_data[0]['timestamp'])
        end_time = datetime.fromisoformat(self.monitoring_data[-1]['timestamp'])
        duration_hours = (end_time - start_time).total_seconds() / 3600
        
        # 分析系统资源
        cpu_values = [entry['system_resources']['cpu_percent'] 
                     for entry in self.monitoring_data 
                     if entry['system_resources']]
        memory_values = [entry['system_resources']['memory_percent'] 
                        for entry in self.monitoring_data 
                        if entry['system_resources']]
        
        # 分析SerialT进程
        serialt_memory_values = []
        for entry in self.monitoring_data:
            for proc in entry['serialt_processes']:
                serialt_memory_values.append(proc['memory_mb'])
        
        print(f"\n📊 长时间运行稳定性测试报告")
        print("=" * 60)
        print(f"测试时长: {duration_hours:.2f} 小时")
        print(f"监控数据点: {len(self.monitoring_data)} 个")
        print(f"错误次数: {len(self.error_log)} 个")
        
        if cpu_values:
            print(f"\n系统资源分析:")
            print(f"  CPU使用率 - 平均: {sum(cpu_values)/len(cpu_values):.1f}%, "
                  f"最大: {max(cpu_values):.1f}%, 最小: {min(cpu_values):.1f}%")
        
        if memory_values:
            print(f"  内存使用率 - 平均: {sum(memory_values)/len(memory_values):.1f}%, "
                  f"最大: {max(memory_values):.1f}%, 最小: {min(memory_values):.1f}%")
        
        if serialt_memory_values:
            print(f"\nSerialT进程分析:")
            print(f"  内存使用 - 平均: {sum(serialt_memory_values)/len(serialt_memory_values):.1f}MB, "
                  f"最大: {max(serialt_memory_values):.1f}MB, 最小: {min(serialt_memory_values):.1f}MB")
        
        print(f"\n性能统计:")
        print(f"  发送数据: {self.performance_stats['total_data_sent']} 字节")
        print(f"  接收数据: {self.performance_stats['total_data_received']} 字节")
        print(f"  连接成功率: {self.performance_stats['successful_connections']}/{self.performance_stats['connection_attempts']}")
        print(f"  错误次数: {self.performance_stats['total_errors']}")
        
        # 稳定性评估
        if len(self.error_log) == 0:
            print(f"\n🎉 稳定性评估: 优秀 (无错误)")
        elif len(self.error_log) < 5:
            print(f"\n✅ 稳定性评估: 良好 (少量错误)")
        else:
            print(f"\n⚠️ 稳定性评估: 需要关注 (错误较多)")
    
    def run_quick_test(self, duration_minutes=30):
        """运行快速测试 (用于演示)"""
        self.log_event('INFO', f"开始快速稳定性测试 ({duration_minutes} 分钟)")
        
        self.performance_stats['start_time'] = datetime.now().isoformat()
        self.running = True
        
        # 连接串口
        if not self.connect_serial():
            self.log_event('ERROR', "无法连接串口，测试终止")
            return False
        
        try:
            # 启动监控线程
            monitor_thread = threading.Thread(target=self.monitoring_thread, daemon=True)
            monitor_thread.start()
            
            # 启动数据发送线程
            sender_thread = threading.Thread(target=self.data_sender_thread, daemon=True)
            sender_thread.start()
            
            # 运行指定时间
            end_time = time.time() + duration_minutes * 60
            stress_interval = duration_minutes * 60 // 4  # 每1/4时间执行一次压力测试
            
            while time.time() < end_time and self.running:
                remaining_time = end_time - time.time()
                
                if remaining_time > stress_interval:
                    # 执行压力测试
                    self.simulate_stress_scenarios()
                
                # 等待一段时间
                time.sleep(min(60, remaining_time))
            
            self.running = False
            self.performance_stats['end_time'] = datetime.now().isoformat()
            
            # 等待线程结束
            monitor_thread.join(timeout=5)
            sender_thread.join(timeout=5)
            
            # 生成报告
            self.generate_summary_report()
            self.save_test_results()
            
            return True
            
        except KeyboardInterrupt:
            self.log_event('INFO', "测试被用户中断")
            self.running = False
            return False
        except Exception as e:
            self.log_event('ERROR', f"测试执行异常: {e}")
            self.running = False
            return False
        finally:
            self.disconnect_serial()
    
    def run_full_test(self):
        """运行完整的24小时测试"""
        self.log_event('INFO', f"开始完整稳定性测试 ({self.test_duration_hours} 小时)")
        
        # 实际的24小时测试实现
        # 由于演示需要，这里提供框架
        print("⚠️ 完整24小时测试需要真实的24小时运行时间")
        print("   建议在生产环境或专门的测试环境中执行")
        print("   当前提供30分钟的快速测试作为演示")
        
        return self.run_quick_test(30)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SerialT 长时间运行稳定性测试')
    parser.add_argument('--duration', type=int, default=30, help='测试时长(分钟)，默认30分钟')
    parser.add_argument('--full', action='store_true', help='运行完整24小时测试')
    
    args = parser.parse_args()
    
    print("🕐 SerialT 长时间运行稳定性测试")
    print("=" * 60)
    
    if args.full:
        print("⚠️ 完整24小时测试模式")
        print("   这将运行24小时连续测试")
    else:
        print(f"⚡ 快速测试模式 ({args.duration} 分钟)")
        print("   这是24小时测试的缩短版本")
    
    print("\n⚠️ 测试要求:")
    print("1. SerialT应用已启动并连接到COM30")
    print("2. 系统有足够的资源进行长时间运行")
    print("3. 测试期间请不要关闭SerialT")
    print("4. 建议在测试期间监控系统性能")
    print()
    
    input("准备就绪后按回车键开始测试...")
    
    tester = LongRunningStabilityTester()
    
    try:
        if args.full:
            success = tester.run_full_test()
        else:
            success = tester.run_quick_test(args.duration)
        
        return success
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
