#ifndef INSTANCEMANAGER_H
#define INSTANCEMANAGER_H

#include <QObject>
#include <QSharedMemory>
#include <QSystemSemaphore>

class InstanceManager : public QObject {
    Q_OBJECT
public:
    explicit InstanceManager(QObject *parent = nullptr);
    ~InstanceManager() override;

    InstanceManager(const InstanceManager &) = delete;
    auto operator=(const InstanceManager &) -> InstanceManager & = delete;
    InstanceManager(InstanceManager &&) = delete;
    auto operator=(InstanceManager &&) -> InstanceManager & = delete;

    [[nodiscard]] auto getInstanceNumber() const -> int;

private:
    void initializeSharedMemory();
    void releaseInstanceNumber();

    QSharedMemory m_sharedMemory;
    QSystemSemaphore m_semaphore;
    int m_instanceNumber;
    bool m_isAttached;
};

#endif // INSTANCEMANAGER_H
