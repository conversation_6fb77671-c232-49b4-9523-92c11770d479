CompileFlags:
  Add: [
    -std=c++20,                        # 设置C++20标准
    -Wall,                             # 启用常见警告
    -Wex<PERSON>,                           # 启用额外警告
    -Wsign-conversion,                 # 符号转换警告
    -Wconversion,                      # 类型转换警告
    -Wno-c++98-compat,                # 禁用C++98兼容性警告
    -Wno-c++98-compat-pedantic,       # 禁用C++98严格兼容性警告
    -xc++,                            # 指定C++语言
    --target=x86_64-pc-windows-msvc,  # 指定目标平台
    -fno-ms-compatibility,             # 不使用MSVC兼容模式
    -Wno-switch-default               # 禁用已经穷举的switch语句缺少default分支的警告 
  ]
  Remove: []  

Diagnostics:
  ClangTidy:
    Add: []
    Remove: []
  UnusedIncludes: Strict
  
Index:
  Background: Build
  
InlayHints:
  Enabled: Yes
  ParameterNames: Yes
  DeducedTypes: Yes

Hover:
  ShowAKA: Yes 