/**
 * @file ConnectionConstants.h
 * @brief 定义了串口连接管理相关的所有常量，包括监控间隔、连接质量评估和健康检查参数。
 *
 * 本文件包含了SerialT项目中智能连接监控系统的核心常量定义，涵盖连接状态监控、
 * 质量评估算法、响应时间分析和异常检测等方面的配置参数。
 *
 * @note 这些常量直接影响连接稳定性判断和监控性能，修改时需要充分测试。
 */
#ifndef CONNECTIONCONSTANTS_H
#define CONNECTIONCONSTANTS_H

namespace App::Connection {

// #############################################################################
// # 定时器与间隔常量 (Timer and Interval Constants)
// #############################################################################

/**
 * @brief 默认统计信息更新间隔（毫秒）
 *
 * 控制连接统计信息（如数据传输速率、错误率等）的更新频率。
 * 较小的值提供更实时的统计，但会增加CPU使用率。
 */
inline constexpr int kDefaultStatisticsIntervalMs = 1000;

/**
 * @brief 默认基础监控间隔（毫秒）
 *
 * 智能连接监控的基础间隔时间，作为自适应调整的起始点。
 * 连接质量好时会延长间隔，质量差时会缩短间隔。
 */
inline constexpr int kDefaultBaseMonitorIntervalMs = 2000;

/**
 * @brief 默认重连延迟间隔（毫秒）
 *
 * 连接失败后到开始重连尝试之间的等待时间，避免过于频繁的重连尝试。
 */
inline constexpr int kDefaultReconnectDelayMs = 250;

/**
 * @brief 最小监控间隔（毫秒）
 *
 * 连接质量监控的最快频率限制，防止过度频繁的监控影响性能。
 * 在连接质量很差时会使用此值进行密集监控。
 */
inline constexpr int kMinMonitorIntervalMs = 500;

/**
 * @brief 最大监控间隔（毫秒）
 *
 * 连接质量监控的最慢频率限制，在连接稳定时使用较长间隔节省资源。
 * 在连接质量很好时会使用此值进行稀疏监控。
 */
inline constexpr int kMaxMonitorIntervalMs = 10000;

/**
 * @brief 监控间隔变化阈值（毫秒）
 *
 * 触发监控间隔调整的最小时间差，避免监控间隔频繁微调。
 */
inline constexpr int kMonitorIntervalChangeThresholdMs = 500;

/**
 * @brief 监控间隔递增步长（毫秒）
 *
 * 当连接质量改善时，监控间隔的递增幅度，实现渐进式调整。
 */
inline constexpr int kMonitorIntervalIncrementMs = 2000;

// #############################################################################
// # 连接质量与健康检查常量 (Connection Quality & Health-Check Constants)
// #############################################################################

/**
 * @brief 连接质量评分阈值：优秀级别
 *
 * 质量评分范围 0.0 到 1.0，达到此阈值表示连接质量优秀，
 * 可以使用最长的监控间隔。
 */
inline constexpr double kQualityThresholdExcellent = 0.9;

/**
 * @brief 连接质量评分阈值：非常好级别
 *
 * 用于调整监控间隔的特定阈值，介于优秀和良好之间的质量水平。
 */
inline constexpr double kQualityThresholdVeryGood = 0.8;

/**
 * @brief 连接质量评分阈值：良好级别
 *
 * 表示连接质量良好，可以适当延长监控间隔。
 */
inline constexpr double kQualityThresholdGood = 0.7;

/**
 * @brief 连接质量评分阈值：一般级别
 *
 * 表示连接质量一般，需要保持正常的监控频率。
 */
inline constexpr double kQualityThresholdFair = 0.5;

/**
 * @brief 连接质量评分阈值：较差级别
 *
 * 表示连接质量较差，需要增加监控频率以及时发现问题。
 */
inline constexpr double kQualityThresholdPoor = 0.3;

// #############################################################################
// # 连接质量计算因子 (Connection Quality Calculation Factors)
// #############################################################################

/**
 * @brief 质量评分平滑因子
 *
 * 用于指数移动平均算法的平滑系数，控制新数据对历史评分的影响程度。
 * 较小的值使评分变化更平滑，较大的值使评分对新数据更敏感。
 */
inline constexpr double kQualitySmoothingFactor = 0.3;

/**
 * @brief 响应时间惩罚因子
 *
 * 对 100ms 到 500ms 响应时间的线性惩罚因子，用于计算质量评分。
 * 响应时间越长，对质量评分的负面影响越大。
 */
inline constexpr double kResponseTimePenaltyFactor = 0.3;

/**
 * @brief 严重响应时间惩罚
 *
 * 对超过 500ms 响应时间的固定惩罚值，表示严重的响应延迟。
 */
inline constexpr double kPoorResponseTimePenalty = 0.5;

/**
 * @brief 数据停滞惩罚因子
 *
 * 每次数据停滞事件对质量评分的惩罚因子，累积计算。
 */
inline constexpr double kDataStallPenaltyFactor = 0.1;

/**
 * @brief 连续失败惩罚因子
 *
 * 每次连续失败对质量评分的惩罚因子，反映连接的不稳定性。
 */
inline constexpr double kFailurePenaltyFactor = 0.2;

/**
 * @brief 允许的最低质量评分
 *
 * 质量评分的下限值，确保评分在有效范围内。
 */
inline constexpr double kMinQualityScore = 0.0;

/**
 * @brief 允许的最高连接质量评分
 *
 * 质量评分的上限值，确保评分在有效范围内。
 */
inline constexpr double kMaxConnectionQuality = 1.0;

/**
 * @brief 初始连接质量评分
 *
 * 新建连接时的初始质量评分，假设连接开始时是完美的。
 */
inline constexpr double kInitialConnectionQuality = 1.0;

// #############################################################################
// # 响应时间阈值常量 (Response Time Threshold Constants)
// #############################################################################

/**
 * @brief 优秀响应时间阈值（毫秒）
 *
 * 低于此值的响应时间被认为是优秀的，不会对质量评分产生负面影响。
 */
inline constexpr int kExcellentResponseTimeMs = 100;

/**
 * @brief 良好响应时间阈值（毫秒）
 *
 * 超过此值的响应时间被认为是较差的，会对质量评分产生显著负面影响。
 */
inline constexpr int kGoodResponseTimeMs = 500;

/**
 * @brief 响应时间惩罚计算范围
 *
 * 用于响应时间线性惩罚计算的范围值，等于良好阈值减去优秀阈值。
 */
inline constexpr double kResponseTimePenaltyRange = static_cast<double>(kGoodResponseTimeMs - kExcellentResponseTimeMs);

// #############################################################################
// # 健康与异常检测常量 (Health and Anomaly Detection Constants)
// #############################################################################

/**
 * @brief 响应时间尖峰检测因子
 *
 * 用于检测响应时间相对于平均值的异常尖峰的倍数因子。
 * 当响应时间超过平均值的此倍数时，被认为是异常尖峰。
 */
inline constexpr double kResponseTimeSpikeFactor = 3.0;

/**
 * @brief 近期失败率异常阈值
 *
 * 在最近的检查周期中触发异常警报的失败率阈值。
 * 超过此比例的失败率会触发连接异常处理。
 */
inline constexpr double kRecentFailureRateThreshold = 0.3;

} // namespace App::Connection

#endif // CONNECTIONCONSTANTS_H
