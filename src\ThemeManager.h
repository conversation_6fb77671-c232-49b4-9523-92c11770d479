#ifndef THEMEMANAGER_H
#define THEMEMANAGER_H

#include <QColor>
#include <QObject>
#include <QPalette>
#include <QString>

/**
 * @brief 主题类型枚举
 */
enum class ThemeType : std::uint8_t
{
    Light = 0, // 亮色主题
    Dark = 1   // 暗色主题
};

/**
 * @brief 主题颜色方案结构
 *
 * 基于Windows 11设计语言，使用蓝色作为强调色
 */
struct ThemeColors
{
    // === 基础颜色 ===
    QColor background;     // 主背景色
    QColor surface;        // 表面背景色（卡片、面板等）
    QColor surfaceVariant; // 表面变体色（悬停状态等）

    // === 文本颜色 ===
    QColor onBackground;     // 主背景上的文本
    QColor onSurface;        // 表面上的文本
    QColor onSurfaceVariant; // 次要文本颜色
    QColor disabled;         // 禁用状态文本

    // === 强调色系统（蓝色主题） ===
    QColor primary;        // 主要强调色
    QColor primaryVariant; // 主要强调色变体
    QColor onPrimary;      // 主要强调色上的文本
    QColor secondary;      // 次要强调色
    QColor onSecondary;    // 次要强调色上的文本

    // === 状态颜色 ===
    QColor success; // 成功状态
    QColor warning; // 警告状态
    QColor error;   // 错误状态
    QColor info;    // 信息状态

    // === 边框和分割线 ===
    QColor border;  // 边框颜色
    QColor divider; // 分割线颜色
    QColor outline; // 轮廓线颜色

    // === 终端特定颜色 ===
    QColor terminalBackground; // 终端背景
    QColor terminalForeground; // 终端前景文本
    QColor terminalCursor;     // 终端光标
    QColor terminalSelection;  // 终端选择背景

    // === 搜索高亮 ===
    QColor searchHighlight; // 搜索高亮背景
    QColor searchActive;    // 当前搜索结果高亮
};

/**
 * @brief 主题管理器类
 *
 * 负责管理应用程序的主题系统，包括：
 * - 亮色/暗色主题切换
 * - Windows 11风格的颜色方案
 * - 主题持久化存储
 * - 主题变更通知
 */
class ThemeManager : public QObject {
    Q_OBJECT

public:
    explicit ThemeManager(QObject *parent = nullptr);

    // === 主题管理 ===
    [[nodiscard]] auto currentTheme() const -> ThemeType { return m_currentTheme; }
    void setTheme(ThemeType theme);
    void toggleTheme();

    // === 颜色访问 ===
    [[nodiscard]] auto colors() const -> const ThemeColors & { return m_colors; }
    [[nodiscard]] auto color(const QString &colorName) const -> QColor;

    // === 颜色名称 (供 color() 方法使用) ===
    // 定义为公共静态常量，作为 color() 方法的稳定接口，避免在调用时使用“魔法字符串”
    static constexpr const char *kColorBackground = "background";
    static constexpr const char *kColorSurface = "surface";
    static constexpr const char *kColorPrimary = "primary";
    static constexpr const char *kColorOnBackground = "onBackground";
    static constexpr const char *kColorOnSurface = "onSurface";
    static constexpr const char *kColorBorder = "border";
    static constexpr const char *kColorTerminalBackground = "terminalBackground";
    static constexpr const char *kColorTerminalForeground = "terminalForeground";

    // === 样式表生成 ===
    [[nodiscard]] auto generateStyleSheet() const -> QString;
    [[nodiscard]] auto generateButtonStyle() const -> QString;
    [[nodiscard]] auto generateComboBoxStyle() const -> QString;
    [[nodiscard]] auto generateLineEditStyle() const -> QString;
    [[nodiscard]] auto generateMenuStyle() const -> QString;
    [[nodiscard]] auto generateToolBarStyle() const -> QString;
    [[nodiscard]] auto generateStatusBarStyle() const -> QString;
    [[nodiscard]] auto generateDialogStyle() const -> QString;
    [[nodiscard]] auto generateCompactButtonStyle() const -> QString;
    [[nodiscard]] auto generateCompactToolBarStyle() const -> QString; // 紧凑的toolbar样式
    [[nodiscard]] auto generateScrollBarStyle() const -> QString;

    // === 系统主题检测 ===
    static auto detectSystemTheme() -> ThemeType;
    void followSystemTheme(bool follow = true);

    // === 持久化 ===
    void saveThemeSettings();
    void loadThemeSettings();

signals:
    void themeChanged(ThemeType newTheme);
    void colorsChanged();

private slots:
    void onSystemThemeChanged();

private:
    void initializeThemes();
    void updateColors();
    static auto createLightTheme() -> ThemeColors;
    static auto createDarkTheme() -> ThemeColors;

    ThemeType m_currentTheme;
    ThemeColors m_colors;
    bool m_followSystemTheme;
};

#endif // THEMEMANAGER_H
