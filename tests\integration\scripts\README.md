# 集成测试脚本说明

## 📋 脚本分类

### 🔧 核心测试脚本 (必需)

#### `serial_simulator.py` ⭐
**主要测试工具** - 串口数据模拟器
- 发送各种类型的测试数据到SerialT
- 支持文本、VT100序列、大数据等测试模式
- 使用频率：⭐⭐⭐⭐⭐

```bash
python serial_simulator.py --port COM31 --test basic_text --duration 10
```

#### `detect_virtual_ports.py` ⭐
**环境检测工具** - 自动检测虚拟串口配置
- 扫描系统串口并测试配对关系
- 验证COM30↔COM31是否正常工作
- 使用频率：⭐⭐⭐⭐

```bash
python detect_virtual_ports.py
```

### 🎮 辅助测试脚本 (可选)

#### `interactive_tester.py`
**交互式测试工具** - 手动测试和实时观察
- 支持手动输入和特殊命令
- 适合调试和探索性测试
- 使用频率：⭐⭐⭐

```bash
python interactive_tester.py --port COM31
```

### 🚫 过时/复杂脚本 (可删除)

#### `test_virtual_ports.py`
**已被替代** - 功能被 `detect_virtual_ports.py` 覆盖
- 原始的环境检测脚本
- 建议删除：✅

#### `test_environment_only.py`
**已被替代** - 功能被 `detect_virtual_ports.py` 覆盖
- 简化的环境检测脚本
- 建议删除：✅

#### `test_flow_manager.py`
**过于复杂** - 实际使用中不够灵活
- 试图自动化整个测试流程
- 实际上手动运行更方便
- 建议删除：✅

## 🎯 推荐的简化方案

### 保留的脚本 (3个)
1. `serial_simulator.py` - 主要测试工具
2. `detect_virtual_ports.py` - 环境检测
3. `interactive_tester.py` - 交互式测试

### 删除的脚本 (3个)
1. `test_virtual_ports.py` - 功能重复
2. `test_environment_only.py` - 功能重复  
3. `test_flow_manager.py` - 过于复杂

## 📖 简化后的使用流程

### 1. 环境检查
```bash
python scripts/detect_virtual_ports.py
```

### 2. 基础测试
```bash
# 启动SerialT，连接COM30后运行：
python scripts/serial_simulator.py --port COM31 --test basic_text --duration 10
python scripts/serial_simulator.py --port COM31 --test vt100 --duration 10
python scripts/serial_simulator.py --port COM31 --test large --duration 15
```

### 3. 交互式测试 (可选)
```bash
python scripts/interactive_tester.py --port COM31
```

## 🗂️ 文件整理建议

### 删除重复文档
- 删除 `QUICK_START.md` (内容已合并到README.md)

### 删除冗余脚本
```bash
# 可以安全删除这些文件：
rm scripts/test_virtual_ports.py
rm scripts/test_environment_only.py  
rm scripts/test_flow_manager.py
rm QUICK_START.md
```

### 最终目录结构
```
tests/integration/
├── README.md                    # 综合说明文档
├── config.py                    # 测试配置
└── scripts/
    ├── README.md                # 脚本说明 (本文件)
    ├── serial_simulator.py      # 主要测试工具
    ├── detect_virtual_ports.py  # 环境检测
    └── interactive_tester.py    # 交互式测试
```

这样整理后，目录更清晰，脚本更专注，使用更简单！
