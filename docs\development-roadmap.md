# SerialT 开发路线图

## 🎯 优先级排序

### 1. 代码质量和稳定性 🔧 (高优先级) ✅ 已完成

#### 单元测试和集成测试 ✅
- SerialProcessor 的连接/断开测试
- VT100Parser 的解析正确性测试  
- HighPerformanceTerminal 的渲染测试
- 设置持久化测试

#### 内存泄漏检查 ✅
- 使用 Valgrind 或 Application Verifier 检查
- 长时间运行测试（大量数据收发）
- 频繁连接/断开测试

#### 异常处理完善 ✅
- 串口突然断开的处理
- 大量数据时的缓冲区溢出保护
- 无效VT100序列的容错处理
- 程序退出崩溃修复

### 2. 用户体验优化 🎨 (中高优先级)

#### 自动换行功能完善 ✅ 已完成
- 实现模式切换时保持内容的重新布局
- 窗口缩放时的动态重新布局
- 性能优化（大量文本时的换行计算）

#### 十六进制显示模式 ✅ 已完成
- 实现ASCII/HEX/混合三种显示模式
- 添加视图菜单切换选项，支持实时切换
- 完成DataFormatter数据格式化工具类
- 修复自动滚动兼容性问题
- 完整测试覆盖和功能验证

#### UI/UX 现代化改进 ✅ 已完成
- ✅ Windows 11 风格界面设计
- ✅ 深色/亮色主题支持（参考Win11默认主题）
- ✅ 蓝色主题强调色系统（与应用图标呼应）
- ✅ 智能系统主题跟随功能
- ✅ 现代化控件样式（按钮、输入框、菜单等）
- ✅ 完整的对话框主题支持
- ✅ 滚动条主题统一
- ✅ 控件尺寸和对齐优化
- ✅ 字体渲染优化（ClearType、抗锯齿、等宽字体过滤）
- ✅ 状态栏信息丰富化（数据统计、连接时长、传输速率）
- ✅ 快捷键支持完善（VT100兼容性设计、Alt组合键）
- ✅ Toolbar高度优化和连接按钮合并

### 3. 功能扩展 ⚡ (中优先级)

#### 数据处理增强
- 数据过滤和高亮
- 时间戳显示
- 数据统计（发送/接收字节数、速率）

#### 协议支持
- Modbus 协议解析
- 自定义协议定义
- 数据包分析工具

#### 文件操作
- 发送文件功能
- 接收数据保存为文件
- 会话录制和回放

### 4. 开发工具和流程 🛠️ (中低优先级)

#### CI/CD 流程
- GitHub Actions 自动构建
- 多平台编译测试
- 自动化测试运行
- Release 包自动生成

#### 文档完善
- API 文档生成
- 用户手册
- 开发者指南
- 架构设计文档

## 🔗 集成测试说明

### 集成测试 vs 单元测试
- **单元测试** ✅：已完成，测试单个组件的功能正确性
- **集成测试** 🚧：测试组件间协作和完整用户场景

### 集成测试内容
1. **完整串口通信测试**：
   - 真实串口设备连接测试
   - 大数据量收发测试
   - 长时间连接稳定性测试

2. **用户场景测试**：
   - 完整的打开程序→连接串口→收发数据→关闭程序流程
   - 多实例运行测试
   - 设置更改后的功能验证

3. **性能和兼容性测试**：
   - 不同波特率下的性能测试
   - 不同操作系统的兼容性
   - 不同串口设备的兼容性

### 集成测试挑战
- 需要真实硬件环境
- 测试时间较长
- 环境配置复杂
- 结果可能受外部因素影响

## 🎯 具体建议

### 立即可做（1-2天）
1. ~~编写基础单元测试：特别是 VT100Parser 和 SerialProcessor~~ ✅ 已完成
2. ~~完善异常处理：串口断开、解析错误等场景~~ ✅ 已完成
3. 建立集成测试框架：为真实硬件环境测试做准备
4. UI 小优化：状态栏信息、快捷键提示

### 短期目标（1-2周）
1. 完善自动换行功能：实现内容保持的重新布局 ✅ 已完成
2. 添加十六进制显示模式：这是串口工具的常见需求
3. 深色主题支持：现代应用的标配

### 中期目标（1个月）
1. 数据统计和分析功能
2. 文件发送/接收功能
3. 协议解析框架

## 💡 推荐的下一步

**建议优先级调整**：

1. **集成测试框架建设**（高优先级）
   - 为质量保证打下基础
   - 验证单元测试未覆盖的场景

2. **十六进制显示模式**（中高优先级）
   - 用户价值高：串口调试经常需要查看原始字节
   - 技术难度适中：不会太复杂，但有一定挑战性
   - 架构影响小：主要是显示层的修改
   - 测试容易：功能明确，容易验证

这个功能可以：
- 在视图菜单添加"十六进制模式"切换
- 在设置中添加默认显示模式选项
- 支持ASCII和HEX的混合显示

## 📋 任务管理

### 已完成：代码质量和稳定性改进 ✅

#### 🧪 测试框架建设 ✅
- [x] 建立单元测试框架 - 完成Qt Test框架集成，支持CMake和CTest
- [x] SerialProcessor单元测试 - 完成串口处理类的完整测试用例
- [x] VT100Parser单元测试 - 完成VT100解析器的各种控制序列解析测试
- [x] HighPerformanceTerminal渲染测试 - 完成终端渲染的正确性测试
- [x] 设置持久化测试 - 完成应用设置的加载和保存功能验证

#### 🔍 稳定性测试 ✅
- [x] 内存泄漏检查和修复 - 完成内存泄漏检测测试用例
- [x] 长时间运行稳定性测试 - 完成高负载下的程序稳定性测试
- [x] 频繁连接断开测试 - 完成资源管理的正确性测试

#### 🛡️ 异常处理完善 ✅
- [x] 异常处理完善 - 完成程序整体容错能力测试
- [x] 串口突然断开处理 - 完成硬件异常断开情况处理测试
- [x] 缓冲区溢出保护 - 完成大量数据导致的内存问题防护测试
- [x] 无效VT100序列容错 - 完成不规范控制序列的处理测试
- [x] 程序退出崩溃修复 - 修复MainWindow析构时的Debug Error问题

### 当前进行中：集成测试和用户体验优化

#### 🔗 集成测试框架建设
- [ ] 完整串口通信集成测试 - 端到端的串口通信流程测试
- [ ] 硬件兼容性测试 - 与真实串口设备的兼容性验证
- [ ] 用户场景集成测试 - 完整的用户使用场景测试
- [ ] 性能基准测试 - 大数据量处理的性能测试

### 已完成：自动换行功能 ✅
- [x] 添加自动换行设置项
- [x] 在视图菜单中添加切换选项
- [x] 扩展终端支持换行模式
- [x] 适配文本渲染逻辑
- [x] 适配滚动条逻辑
- [x] 适配文本选择和复制功能
- [x] 适配搜索功能
- [x] 处理模式切换的数据重组
- [x] 测试和优化

### 已完成：UI/UX 现代化改进 ✅

#### 📊 状态栏信息丰富化 ✅
- [x] 数据统计功能：发送/接收字节数统计
- [x] 传输速率显示：实时计算字节/秒传输速率
- [x] 连接时长统计：显示连接持续时间
- [x] 智能格式化：字节数自动转换单位(B/KB/MB/GB)
- [x] 双标签状态栏：左侧状态信息，右侧统计信息

#### ⌨️ 快捷键支持完善 ✅
- [x] VT100兼容性设计：使用Alt组合键避免与VT100控制序列冲突
- [x] 串口操作快捷键：连接/断开切换(Alt+C)、刷新(Alt+R)
- [x] 视图操作快捷键：清屏(Alt+L)、查找(Alt+F)、换行(Alt+W)
- [x] 显示模式快捷键：ASCII(Alt+1)、十六进制(Alt+2)、混合(Alt+3)
- [x] 主题和窗口快捷键：主题切换、窗口置顶等
- [x] 智能快捷键管理：支持动态启用/禁用，预留用户自定义接口

#### 🎨 字体渲染优化 ✅
- [x] 等宽字体过滤：设置界面只显示等宽字体，性能优化(18ms)
- [x] 抗锯齿优化：启用TextAntialiasing和SmoothPixmapTransform
- [x] ClearType支持：Windows平台启用子像素渲染技术
- [x] 字体提示优化：使用PreferFullHinting提升小字号清晰度
- [x] 跨平台兼容：针对不同操作系统的字体渲染优化

#### 🛠️ Toolbar界面优化 ✅
- [x] 高度优化：降低toolbar高度，统一按钮尺寸
- [x] 连接按钮合并：将连接/断开合并为智能切换按钮
- [x] 状态视觉反馈：连接/断开状态的明显颜色区分
- [x] 最小窗口宽度：设置800px确保所有控件可见
- [x] 紧凑样式设计：使用主题管理器的颜色系统
