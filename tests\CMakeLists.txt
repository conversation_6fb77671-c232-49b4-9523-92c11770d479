# SerialT 单元测试配置

cmake_minimum_required(VERSION 3.16)

# 启用Qt Test模块
find_package(Qt6 REQUIRED COMPONENTS Test)

# Clang-Tidy is now configured on a per-target basis in the root CMakeLists.txt.
# No override is necessary here, so tests will compile without clang-tidy, speeding up the build.

# 设置测试输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests)

# 包含源代码目录
include_directories(${CMAKE_SOURCE_DIR}/src)

# 创建测试可执行文件的通用函数
function(add_qt_test test_name test_sources)
    # 创建测试可执行文件
    add_executable(${test_name} ${test_sources})

    # 链接Qt Test库和项目源文件
    target_link_libraries(${test_name} 
        Qt6::Test
        Qt6::Core
        Qt6::Widgets
        Qt6::SerialPort
    )

    # 添加源文件依赖（需要测试的类）
    target_sources(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/SerialProcessor.cpp
        ${CMAKE_SOURCE_DIR}/src/VT100Parser.cpp
        ${CMAKE_SOURCE_DIR}/src/HighPerformanceTerminal.cpp
        ${CMAKE_SOURCE_DIR}/src/CommandParser.cpp
        ${CMAKE_SOURCE_DIR}/src/SettingsDialog.cpp
        ${CMAKE_SOURCE_DIR}/src/DataFormatter.cpp
        ${CMAKE_SOURCE_DIR}/src/Logger.cpp
    )
    
    # 设置编译属性
    set_target_properties(${test_name} PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # 添加到CTest
    add_test(NAME ${test_name} COMMAND ${test_name})
    
    # 设置测试属性
    if(test_name STREQUAL "test_long_running_stability")
        set_tests_properties(${test_name} PROPERTIES
            TIMEOUT 120
            WORKING_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
        )
    else()
        set_tests_properties(${test_name} PROPERTIES
            TIMEOUT 30
            WORKING_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
        )
    endif()
endfunction()

# 启用CTest
enable_testing()

# 添加各个测试模块
add_subdirectory(unit)
add_subdirectory(integration)

# 创建运行所有测试的目标
add_custom_target(run_all_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --verbose
    DEPENDS
        test_serial_processor
        test_vt100_parser
        test_terminal_rendering
        test_settings_persistence
        test_memory_leaks
        test_long_running_stability
        test_auto_reconnect
    COMMENT "Running all unit tests"
)

# 创建测试覆盖率报告目标（可选）
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    find_program(GCOV_PATH gcov)
    if(GCOV_PATH)
        add_custom_target(coverage
            COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
            COMMAND gcov ${CMAKE_BINARY_DIR}/tests/*.gcno
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Generating test coverage report"
        )
    endif()
endif()
