#ifndef APPTYPES_H
#define APPTYPES_H

#include <cstdint>

/**
 * @brief 数据显示模式的强类型枚举。
 */
enum class DisplayMode : std::uint8_t
{
    ASCII = 0, // ASCII文本模式
    HEX = 1,   // 十六进制模式
    MIXED = 2  // 混合模式（ASCII + HEX）
};

/**
 * @brief 换行符模式的强类型枚举。
 */
enum class NewLineMode : std::uint8_t
{
    CrLf, ///< 回车换行 (CR+LF, "\\r\\n")
    Cr,   ///< 回车 (CR, "\\r")
    Lf    ///< 换行 (LF, "\\n")
};

/**
 * @brief 连接状态的强类型枚举。
 */
enum class ConnectionState : std::uint8_t
{
    Disconnected, // 初始状态或最终断开状态
    Connected,    // 稳定连接状态
    Reconnecting, // 重连尝试状态
    Failed        // 重连失败状态
};

#endif // APPTYPES_H
