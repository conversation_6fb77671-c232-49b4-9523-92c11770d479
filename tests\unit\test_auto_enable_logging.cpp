#include <QApplication>
#include <QDir>
#include <QSettings>
#include <QSignalSpy>
#include <QTest>
#include <QTimer>
#include <QtTest/QtTest>

#include "../../src/SettingsDialog.h"

class TestAutoEnableLogging : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    void testAutoEnableLogSettingPersistence();
    void testAutoEnableLogSettingDefault();
    void testAutoEnableLogSettingInAppSettings();

private:
    // 辅助方法
    void clearTestSettings();
    AppSettings createTestSettings();
};

void TestAutoEnableLogging::initTestCase()
{
    if (!QApplication::instance())
    {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestAutoEnableLogging::cleanupTestCase() {}

void TestAutoEnableLogging::init()
{
    clearTestSettings();
}

void TestAutoEnableLogging::cleanup()
{
    clearTestSettings();
}

void TestAutoEnableLogging::testAutoEnableLogSettingPersistence()
{
    // 测试自动启用日志设置的持久化

    // Arrange: 创建测试设置
    AppSettings settings = createTestSettings();
    settings.autoEnableLogOnPortOpen = false; // 设置为非默认值

    // Act: 保存设置
    QSettings qsettings("LSDT", "SerialT");
    qsettings.beginGroup("Logging");
    qsettings.setValue("autoEnableLogOnPortOpen", settings.autoEnableLogOnPortOpen);
    qsettings.endGroup();

    // 读取设置
    qsettings.beginGroup("Logging");
    bool loadedValue = qsettings.value("autoEnableLogOnPortOpen", true).toBool();
    qsettings.endGroup();

    // Assert: 验证设置被正确保存和读取
    QCOMPARE(loadedValue, false);
}

void TestAutoEnableLogging::testAutoEnableLogSettingDefault()
{
    // 测试自动启用日志设置的默认值

    // Arrange: 清除所有设置
    clearTestSettings();

    // Act: 读取默认值
    QSettings qsettings("LSDT", "SerialT");
    qsettings.beginGroup("Logging");
    bool defaultValue = qsettings.value("autoEnableLogOnPortOpen", true).toBool();
    qsettings.endGroup();

    // Assert: 验证默认值为true
    QCOMPARE(defaultValue, true);
}

void TestAutoEnableLogging::testAutoEnableLogSettingInAppSettings()
{
    // 测试AppSettings结构体中包含autoEnableLogOnPortOpen字段

    // Arrange: 创建AppSettings实例
    AppSettings settings = createTestSettings();

    // Act & Assert: 验证字段存在且可以设置
    settings.autoEnableLogOnPortOpen = true;
    QCOMPARE(settings.autoEnableLogOnPortOpen, true);

    settings.autoEnableLogOnPortOpen = false;
    QCOMPARE(settings.autoEnableLogOnPortOpen, false);
}

// 辅助方法实现
void TestAutoEnableLogging::clearTestSettings()
{
    QSettings settings("LSDT", "SerialT");
    settings.clear();
}

AppSettings TestAutoEnableLogging::createTestSettings()
{
    AppSettings settings;

    // 设置基本的测试值
    settings.minimizeToTrayOnClose = true;
    settings.alwaysShowInTray = true;
    settings.windowStaysOnTop = false;
    settings.terminalBufferSize = 5000;
    settings.terminalScrollPolicy = 0;
    settings.scrollOnInput = true;
    settings.pauseOnResize = false;
    settings.wordWrapEnabled = false;
    settings.displayMode = DisplayMode::ASCII;

    settings.portName = "COM1";
    settings.baudRate = 115200;
    settings.dataBits = QSerialPort::Data8;
    settings.parity = QSerialPort::NoParity;
    settings.stopBits = QSerialPort::OneStop;
    settings.sendNewLineMode = 0;
    settings.serialEchoEnabled = false;

    settings.logPath = QDir::tempPath();
    settings.logFileNameFormat = "test_%Y-%m-%d_%H-%M-%S.log";
    settings.logTimestampEnabled = true;
    settings.logTimestampFormat = "[yyyy-MM-dd hh:mm:ss.zzz] ";
    settings.logSplitEnabled = false;
    settings.logSplitSizeMB = 10;
    settings.useDefaultLogViewer = true;
    settings.externalLogViewerPath = "";
    settings.autoEnableLogOnPortOpen = true; // 默认值

    settings.terminalFont = QFont("Courier New", 10);

    return settings;
}

#include "test_auto_enable_logging.moc"
QTEST_MAIN(TestAutoEnableLogging)
