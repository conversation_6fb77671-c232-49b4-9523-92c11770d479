#!/usr/bin/env python3
"""
多实例并发测试
测试多个SerialT实例同时运行时的稳定性、资源管理和并发安全性
"""

import sys
import time
import serial
import threading
import subprocess
import psutil
import random
import string
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from config import SERIAL_CONFIG, VT100_SEQUENCES

class MultiInstanceTester:
    """多实例并发测试器"""
    
    def __init__(self):
        self.test_ports = [
            ('COM30', 'COM31'),  # 实例1
            # 注意：这里我们只有一对虚拟串口，所以先测试单对多连接
            # 如果有更多虚拟串口对，可以添加更多
        ]
        self.test_instances = []
        self.running = False
        self.results = {}
        
    def check_system_resources(self):
        """检查系统资源状态"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            print(f"📊 系统资源状态:")
            print(f"   CPU使用率: {cpu_percent:.1f}%")
            print(f"   内存使用率: {memory.percent:.1f}%")
            print(f"   可用内存: {memory.available / 1024 / 1024:.0f} MB")
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_mb': memory.available / 1024 / 1024
            }
        except Exception as e:
            print(f"❌ 获取系统资源信息失败: {e}")
            return None
    
    def find_serialt_processes(self):
        """查找SerialT进程"""
        serialt_processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                if 'SerialT' in proc.info['name']:
                    serialt_processes.append(proc)
            
            print(f"🔍 发现 {len(serialt_processes)} 个SerialT进程:")
            for proc in serialt_processes:
                memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                print(f"   PID {proc.info['pid']}: 内存 {memory_mb:.1f}MB")
            
            return serialt_processes
        except Exception as e:
            print(f"❌ 查找SerialT进程失败: {e}")
            return []
    
    def create_test_instance(self, instance_id, test_port):
        """创建测试实例"""
        return {
            'id': instance_id,
            'port': test_port,
            'serial': None,
            'thread': None,
            'data_sent': 0,
            'data_received': 0,
            'errors': 0,
            'start_time': None
        }
    
    def connect_instance(self, instance):
        """连接实例到串口"""
        try:
            instance['serial'] = serial.Serial(
                port=instance['port'],
                baudrate=SERIAL_CONFIG['DEFAULT_BAUDRATE'],
                timeout=1.0
            )
            print(f"✅ 实例{instance['id']} 已连接到 {instance['port']}")
            return True
        except Exception as e:
            print(f"❌ 实例{instance['id']} 连接失败: {e}")
            instance['errors'] += 1
            return False
    
    def disconnect_instance(self, instance):
        """断开实例连接"""
        try:
            if instance['serial'] and instance['serial'].is_open:
                instance['serial'].close()
                print(f"🔌 实例{instance['id']} 已断开连接")
        except Exception as e:
            print(f"❌ 实例{instance['id']} 断开连接失败: {e}")
            instance['errors'] += 1
    
    def instance_worker(self, instance, test_duration=60):
        """实例工作线程"""
        instance['start_time'] = time.time()
        
        print(f"🚀 实例{instance['id']} 开始工作 (持续{test_duration}秒)")
        
        # 连接串口
        if not self.connect_instance(instance):
            return
        
        try:
            end_time = time.time() + test_duration
            message_count = 0
            
            while time.time() < end_time and self.running:
                # 发送不同类型的测试数据
                message_count += 1
                data_type = random.choice(['text', 'vt100', 'numbers'])
                
                if data_type == 'text':
                    data = f"Instance{instance['id']} Message{message_count}: " + \
                           ''.join(random.choices(string.ascii_letters, k=20)) + "\n"
                elif data_type == 'vt100':
                    color = random.choice(['FG_RED', 'FG_GREEN', 'FG_BLUE'])
                    data = VT100_SEQUENCES[color] + f"Instance{instance['id']} Colored{message_count}" + \
                           VT100_SEQUENCES['RESET'] + "\n"
                else:  # numbers
                    data = f"Instance{instance['id']} Numbers{message_count}: " + \
                           ''.join(random.choices(string.digits, k=10)) + "\n"
                
                # 发送数据
                try:
                    instance['serial'].write(data.encode('utf-8'))
                    instance['serial'].flush()
                    instance['data_sent'] += len(data)
                    
                    # 检查是否有回应数据
                    if instance['serial'].in_waiting > 0:
                        received = instance['serial'].read(instance['serial'].in_waiting)
                        instance['data_received'] += len(received)
                    
                except Exception as e:
                    print(f"❌ 实例{instance['id']} 发送数据失败: {e}")
                    instance['errors'] += 1
                
                # 随机间隔，模拟真实使用
                time.sleep(random.uniform(0.1, 0.5))
            
            print(f"✅ 实例{instance['id']} 工作完成")
            
        finally:
            self.disconnect_instance(instance)
    
    def test_single_port_multiple_connections(self):
        """测试单个串口的多连接处理"""
        print("\n🔗 测试场景1: 单串口多连接处理")
        print("=" * 50)
        
        print("📋 测试SerialT如何处理多个连接尝试到同一串口")
        
        # 尝试多次连接到同一个端口
        test_port = 'COM31'
        connections = []
        
        print(f"🔸 尝试建立多个到 {test_port} 的连接...")
        
        for i in range(3):
            try:
                conn = serial.Serial(test_port, SERIAL_CONFIG['DEFAULT_BAUDRATE'], timeout=1)
                connections.append(conn)
                print(f"   连接 {i+1}: ✅ 成功")
            except Exception as e:
                print(f"   连接 {i+1}: ❌ 失败 - {e}")
        
        print(f"📊 成功建立 {len(connections)} 个连接")
        
        # 清理连接
        for i, conn in enumerate(connections):
            try:
                conn.close()
                print(f"   关闭连接 {i+1}: ✅")
            except Exception as e:
                print(f"   关闭连接 {i+1}: ❌ {e}")
        
        return len(connections)
    
    def test_concurrent_data_transmission(self):
        """测试并发数据传输"""
        print("\n📡 测试场景2: 并发数据传输")
        print("=" * 50)
        
        print("📋 使用多个线程同时向SerialT发送数据")
        
        # 创建测试实例
        num_instances = 3  # 使用3个并发线程
        instances = []
        
        for i in range(num_instances):
            instance = self.create_test_instance(i+1, 'COM31')
            instances.append(instance)
        
        # 记录开始时的系统资源
        print("📊 测试开始前的系统资源:")
        start_resources = self.check_system_resources()
        
        # 启动并发测试
        self.running = True
        threads = []
        
        print(f"🚀 启动 {num_instances} 个并发测试线程...")
        
        for instance in instances:
            thread = threading.Thread(
                target=self.instance_worker, 
                args=(instance, 30),  # 30秒测试
                daemon=True
            )
            thread.start()
            threads.append(thread)
            instance['thread'] = thread
            time.sleep(1)  # 错开启动时间
        
        # 监控测试进度
        print("⏳ 监控测试进度...")
        for i in range(6):  # 30秒，每5秒检查一次
            time.sleep(5)
            print(f"   进度: {(i+1)*5}/30 秒")
            
            # 检查系统资源
            current_resources = self.check_system_resources()
            if current_resources:
                cpu_change = current_resources['cpu_percent'] - start_resources['cpu_percent']
                memory_change = current_resources['memory_percent'] - start_resources['memory_percent']
                print(f"   资源变化: CPU {cpu_change:+.1f}%, 内存 {memory_change:+.1f}%")
        
        # 停止测试
        self.running = False
        
        # 等待所有线程完成
        print("⏹️ 等待所有测试线程完成...")
        for thread in threads:
            thread.join(timeout=5)
        
        # 统计结果
        print("\n📊 并发测试结果:")
        total_sent = 0
        total_received = 0
        total_errors = 0
        
        for instance in instances:
            duration = time.time() - instance['start_time'] if instance['start_time'] else 0
            print(f"   实例{instance['id']}:")
            print(f"     运行时间: {duration:.1f}秒")
            print(f"     发送数据: {instance['data_sent']} 字节")
            print(f"     接收数据: {instance['data_received']} 字节")
            print(f"     错误次数: {instance['errors']}")
            
            total_sent += instance['data_sent']
            total_received += instance['data_received']
            total_errors += instance['errors']
        
        print(f"\n📈 总计统计:")
        print(f"   总发送: {total_sent} 字节")
        print(f"   总接收: {total_received} 字节")
        print(f"   总错误: {total_errors} 次")
        print(f"   错误率: {total_errors/(total_sent/100) if total_sent > 0 else 0:.2f}%")
        
        # 记录结束时的系统资源
        print("\n📊 测试结束后的系统资源:")
        end_resources = self.check_system_resources()
        
        return {
            'instances': len(instances),
            'total_sent': total_sent,
            'total_received': total_received,
            'total_errors': total_errors,
            'start_resources': start_resources,
            'end_resources': end_resources
        }
    
    def test_serialt_process_monitoring(self):
        """测试SerialT进程监控"""
        print("\n🔍 测试场景3: SerialT进程监控")
        print("=" * 50)
        
        print("📋 监控SerialT进程的资源使用情况")
        
        # 查找SerialT进程
        processes = self.find_serialt_processes()
        
        if not processes:
            print("⚠️ 未发现运行中的SerialT进程")
            print("   请确保至少有一个SerialT实例正在运行")
            return None
        
        # 监控进程资源使用
        print("📊 开始监控进程资源使用 (30秒)...")
        
        monitoring_data = []
        
        for i in range(6):  # 30秒，每5秒采样一次
            timestamp = time.time()
            sample_data = {
                'timestamp': timestamp,
                'processes': []
            }
            
            for proc in processes:
                try:
                    proc_info = {
                        'pid': proc.pid,
                        'memory_mb': proc.memory_info().rss / 1024 / 1024,
                        'cpu_percent': proc.cpu_percent(),
                        'status': proc.status()
                    }
                    sample_data['processes'].append(proc_info)
                    
                    print(f"   PID {proc_info['pid']}: "
                          f"内存 {proc_info['memory_mb']:.1f}MB, "
                          f"CPU {proc_info['cpu_percent']:.1f}%")
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    print(f"   PID {proc.pid}: 进程已退出或无法访问")
            
            monitoring_data.append(sample_data)
            
            if i < 5:  # 最后一次不需要等待
                time.sleep(5)
        
        # 分析监控数据
        print("\n📈 进程监控分析:")
        
        if monitoring_data:
            for proc_data in monitoring_data[0]['processes']:
                pid = proc_data['pid']
                memory_samples = [sample['processes'][0]['memory_mb'] 
                                for sample in monitoring_data 
                                if sample['processes']]
                
                if memory_samples:
                    avg_memory = sum(memory_samples) / len(memory_samples)
                    max_memory = max(memory_samples)
                    min_memory = min(memory_samples)
                    
                    print(f"   PID {pid}:")
                    print(f"     平均内存: {avg_memory:.1f}MB")
                    print(f"     最大内存: {max_memory:.1f}MB")
                    print(f"     最小内存: {min_memory:.1f}MB")
                    print(f"     内存变化: {max_memory - min_memory:.1f}MB")
        
        return monitoring_data
    
    def test_stress_multiple_instances(self):
        """测试多实例压力场景"""
        print("\n⚡ 测试场景4: 多实例压力测试")
        print("=" * 50)
        
        print("📋 模拟多个SerialT实例同时处理大量数据")
        
        # 提示用户启动多个实例
        print("⚠️ 此测试需要手动启动多个SerialT实例")
        print("   建议步骤:")
        print("   1. 启动第一个SerialT实例，连接COM30")
        print("   2. 如果有更多虚拟串口，启动更多实例")
        print("   3. 观察系统资源使用情况")
        
        input("准备就绪后按回车键继续...")
        
        # 检查当前SerialT进程
        initial_processes = self.find_serialt_processes()
        print(f"📊 检测到 {len(initial_processes)} 个SerialT进程")
        
        # 执行压力测试
        print("🚀 开始压力测试...")
        
        # 使用更高强度的并发测试
        stress_result = self.test_concurrent_data_transmission()
        
        # 再次检查进程状态
        final_processes = self.find_serialt_processes()
        print(f"📊 测试后检测到 {len(final_processes)} 个SerialT进程")
        
        # 检查是否有进程崩溃
        if len(final_processes) < len(initial_processes):
            print("⚠️ 检测到进程数量减少，可能有实例崩溃")
        else:
            print("✅ 所有进程保持稳定")
        
        return {
            'initial_processes': len(initial_processes),
            'final_processes': len(final_processes),
            'stress_result': stress_result
        }
    
    def run_all_tests(self):
        """运行所有多实例测试"""
        print("🧪 SerialT 多实例并发测试")
        print("=" * 60)
        print("本测试将验证多个SerialT实例的并发运行能力")
        print("请确保至少有一个SerialT实例正在运行")
        print()
        
        # 初始系统状态检查
        print("📊 初始系统状态检查:")
        initial_resources = self.check_system_resources()
        initial_processes = self.find_serialt_processes()
        
        if not initial_processes:
            print("❌ 未发现运行中的SerialT进程")
            print("   请启动至少一个SerialT实例后重新运行测试")
            return False
        
        try:
            test_scenarios = [
                ("单串口多连接处理", self.test_single_port_multiple_connections),
                ("并发数据传输", self.test_concurrent_data_transmission),
                ("进程监控", self.test_serialt_process_monitoring),
                ("多实例压力测试", self.test_stress_multiple_instances),
            ]
            
            results = {}
            
            for scenario_name, test_func in test_scenarios:
                print(f"\n🔄 开始执行: {scenario_name}")
                try:
                    result = test_func()
                    results[scenario_name] = result
                    print(f"✅ '{scenario_name}' 执行完成")
                except Exception as e:
                    print(f"❌ '{scenario_name}' 执行异常: {e}")
                    results[scenario_name] = None
                
                # 测试间暂停
                if scenario_name != "多实例压力测试":
                    print("⏸️ 测试间暂停 5 秒...")
                    time.sleep(5)
            
            # 生成最终报告
            self.generate_final_report(results, initial_resources, initial_processes)
            
            return True
            
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
            self.running = False
            return False
        except Exception as e:
            print(f"\n❌ 测试执行异常: {e}")
            return False
    
    def generate_final_report(self, results, initial_resources, initial_processes):
        """生成最终测试报告"""
        print(f"\n📊 多实例并发测试报告")
        print("=" * 60)
        
        # 测试完成情况
        completed_tests = sum(1 for result in results.values() if result is not None)
        total_tests = len(results)
        
        print(f"测试完成情况: {completed_tests}/{total_tests}")
        print(f"成功率: {completed_tests/total_tests*100:.1f}%")
        
        print(f"\n详细结果:")
        for test_name, result in results.items():
            status = "✅ 完成" if result is not None else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        # 系统资源分析
        print(f"\n系统资源分析:")
        print(f"  初始SerialT进程: {len(initial_processes)} 个")
        
        if initial_resources:
            print(f"  初始CPU使用率: {initial_resources['cpu_percent']:.1f}%")
            print(f"  初始内存使用率: {initial_resources['memory_percent']:.1f}%")
        
        # 并发测试特定分析
        if results.get("并发数据传输"):
            concurrent_result = results["并发数据传输"]
            print(f"\n并发测试分析:")
            print(f"  并发实例数: {concurrent_result['instances']}")
            print(f"  总数据传输: {concurrent_result['total_sent']} 字节")
            print(f"  错误次数: {concurrent_result['total_errors']}")
            
            if concurrent_result['total_errors'] == 0:
                print("  ✅ 并发测试无错误，稳定性优秀")
            else:
                error_rate = concurrent_result['total_errors'] / concurrent_result['instances']
                print(f"  ⚠️ 平均每实例错误: {error_rate:.1f} 次")
        
        # 总结评价
        print(f"\n🎯 测试总结:")
        if completed_tests == total_tests:
            print("🎉 所有多实例测试通过！")
            print("SerialT在并发环境下表现稳定可靠")
        else:
            print("⚠️ 部分测试未完成，需要进一步检查")
        
        print(f"\n建议:")
        print("- 继续监控长时间运行的稳定性")
        print("- 在不同硬件配置下进行测试")
        print("- 测试更多虚拟串口对的并发场景")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SerialT 多实例并发测试')
    
    args = parser.parse_args()
    
    print("🎯 SerialT 多实例并发测试")
    print("=" * 60)
    print("⚠️ 测试要求:")
    print("1. 至少启动一个SerialT实例并连接到COM30")
    print("2. 确保系统有足够的资源")
    print("3. 准备观察系统资源使用情况")
    print()
    
    input("准备就绪后按回车键开始测试...")
    
    tester = MultiInstanceTester()
    
    try:
        success = tester.run_all_tests()
        return success
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
