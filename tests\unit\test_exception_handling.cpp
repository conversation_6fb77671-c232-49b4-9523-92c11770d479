#include <QDebug>
#include <QSignalSpy>
#include <QTimer>
#include <QtTest/QtTest>

#include "../../src/SerialProcessor.h"
#include "../../src/core/AppTypes.h"
#include "core/AppTypes.h"

class TestExceptionHandling : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 异常处理测试
    void testInvalidPortNameHandling();
    void testInvalidBaudRateHandling();
    void testWriteToClosedPortHandling();
    void testEmptyDataHandling();
    void testErrorSignalEmission();
    void testLastErrorRetrieval();
    void testConnectionLostSignal();
    void testNullPointerSafety();
    void testParameterValidation();

private:
    SerialProcessor *m_processor;
};

void TestExceptionHandling::initTestCase()
{
    qDebug() << "Starting exception handling tests";
}

void TestExceptionHandling::cleanupTestCase()
{
    qDebug() << "Exception handling tests completed";
}

void TestExceptionHandling::init()
{
    m_processor = new SerialProcessor(this);
}

void TestExceptionHandling::cleanup()
{
    if (m_processor)
    {
        if (m_processor->isOpen())
        {
            m_processor->closePort();
        }
        delete m_processor;
        m_processor = nullptr;
    }
}

void TestExceptionHandling::testInvalidPortNameHandling()
{
    // 测试空端口名处理
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);

    bool result = m_processor->openPort("", 9600);

    QVERIFY(!result);
    QVERIFY(!m_processor->isOpen());
    QCOMPARE(errorSpy.count(), 1);

    QString lastError = m_processor->getLastError();
    QVERIFY(lastError.contains("端口名称不能为空"));
}

void TestExceptionHandling::testInvalidBaudRateHandling()
{
    // 测试无效波特率处理
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);

    bool result1 = m_processor->openPort("COM1", -1);
    QVERIFY(!result1);
    QCOMPARE(errorSpy.count(), 1);

    bool result2 = m_processor->openPort("COM1", 0);
    QVERIFY(!result2);
    QCOMPARE(errorSpy.count(), 2);

    QString lastError = m_processor->getLastError();
    QVERIFY(lastError.contains("无效的波特率"));
}

void TestExceptionHandling::testWriteToClosedPortHandling()
{
    // 测试向关闭的端口写入数据
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);

    // 确保端口是关闭的
    QVERIFY(!m_processor->isOpen());

    // 尝试写入数据
    QByteArray testData = "test data";
    m_processor->writeData(testData);

    QCOMPARE(errorSpy.count(), 1);
    QString lastError = m_processor->getLastError();
    QVERIFY(lastError.contains("串口未打开"));

    // 测试原始数据写入
    m_processor->writeDataRaw(testData);
    QCOMPARE(errorSpy.count(), 2);
}

void TestExceptionHandling::testEmptyDataHandling()
{
    // 测试空数据处理
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);

    // 空数据不应该产生错误
    m_processor->writeData(QByteArray());
    m_processor->writeDataRaw(QByteArray());

    // 对于关闭的端口，空数据仍然会产生错误
    QCOMPARE(errorSpy.count(), 2);
}

void TestExceptionHandling::testErrorSignalEmission()
{
    // 测试错误信号的发射
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);
    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 尝试打开不存在的端口
    bool result = m_processor->openPort("COM999", 9600);

    QVERIFY(!result);
    QVERIFY(!m_processor->isOpen());

    // 应该有错误信号但没有连接状态变化信号（因为连接失败）
    QVERIFY(errorSpy.count() > 0);
    QCOMPARE(connectionSpy.count(), 0);

    // 检查错误信息
    QString lastError = m_processor->getLastError();
    QVERIFY(!lastError.isEmpty());
    QVERIFY(lastError.contains("打开端口失败"));
}

void TestExceptionHandling::testLastErrorRetrieval()
{
    // 测试错误信息的获取
    QString initialError = m_processor->getLastError();
    QVERIFY(initialError.isEmpty()); // 初始状态应该没有错误

    // 触发一个错误
    m_processor->openPort("", 9600);

    QString errorAfterFailure = m_processor->getLastError();
    QVERIFY(!errorAfterFailure.isEmpty());
    QVERIFY(errorAfterFailure.contains("端口名称不能为空"));

    // 成功操作后错误应该被清除
    // 注意：由于我们使用的是不存在的端口，这里仍然会失败
    // 但错误信息会被更新
    QString previousError = errorAfterFailure;
    m_processor->openPort("COM999", 9600);
    QString newError = m_processor->getLastError();
    QVERIFY(newError != previousError); // 错误信息应该更新
}

void TestExceptionHandling::testConnectionLostSignal()
{
    // 测试连接丢失信号
    // 这个测试主要验证信号的连接是否正确
    QSignalSpy connectionLostSpy(m_processor, &SerialProcessor::connectionLost);

    // 由于我们无法模拟真实的连接丢失，这里主要测试信号连接
    QVERIFY(connectionLostSpy.isValid());

    // 验证信号槽连接正常
    QCOMPARE(connectionLostSpy.count(), 0);
}

void TestExceptionHandling::testNullPointerSafety()
{
    // 测试空指针安全性
    QVERIFY(m_processor != nullptr);
    QVERIFY(m_processor->parser() != nullptr);

    // 测试在各种状态下对象的有效性
    m_processor->setEchoEnabled(true);
    m_processor->setNewLineMode(NewLineMode::CrLf);
    m_processor->setNewLineMode(NewLineMode::Cr);
    m_processor->setNewLineMode(NewLineMode::Lf);
    // m_processor->setNewLineMode(999); // 无效值，重构成enum class后不再需要，编译器会拦截

    // 对象应该仍然有效
    QVERIFY(m_processor->parser() != nullptr);
}

void TestExceptionHandling::testParameterValidation()
{
    // 测试参数验证
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);

    // 测试各种无效参数组合
    struct TestCase
    {
        QString portName;
        int baudRate;
        bool shouldFail;
        QString expectedErrorKeyword;
    };

    QList<TestCase> testCases = {
        {"", 9600, true, "端口名称不能为空"},
        {"COM1", -1, true, "无效的波特率"},
        {"COM1", 0, true, "无效的波特率"},
        {"COM999", 9600, true, "打开端口失败"}, // 端口不存在
    };

    Q_UNUSED(errorSpy.count()) // 记录初始错误计数

    for (const auto &testCase : testCases)
    {
        qsizetype errorCountBefore = errorSpy.count();
        bool result = m_processor->openPort(testCase.portName, testCase.baudRate);
        qsizetype errorCountAfter = errorSpy.count();

        if (testCase.shouldFail)
        {
            QVERIFY(!result);
            // 每次失败的openPort调用应该产生一个错误信号
            QVERIFY(errorCountAfter > errorCountBefore);

            QString lastError = m_processor->getLastError();
            QVERIFY(lastError.contains(testCase.expectedErrorKeyword));
        }
        else
        {
            // 对于应该成功的情况（如果有的话）
            Q_UNUSED(result) // 在测试环境中可能仍然失败
        }

        // 确保端口在测试间是关闭的
        m_processor->closePort();
    }
}

QTEST_MAIN(TestExceptionHandling)
#include "test_exception_handling.moc"
