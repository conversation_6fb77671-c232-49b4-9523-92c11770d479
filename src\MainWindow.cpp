#include "MainWindow.h"
#include "CommandParser.h"
#include "FindWidget.h"
#include "HighPerformanceTerminal.h"
#include "Logger.h"
#include "SerialProcessor.h"
#include "SettingsDialog.h"
#include "VT100Parser.h"
#include "core/AppInfo.h"
#include "core/AppTypes.h"
#include "core/LoggingConstants.h"
#include "core/ReconnectConstants.h"
#include "core/SerialConstants.h"
#include "core/TerminalConstants.h"

#include <QAction>
#include <QActionGroup>
#include <QApplication>
#include <QCloseEvent>
#include <QComboBox>
#include <QCoreApplication>
#include <QDateTime>
#include <QDesktopServices>
#include <QDialog>
#include <QDir>
#include <QFileDialog>
#include <QFileInfo>
#include <QFontDialog>
#include <QHBoxLayout>
#include <QInputDialog>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QLabel>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QProcess>
#include <QPushButton>
#include <QSerialPortInfo>
#include <QSettings>
#include <QStatusBar>
#include <QStyle>
#include <QTimer>
#include <QToolBar>
#include <QVBoxLayout>

#include <QDebug>

#ifdef Q_OS_WIN
#include <windows.h>
#endif

// 匿名命名空间已被移除，其内容已作为类的静态常量移至 MainWindow.h

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent),
      m_terminal(nullptr),
      m_serialProcessor(nullptr),
      m_logger(nullptr),
      m_toolBar(nullptr),
      m_portComboBox(nullptr),
      m_baudRateComboBox(nullptr),
      m_connectionToggleAction(nullptr),
      m_refreshAction(nullptr),
      m_findAction(nullptr),
      m_toggleLoggingAction(nullptr),
      m_openLogFileAction(nullptr),
      m_openLogDirAction(nullptr),
      m_pinAction(nullptr),
      m_wordWrapAction(nullptr),
      m_displayModeActionGroup(nullptr),
      m_asciiModeAction(nullptr),
      m_hexModeAction(nullptr),
      m_mixedModeAction(nullptr),
      m_lightThemeAction(nullptr),
      m_darkThemeAction(nullptr),
      m_autoThemeAction(nullptr),
      m_quickCommandsMenu(nullptr),
      m_findWidget(nullptr),
      m_searchDebounceTimer(nullptr),
      m_trayIcon(nullptr),
      m_trayMenu(nullptr),
      m_trayShowAction(nullptr),
      m_trayExitAction(nullptr),
      m_themeManager(new ThemeManager(this)), // 初始化主题管理器
      m_statusLabel(nullptr),
      m_statisticsLabel(nullptr),
      m_statusMessageTimer(nullptr),
      m_customTitleWidget(nullptr),
      m_customTitleCheckBox(nullptr),
      m_customTitleLineEdit(nullptr),
      m_titleAnimation(nullptr),
      m_hideTimer(nullptr)
{
    setWindowIcon(QIcon(":/icons/icon.ico"));

    connect(m_themeManager, &ThemeManager::themeChanged, this, &MainWindow::onThemeChanged);

    loadApplicationSettings();
    setupUi();
    createActions();
    createToolBar();
    createMenus();
    updatePortList();
    applyApplicationSettings(); // 启动时应用加载的设置
    updateQuickCommandsMenu();  // 初始化快捷命令菜单
    createTrayMenu();
    updateTrayIconVisibility(); // 初始化托盘图标可见性

    // 应用初始主题
    onThemeChanged();

    const int kInstanceNum = m_instanceManager.getInstanceNumber();
    if (kInstanceNum > 0)
    {
        setWindowTitle(QString("SerialT [%1]").arg(kInstanceNum));
    }
    else
    {
        setWindowTitle("SerialT");
    }
}

MainWindow::~MainWindow()
{
    // 首先断开所有可能发送信号到this的对象，防止在析构过程中出现信号槽访问已销毁对象的问题

    // 断开SerialProcessor信号并关闭串口
    if (m_serialProcessor != nullptr)
    {
        disconnect(m_serialProcessor, nullptr, this, nullptr);
        if (m_serialProcessor->isOpen())
        {
            m_serialProcessor->closePort();
        }
    }

    // 断开托盘图标信号
    if (m_trayIcon != nullptr)
    {
        disconnect(m_trayIcon, nullptr, this, nullptr);
        m_trayIcon->hide(); // 隐藏托盘图标
    }

    // 停止并清理所有快捷命令定时器
    for (auto it = m_quickCommandTimers.begin(); it != m_quickCommandTimers.end(); ++it)
    {
        QTimer *timer = it.value();
        if (timer != nullptr)
        {
            timer->stop();
            timer->disconnect(); // 断开所有信号连接
            delete timer;
        }
    }
    m_quickCommandTimers.clear();

    // 停止搜索防抖定时器
    if (m_searchDebounceTimer != nullptr)
    {
        m_searchDebounceTimer->stop();
        m_searchDebounceTimer->disconnect();
    }

    // 断开终端相关信号
    if (m_terminal != nullptr)
    {
        disconnect(m_terminal, nullptr, this, nullptr);
    }

    // 断开查找窗口信号
    if (m_findWidget != nullptr)
    {
        disconnect(m_findWidget, nullptr, this, nullptr);
    }

    // 移除全局事件过滤器
    qApp->removeEventFilter(this);

    // 最后断开所有与this相关的信号连接
    disconnect(this, nullptr, nullptr, nullptr);

    saveApplicationSettings();
}

void MainWindow::setupUi()
{
    m_terminal = new HighPerformanceTerminal(this);
    setCentralWidget(m_terminal);

    // 设置最小窗口宽度，确保所有toolbar控件都能显示
    setMinimumWidth(kMinimumWidth); // 足够显示所有toolbar控件

    m_serialProcessor = new SerialProcessor(this);
    m_logger = new Logger(this);

    VT100Parser *parser = m_serialProcessor->parser();
    m_terminal->setParser(parser);
    connect(parser, &VT100Parser::parseFinished, m_terminal, &HighPerformanceTerminal::scheduleUpdate);
    connect(m_terminal, &HighPerformanceTerminal::dataReadyToSend, m_serialProcessor, &SerialProcessor::writeData);
    connect(m_serialProcessor, &SerialProcessor::dataForLogging, m_logger, &Logger::writeData);
    connect(m_serialProcessor, &SerialProcessor::rawDataReceived, m_terminal, &HighPerformanceTerminal::appendRawData);
    connect(m_serialProcessor, &SerialProcessor::connectionStatusChanged, this,
            &MainWindow::updateUiForConnectionState);
    connect(m_serialProcessor, &SerialProcessor::errorOccurred, this, &MainWindow::handleSerialError);
    connect(m_serialProcessor, &SerialProcessor::connectionLost, this, &MainWindow::handleConnectionLost);
    connect(m_serialProcessor, &SerialProcessor::connectionHealthChanged, this,
            &MainWindow::handleConnectionHealthChanged);
    connect(m_serialProcessor, &SerialProcessor::statisticsUpdated, this, &MainWindow::updateStatusBarStatistics);
    connect(m_serialProcessor, &SerialProcessor::connectionStateChanged, this,
            [this](ConnectionState state, int currentAttempt, int maxAttempts) {
                onConnectionStateChanged(state, currentAttempt, maxAttempts);
            });

    // 设置系统托盘图标
    m_trayIcon = new QSystemTrayIcon(this);
    m_trayIcon->setIcon(QIcon(":/icons/icon.ico"));
    m_trayIcon->setToolTip("SerialT");
    connect(m_trayIcon, &QSystemTrayIcon::activated, this, &MainWindow::onTrayIconActivated);
    m_trayIcon->setVisible(false); // 初始时隐藏

    // 初始化状态栏
    m_statusLabel = new QLabel("就绪", this);
    m_statisticsLabel = new QLabel("", this);
    m_statusMessageTimer = new QTimer(this);
    m_statusMessageTimer->setSingleShot(true);
    connect(m_statusMessageTimer, &QTimer::timeout, this, &MainWindow::restoreNormalStatusMessage);
    statusBar()->addWidget(m_statusLabel); // 连接状态

    // 设置自定义标题控件（紧跟在连接状态后面）
    setupCustomTitleWidget();
    statusBar()->addWidget(m_customTitleWidget); // 添加自定义标题控件

    // 添加弹性空间，将统计信息推到右侧
    auto *spacer = new QWidget();
    spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    statusBar()->addWidget(spacer, 1); // 拉伸因子为1，占用剩余空间

    statusBar()->addPermanentWidget(m_statisticsLabel); // 永久显示在右侧
    // --- 新的搜索逻辑 ---
    m_findWidget = new FindWidget(this);
    m_findWidget->setWindowFlags(m_findWidget->windowFlags() | Qt::Tool);
    m_findWidget->hide();

    m_searchDebounceTimer = new QTimer(this);
    m_searchDebounceTimer->setSingleShot(true);
    m_searchDebounceTimer->setInterval(kSearchDebounceTimeMs); // 200ms 防抖

    connect(m_findWidget, &FindWidget::findTriggered, this, &MainWindow::performSearch);
    connect(m_findWidget, &FindWidget::findNextTriggered, m_terminal, &HighPerformanceTerminal::findNext);
    connect(m_findWidget, &FindWidget::findPreviousTriggered, m_terminal, &HighPerformanceTerminal::findPrevious);
    connect(m_terminal, &HighPerformanceTerminal::searchResultsUpdated, m_findWidget, &FindWidget::updateResults);
    // 当FindWidget关闭时，取消findAction的勾选状态
    connect(m_findWidget, &FindWidget::widgetClosed, this, [this]() {
        m_findAction->setChecked(false);
        m_terminal->find("", HighPerformanceTerminal::FindFlag::NoFlags); // 清除高亮
        m_terminal->setFocus();
    });

    connect(m_terminal, &HighPerformanceTerminal::screenCleared, this, &MainWindow::onScreenCleared);
}

void MainWindow::createActions()
{
    // 创建连接切换按钮（合并连接/断开功能）
    m_connectionToggleAction = new QAction("连接", this);
    m_connectionToggleAction->setShortcut(QKeySequence("Alt+C")); // Connect/Disconnect toggle
    m_connectionToggleAction->setCheckable(true);                 // 设为可切换按钮
    connect(m_connectionToggleAction, &QAction::triggered, this, &MainWindow::toggleConnection);

    m_refreshAction = new QAction("刷新", this);
    m_refreshAction->setShortcut(QKeySequence("Alt+R")); // Refresh ports (Alt避免VT100冲突)
    connect(m_refreshAction, &QAction::triggered, this, &MainWindow::refreshPorts);

    m_toggleLoggingAction = new QAction("启用日志", this);
    m_toggleLoggingAction->setShortcut(QKeySequence("Ctrl+Shift+G")); // Toggle logging
    m_toggleLoggingAction->setCheckable(true);
    connect(m_toggleLoggingAction, &QAction::toggled, this, &MainWindow::toggleLogging);

    m_openLogFileAction = new QAction("打开日志", this);
    m_openLogFileAction->setShortcut(QKeySequence("Ctrl+Shift+O")); // Open log file
    connect(m_openLogFileAction, &QAction::triggered, this, &MainWindow::onOpenLogFileClicked);

    m_openLogDirAction = new QAction("打开目录", this);
    connect(m_openLogDirAction, &QAction::triggered, this, &MainWindow::onOpenLogDirectoryClicked);

    m_pinAction = new QAction(QIcon(":/icons/sticky.svg"), "置顶", this);
    m_pinAction->setShortcut(QKeySequence("Ctrl+Shift+T")); // Toggle pin on top
    m_pinAction->setCheckable(true);
    connect(m_pinAction, &QAction::toggled, this, &MainWindow::onPinActionToggled);

    m_findAction = new QAction(QIcon(":/icons/find.svg"), "查找", this);
    m_findAction->setShortcut(QKeySequence("Ctrl+Shift+F")); // Find
    m_findAction->setCheckable(true);
    connect(m_findAction, &QAction::toggled, this, [this](bool visible) {
        m_findWidget->setVisible(visible);
        if (visible)
        {
            updateFindWidgetPosition();
            m_findWidget->setFocusOnLineEdit();
        }
    });

    m_wordWrapAction = new QAction("自动换行", this);
    m_wordWrapAction->setShortcut(QKeySequence("Alt+W")); // Word wrap (Alt避免VT100冲突)
    m_wordWrapAction->setCheckable(true);
    connect(m_wordWrapAction, &QAction::toggled, this, &MainWindow::toggleWordWrap);

    // 创建显示模式Action组
    m_displayModeActionGroup = new QActionGroup(this);

    m_asciiModeAction = new QAction("ASCII文本模式", this);
    m_asciiModeAction->setShortcut(QKeySequence("Alt+1")); // ASCII mode (Alt避免VT100冲突)
    m_asciiModeAction->setCheckable(true);
    m_asciiModeAction->setChecked(true); // 默认选中ASCII模式
    m_asciiModeAction->setData(static_cast<int>(DisplayMode::ASCII));
    m_displayModeActionGroup->addAction(m_asciiModeAction);

    m_hexModeAction = new QAction("十六进制模式", this);
    m_hexModeAction->setShortcut(QKeySequence("Alt+2")); // Hex mode (Alt避免VT100冲突)
    m_hexModeAction->setCheckable(true);
    m_hexModeAction->setData(static_cast<int>(DisplayMode::HEX));
    m_displayModeActionGroup->addAction(m_hexModeAction);

    m_mixedModeAction = new QAction("混合模式 (ASCII + HEX)", this);
    m_mixedModeAction->setShortcut(QKeySequence("Alt+3")); // Mixed mode (Alt避免VT100冲突)
    m_mixedModeAction->setCheckable(true);
    m_mixedModeAction->setData(static_cast<int>(DisplayMode::MIXED));
    m_displayModeActionGroup->addAction(m_mixedModeAction);

    // 连接显示模式切换信号
    connect(m_displayModeActionGroup, &QActionGroup::triggered, this, &MainWindow::onDisplayModeChanged);

    // 创建主题切换Action
    m_lightThemeAction = new QAction("亮色主题", this);
    m_lightThemeAction->setShortcut(QKeySequence("Ctrl+Shift+L")); // Light theme
    m_lightThemeAction->setCheckable(true);
    connect(m_lightThemeAction, &QAction::triggered, [this]() {
        m_themeManager->setTheme(ThemeType::Light);
        m_themeManager->followSystemTheme(false);
        m_autoThemeAction->setChecked(false);
    });

    m_darkThemeAction = new QAction("暗色主题", this);
    m_darkThemeAction->setShortcut(QKeySequence("Ctrl+Shift+D")); // Dark theme
    m_darkThemeAction->setCheckable(true);
    connect(m_darkThemeAction, &QAction::triggered, [this]() {
        m_themeManager->setTheme(ThemeType::Dark);
        m_themeManager->followSystemTheme(false);
        m_autoThemeAction->setChecked(false);
    });

    m_autoThemeAction = new QAction("跟随系统", this);
    m_autoThemeAction->setShortcut(QKeySequence("Ctrl+Shift+A")); // Auto theme
    m_autoThemeAction->setCheckable(true);
    m_autoThemeAction->setChecked(true); // 默认跟随系统
    connect(m_autoThemeAction, &QAction::triggered, [this]() {
        m_themeManager->followSystemTheme(true);
    });

    // 分类管理快捷键：将可能与VT100冲突的快捷键分组
    // 注意：现在我们使用Alt组合键，基本不会与VT100冲突
    // 但保留这个架构以便将来扩展
    m_vt100ConflictActions.clear();
    m_safeActions.clear();

    // 安全的快捷键（Ctrl+Shift组合键和特殊键）
    m_safeActions << m_lightThemeAction << m_darkThemeAction << m_autoThemeAction;
    m_safeActions << m_toggleLoggingAction << m_openLogFileAction << m_findAction << m_pinAction;

    // 使用Alt组合键的快捷键（基本安全，但可以根据需要禁用）
    m_vt100ConflictActions << m_connectionToggleAction << m_refreshAction;
    m_vt100ConflictActions << m_wordWrapAction;
    m_vt100ConflictActions << m_asciiModeAction << m_hexModeAction << m_mixedModeAction;
}

void MainWindow::createToolBar()
{
    m_toolBar = addToolBar("串口");

    // 设置toolbar基本属性
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_toolBar->setIconSize(QSize(kIconSize, kIconSize)); // 设置较小的图标尺寸

    m_toolBar->addWidget(new QLabel("串口: "));
    m_portComboBox = new QComboBox(this);
    m_toolBar->addWidget(m_portComboBox);

    m_toolBar->addWidget(new QLabel("波特率: "));
    m_baudRateComboBox = new QComboBox(this);
    m_baudRateComboBox->addItems(QStringList(App::Serial::kBaudRateList.begin(), App::Serial::kBaudRateList.end()));
    m_toolBar->addWidget(m_baudRateComboBox);

    connect(m_portComboBox, &QComboBox::currentTextChanged, this, &MainWindow::onPortChanged);
    connect(m_baudRateComboBox, &QComboBox::currentTextChanged, this, &MainWindow::onBaudRateChanged);

    m_toolBar->addAction(m_connectionToggleAction);
    m_toolBar->addAction(m_refreshAction);

    // 添加一个可伸缩项，将日志相关的Action推到右侧
    auto *spacer = new QWidget();
    spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    m_toolBar->addWidget(spacer);

    m_toolBar->addAction(m_toggleLoggingAction);
    m_toolBar->addAction(m_openLogFileAction);
    m_toolBar->addAction(m_openLogDirAction);
    m_toolBar->addAction(m_pinAction);
    m_toolBar->addAction(m_findAction);
}

void MainWindow::updatePortList()
{
    m_portComboBox->clear();
    const auto kPorts = QSerialPortInfo::availablePorts();
    for (const auto &info : kPorts)
    {
        m_portComboBox->addItem(info.portName());
    }
    // 反映当前设置
    m_portComboBox->setCurrentText(m_appSettings.portName);
}

void MainWindow::refreshPorts()
{
    updatePortList();
}

void MainWindow::openSerialPort()
{
    if (m_appSettings.portName.isEmpty())
    {
        QMessageBox::warning(this, "警告", "没有配置串口。请在设置中指定。");
        return;
    }

    if (m_serialProcessor->openPort(m_appSettings.portName, m_appSettings.baudRate, m_appSettings.dataBits,
                                    m_appSettings.parity, m_appSettings.stopBits))
    {
        // 状态现在通过信号/槽更新
        m_statusLabel->setText(QString("已连接到 %1 : %2").arg(m_appSettings.portName).arg(m_appSettings.baudRate));

        // 新增：根据设置自动启用日志
        if (m_appSettings.autoEnableLogOnPortOpen && !m_logger->isLogging())
        {
            // 使用 QTimer::singleShot 确保在当前事件循环完成后执行，
            // 避免在 openSerialPort 调用堆栈中直接修改UI状态可能引发的潜在问题。
            // 这也使得UI更新更加平滑。
            QTimer::singleShot(0, this, [this]() {
                m_toggleLoggingAction->setChecked(true);
            });
        }
    }
    // 注意：错误处理现在通过 handleSerialError 信号槽统一处理，避免双重弹窗
}

void MainWindow::closeSerialPort()
{
    m_serialProcessor->closePort();
    // 状态现在通过信号/槽更新
    m_statusLabel->setText("已断开");
    m_statisticsLabel->clear(); // 清除统计信息
}

void MainWindow::toggleConnection()
{
    // 如果正在重连，则取消重连
    if (m_serialProcessor->isReconnecting())
    {
        m_serialProcessor->cancelReconnection();
        return;
    }

    if (m_serialProcessor->isOpen())
    {
        closeSerialPort();
    }
    else
    {
        openSerialPort();
    }
}

void MainWindow::onPortChanged(const QString &name)
{
    if (m_appSettings.portName != name)
    {
        m_appSettings.portName = name;
        // 如果串口已连接，可以考虑给出提示或自动重连
        if (m_serialProcessor->isOpen())
        {
            // 暂时只更新设置，用户需要手动重连
            m_statusLabel->setText("端口已更改，请重新连接");
        }
    }
}

void MainWindow::onBaudRateChanged(const QString &baudRate)
{
    bool ok = false;
    const int kBaud = baudRate.toInt(&ok);
    if (ok && m_appSettings.baudRate != kBaud)
    {
        m_appSettings.baudRate = kBaud;
        if (m_serialProcessor->isOpen())
        {
            // 暂时只更新设置，用户需要手动重连
            m_statusLabel->setText("波特率已更改，请重新连接");
        }
    }
}
void MainWindow::createMenus()
{
    // --- 文件菜单 ---
    QMenu *fileMenu = menuBar()->addMenu("文件(&F)");
    QAction *settingsAction = fileMenu->addAction("设置...(&S)");
    settingsAction->setShortcut(QKeySequence("Ctrl+,")); // Settings (common shortcut)
    connect(settingsAction, &QAction::triggered, this, &MainWindow::showSettingsDialog);
    fileMenu->addSeparator();
    QAction *exitAction = fileMenu->addAction("退出(&X)");
    exitAction->setShortcut(QKeySequence("Ctrl+Q")); // Quit
    connect(exitAction, &QAction::triggered, this, &MainWindow::onExitActionTriggered);

    // --- 编辑菜单 ---
    QMenu *editMenu = menuBar()->addMenu("编辑(&E)");
    QAction *copyAction = editMenu->addAction("复制(&C)");
    connect(copyAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::copy);
    QAction *pasteAction = editMenu->addAction("粘贴(&P)");
    connect(pasteAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::paste);
    QAction *selectAllAction = editMenu->addAction("全选(&A)");
    connect(selectAllAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::selectAll);
    editMenu->addSeparator();
    QAction *clearTerminalAction = editMenu->addAction("清空终端(&L)");
    connect(clearTerminalAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::clear);
    editMenu->addSeparator();
    editMenu->addAction(m_findAction);

    // --- 视图菜单 ---
    QMenu *viewMenu = menuBar()->addMenu("视图(&V)");
    QAction *clearViewAction = viewMenu->addAction("清空视图(&C)");
    clearViewAction->setShortcut(QKeySequence("Alt+L")); // Clear screen (Alt避免VT100冲突)
    connect(clearViewAction, &QAction::triggered, this, &MainWindow::clearTerminalView);
    viewMenu->addSeparator();
    viewMenu->addAction(m_wordWrapAction);
    viewMenu->addSeparator();

    // 添加显示模式子菜单
    QMenu *displayModeMenu = viewMenu->addMenu("数据显示模式(&D)");
    displayModeMenu->addAction(m_asciiModeAction);
    displayModeMenu->addAction(m_hexModeAction);
    displayModeMenu->addAction(m_mixedModeAction);
    viewMenu->addSeparator();

    // 添加主题子菜单
    QMenu *themeMenu = viewMenu->addMenu("主题(&T)");
    themeMenu->addAction(m_autoThemeAction);
    themeMenu->addSeparator();
    themeMenu->addAction(m_lightThemeAction);
    themeMenu->addAction(m_darkThemeAction);
    viewMenu->addSeparator();

    QAction *zoomInAction = viewMenu->addAction("放大(&I)");
    zoomInAction->setShortcut(QKeySequence("Ctrl+=")); // Zoom in
    connect(zoomInAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::zoomIn);
    QAction *zoomOutAction = viewMenu->addAction("缩小(&O)");
    zoomOutAction->setShortcut(QKeySequence("Ctrl+-")); // Zoom out
    connect(zoomOutAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::zoomOut);
    QAction *resetZoomAction = viewMenu->addAction("重置缩放(&Z)");
    resetZoomAction->setShortcut(QKeySequence("Ctrl+0")); // Reset zoom
    connect(resetZoomAction, &QAction::triggered, m_terminal, &HighPerformanceTerminal::zoomReset);

    // --- 快捷命令菜单 ---
    m_quickCommandsMenu = menuBar()->addMenu("快捷命令(&T)");
    QAction *editCommandsAction = m_quickCommandsMenu->addAction("编辑配置...(&E)");
    connect(editCommandsAction, &QAction::triggered, this, &MainWindow::showQuickCommandsSettings);
    m_quickCommandsMenu->addSeparator(); // 用于分隔快捷命令的分割线

    // --- 帮助菜单 ---
    QMenu *helpMenu = menuBar()->addMenu("帮助(&H)");
    QAction *aboutAction = helpMenu->addAction("关于(&A)");
    connect(aboutAction, &QAction::triggered, this, &MainWindow::showAboutDialog);
}

void MainWindow::clearTerminalView()
{
    if (m_terminal != nullptr)
    {
        m_terminal->clear();
    }
}

void MainWindow::toggleLogging(bool checked)
{
    // 因为这是一个专用的action，我们不需要检查sender()
    if (checked)
    {
        if (m_appSettings.logPath.isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先在设置中指定日志保存路径！");
            // 阻塞信号以防止重入此槽
            m_toggleLoggingAction->blockSignals(true);
            m_toggleLoggingAction->setChecked(false);
            m_toggleLoggingAction->blockSignals(false);
            return;
        }

        const QString kBaseName = formatLogFileName(m_appSettings.logFileNameFormat);
        const QDir kLogDir(m_appSettings.logPath);
        if (!kLogDir.exists())
        {
            kLogDir.mkpath(".");
        }
        const QString kFullFilePath = kLogDir.filePath(kBaseName);

        m_logger->startLogging(kFullFilePath, m_appSettings.logSplitEnabled, m_appSettings.logSplitSizeMB);
    }
    else
    {
        m_logger->stopLogging();
    }
}

void MainWindow::toggleWordWrap(bool checked)
{
    m_appSettings.wordWrapEnabled = checked;
    // 应用自动换行设置到终端
    m_terminal->setWordWrapEnabled(checked);
    saveApplicationSettings();
}

void MainWindow::onDisplayModeChanged()
{
    QAction *action = m_displayModeActionGroup->checkedAction();
    if (action != nullptr)
    {
        const DisplayMode kMode = static_cast<DisplayMode>(action->data().toInt());
        m_appSettings.displayMode = kMode;

        // 应用显示模式到串口处理器和终端
        m_serialProcessor->setDisplayMode(kMode);
        m_terminal->setDisplayMode(kMode);

        saveApplicationSettings();
    }
}

void MainWindow::onThemeChanged()
{
    if (m_themeManager == nullptr)
    {
        return;
    }

    // 应用主题样式表
    const QString kStyleSheet = m_themeManager->generateStyleSheet() + m_themeManager->generateButtonStyle()
                                + m_themeManager->generateComboBoxStyle() + m_themeManager->generateLineEditStyle()
                                + m_themeManager->generateMenuStyle() + m_themeManager->generateCompactToolBarStyle()
                                + m_themeManager->generateStatusBarStyle() + m_themeManager->generateDialogStyle()
                                + m_themeManager->generateScrollBarStyle();

    setStyleSheet(kStyleSheet);

    // 更新终端颜色（暂时注释，等待HighPerformanceTerminal支持）
    // const ThemeColors& colors = m_themeManager->colors();
    // if (m_terminal != nullptr) {
    //     m_terminal->setThemeColors(colors);
    // }

    // 更新主题菜单状态
    const ThemeType kCurrentTheme = m_themeManager->currentTheme();
    m_lightThemeAction->setChecked(kCurrentTheme == ThemeType::Light);
    m_darkThemeAction->setChecked(kCurrentTheme == ThemeType::Dark);

    // 检查是否跟随系统主题
    QSettings settings;
    settings.beginGroup("Theme");
    const bool kFollowSystemTheme = settings.value("followSystemTheme", true).toBool();
    settings.endGroup();
    m_autoThemeAction->setChecked(kFollowSystemTheme);
}

void MainWindow::showAboutDialog()
{
    QMessageBox aboutBox(this);
    aboutBox.setWindowTitle("About SerialT");
    aboutBox.setWindowIcon(QIcon(":/icons/icon.ico"));
    aboutBox.setIconPixmap(QIcon(":/icons/icon.ico").pixmap(kAboutIconSize, kAboutIconSize));
    aboutBox.setTextFormat(Qt::RichText);
    aboutBox.setText(QString("<h3>SerialT %1</h3>"
                             "<p>A VT100-compatible serial terminal.</p>")
                         .arg(QString::fromUtf8(App::Info::kAppVersion)));
    aboutBox.setInformativeText(
        "<p><b><a href='http://192.168.0.117:8090/pages/viewpage.action?pageId=104474682'>GWF</a> "
        "Grand Presentation</b><br>"
        "Supported by <b>LSDT</b><br>"
        "<small>Built with Qt 6</small></p>");

    // 应用主题样式到消息框，但保持紧凑的按钮样式
    if (m_themeManager != nullptr)
    {
        const QString kDialogStyle =
            m_themeManager->generateDialogStyle() + m_themeManager->generateCompactButtonStyle();
        aboutBox.setStyleSheet(kDialogStyle);
    }

    aboutBox.exec();
}

void MainWindow::loadApplicationSettings()
{
    QSettings settings{QLatin1String(App::Info::kOrgName), QLatin1String(App::Info::kAppName)};

    settings.beginGroup("General");
    m_appSettings.minimizeToTrayOnClose = settings.value("minimizeToTrayOnClose", true).toBool();
    m_appSettings.alwaysShowInTray = settings.value("alwaysShowInTray", true).toBool();
    m_appSettings.windowStaysOnTop = settings.value("windowStaysOnTop", false).toBool();
    m_appSettings.terminalBufferSize = settings.value("terminalBufferSize", App::Terminal::kDefaultBufferSize).toInt();
    m_appSettings.terminalScrollPolicy =
        settings.value("terminalScrollPolicy", App::Terminal::kDefaultScrollPolicy).toInt();
    m_appSettings.scrollOnInput =
        settings.value("scrollOnInput", App::Terminal::kDefaultScrollOnInput).toBool(); // 默认为 true
    m_appSettings.pauseOnResize =
        settings.value("pauseOnResize", App::Terminal::kDefaultPauseOnResize).toBool(); // 默认为 false
    m_appSettings.wordWrapEnabled =
        settings.value("wordWrapEnabled", App::Terminal::kDefaultWordWrapEnabled).toBool(); // 默认为 true
    m_appSettings.displayMode = static_cast<DisplayMode>(
        settings.value("displayMode", static_cast<int>(DisplayMode::ASCII)).toInt()); // 默认为 ASCII
    // m_appSettings.language = settings.value("language", "en_US").toString(); // 语言设置已移除
    settings.endGroup();

    settings.beginGroup("Serial");
    m_appSettings.portName = settings.value("portName", "").toString();
    m_appSettings.baudRate = settings.value("baudRate", App::Serial::kDefaultBaudRate).toInt();
    m_appSettings.dataBits = static_cast<QSerialPort::DataBits>(settings.value("dataBits", QSerialPort::Data8).toInt());
    m_appSettings.parity = static_cast<QSerialPort::Parity>(settings.value("parity", QSerialPort::NoParity).toInt());
    m_appSettings.stopBits =
        static_cast<QSerialPort::StopBits>(settings.value("stopBits", QSerialPort::OneStop).toInt());
    m_appSettings.sendNewLineMode = settings.value("newLineMode", App::Serial::kDefaultNewLineMode).toInt();
    m_appSettings.serialEchoEnabled = settings.value("echoEnabled", App::Serial::kDefaultSerialEchoEnabled).toBool();

    // 自动重连设置
    m_appSettings.autoReconnectEnabled =
        settings.value("autoReconnectEnabled", App::Reconnect::kDefaultAutoReconnectEnabled).toBool();
    m_appSettings.maxReconnectAttempts =
        settings.value("maxReconnectAttempts", App::Reconnect::kDefaultMaxReconnectAttempts).toInt();
    m_appSettings.reconnectIntervalMs =
        settings.value("reconnectIntervalMs", App::Reconnect::kDefaultReconnectIntervalMs).toInt();
    m_appSettings.reconnectIntervalMultiplier =
        settings.value("reconnectIntervalMultiplier", App::Reconnect::kDefaultReconnectIntervalMultiplier).toDouble();
    settings.endGroup();

    settings.beginGroup("Logging");
    m_appSettings.logPath = settings.value("path").toString();
    if (m_appSettings.logPath.isEmpty())
    {
        m_appSettings.logPath = QDir(QCoreApplication::applicationDirPath()).filePath("log");
    }
    m_appSettings.logFileNameFormat =
        settings.value("fileNameFormat", QLatin1String(App::Logging::kDefaultLogFileNameFormat)).toString();
    m_appSettings.logTimestampEnabled =
        settings.value("logTimestampEnabled", App::Logging::kDefaultLogTimestampEnabled).toBool();
    m_appSettings.logTimestampFormat =
        settings.value("logTimestampFormat", QLatin1String(App::Logging::kDefaultLogTimestampFormat)).toString();
    m_appSettings.logSplitEnabled = settings.value("splitEnabled", App::Logging::kDefaultLogSplitEnabled).toBool();
    m_appSettings.logSplitSizeMB = settings.value("splitSizeMB", App::Logging::kDefaultLogSplitSizeMB).toInt();
    m_appSettings.useDefaultLogViewer =
        settings.value("useDefaultLogViewer", App::Logging::kDefaultUseLogViewer).toBool();
    m_appSettings.externalLogViewerPath = settings.value("externalLogViewerPath", "").toString();
    m_appSettings.autoEnableLogOnPortOpen =
        settings.value("autoEnableLogOnPortOpen", App::Logging::kDefaultAutoEnableLogOnPortOpen)
            .toBool(); // 默认为 true
    settings.endGroup();

    settings.beginGroup("Terminal");
    const QString kFontString = settings.value("font").toString();
    if (!kFontString.isEmpty())
    {
        m_appSettings.terminalFont.fromString(kFontString);
    }
    else
    {
        m_appSettings.terminalFont =
            QFont(QLatin1String(App::Terminal::kDefaultFontFamily), App::Terminal::kDefaultFontSize); // 默认字体
    }
    settings.endGroup();

    settings.beginGroup("QuickCommands");
    const QString kJsonString = settings.value("json").toString();
    if (!kJsonString.isEmpty())
    {
        const QJsonDocument kDoc = QJsonDocument::fromJson(kJsonString.toUtf8());
        if (kDoc.isArray())
        {
            const QJsonArray kJsonArray = kDoc.array();
            m_appSettings.quickCommands.clear();
            for (const auto kValue : kJsonArray)
            {
                if (kValue.isObject())
                {
                    QJsonObject obj = kValue.toObject();
                    QuickCommand cmd;
                    cmd.uuid = obj["uuid"].toString();
                    cmd.name = obj["name"].toString();
                    cmd.command = obj["command"].toString();
                    cmd.enabled = obj["enabled"].toBool();
                    const QString kFormatStr = obj["format"].toString("ASCII");
                    cmd.format = (kFormatStr.toUpper() == "HEX") ? CommandFormat::HEX : CommandFormat::ASCII;
                    cmd.isCycle = obj["isCycle"].toBool();
                    cmd.cycleInterval = obj["cycleInterval"].toInt();
                    m_appSettings.quickCommands.append(cmd);
                }
            }
        }
    }
    settings.endGroup();

    settings.beginGroup("VT100");
    m_appSettings.vt100CommandConfig.enableSGR = settings.value("enableSGR", true).toBool();
    m_appSettings.vt100CommandConfig.enableErase = settings.value("enableErase", true).toBool();
    m_appSettings.vt100CommandConfig.enableCursorMovement = settings.value("enableCursorMovement", true).toBool();

    // ASCII控制字符设置
    m_appSettings.vt100CommandConfig.enableBackspace = settings.value("enableBackspace", true).toBool();
    m_appSettings.vt100CommandConfig.enableHorizontalTab = settings.value("enableHorizontalTab", true).toBool();
    m_appSettings.vt100CommandConfig.enableBell = settings.value("enableBell", true).toBool();
    m_appSettings.vt100CommandConfig.enableFormFeed = settings.value("enableFormFeed", false).toBool(); // 默认禁用
    m_appSettings.vt100CommandConfig.enableVerticalTab = settings.value("enableVerticalTab", true).toBool();
    settings.endGroup();
}

void MainWindow::saveApplicationSettings()
{
    QSettings settings{QLatin1String(App::Info::kOrgName), QLatin1String(App::Info::kAppName)};

    settings.beginGroup("General");
    settings.setValue("minimizeToTrayOnClose", m_appSettings.minimizeToTrayOnClose);
    settings.setValue("alwaysShowInTray", m_appSettings.alwaysShowInTray);
    settings.setValue("windowStaysOnTop", m_appSettings.windowStaysOnTop);
    settings.setValue("terminalBufferSize", m_appSettings.terminalBufferSize);
    settings.setValue("terminalScrollPolicy", m_appSettings.terminalScrollPolicy);
    settings.setValue("scrollOnInput", m_appSettings.scrollOnInput);
    settings.setValue("pauseOnResize", m_appSettings.pauseOnResize);
    settings.setValue("wordWrapEnabled", m_appSettings.wordWrapEnabled);
    settings.setValue("displayMode", static_cast<int>(m_appSettings.displayMode));
    // settings.setValue("language", m_appSettings.language); // 语言设置已移除
    settings.endGroup();

    settings.beginGroup("Serial");
    settings.setValue("portName", m_appSettings.portName);
    settings.setValue("baudRate", m_appSettings.baudRate);
    settings.setValue("dataBits", static_cast<int>(m_appSettings.dataBits));
    settings.setValue("parity", static_cast<int>(m_appSettings.parity));
    settings.setValue("stopBits", static_cast<int>(m_appSettings.stopBits));
    settings.setValue("newLineMode", m_appSettings.sendNewLineMode);
    settings.setValue("echoEnabled", m_appSettings.serialEchoEnabled);
    settings.setValue("autoReconnectEnabled", m_appSettings.autoReconnectEnabled);
    settings.setValue("maxReconnectAttempts", m_appSettings.maxReconnectAttempts);
    settings.setValue("reconnectIntervalMs", m_appSettings.reconnectIntervalMs);
    settings.setValue("reconnectIntervalMultiplier", m_appSettings.reconnectIntervalMultiplier);
    settings.endGroup();

    settings.beginGroup("Logging");
    settings.setValue("path", m_appSettings.logPath);
    settings.setValue("fileNameFormat", m_appSettings.logFileNameFormat);
    settings.setValue("logTimestampEnabled", m_appSettings.logTimestampEnabled);
    settings.setValue("logTimestampFormat", m_appSettings.logTimestampFormat);
    settings.setValue("splitEnabled", m_appSettings.logSplitEnabled);
    settings.setValue("splitSizeMB", m_appSettings.logSplitSizeMB);
    settings.setValue("useDefaultLogViewer", m_appSettings.useDefaultLogViewer);
    settings.setValue("externalLogViewerPath", m_appSettings.externalLogViewerPath);
    settings.setValue("autoEnableLogOnPortOpen", m_appSettings.autoEnableLogOnPortOpen);
    settings.endGroup();

    settings.beginGroup("Terminal");
    settings.setValue("font", m_appSettings.terminalFont.toString());
    settings.endGroup();

    settings.beginGroup("QuickCommands");
    QJsonArray jsonArray;
    for (const auto &cmd : std::as_const(m_appSettings.quickCommands))
    {
        QJsonObject obj;
        obj["uuid"] = cmd.uuid;
        obj["name"] = cmd.name;
        obj["command"] = cmd.command;
        obj["enabled"] = cmd.enabled;
        obj["format"] = (cmd.format == CommandFormat::HEX) ? "HEX" : "ASCII";
        obj["isCycle"] = cmd.isCycle;
        obj["cycleInterval"] = cmd.cycleInterval;
        jsonArray.append(obj);
    }
    const QJsonDocument kDoc(jsonArray);
    settings.setValue("json", QString(kDoc.toJson(QJsonDocument::Compact)));
    settings.endGroup();

    settings.beginGroup("VT100");
    settings.setValue("enableSGR", m_appSettings.vt100CommandConfig.enableSGR);
    settings.setValue("enableErase", m_appSettings.vt100CommandConfig.enableErase);
    settings.setValue("enableCursorMovement", m_appSettings.vt100CommandConfig.enableCursorMovement);

    // ASCII控制字符设置
    settings.setValue("enableBackspace", m_appSettings.vt100CommandConfig.enableBackspace);
    settings.setValue("enableHorizontalTab", m_appSettings.vt100CommandConfig.enableHorizontalTab);
    settings.setValue("enableBell", m_appSettings.vt100CommandConfig.enableBell);
    settings.setValue("enableFormFeed", m_appSettings.vt100CommandConfig.enableFormFeed);
    settings.setValue("enableVerticalTab", m_appSettings.vt100CommandConfig.enableVerticalTab);
    settings.endGroup();
}

void MainWindow::applyApplicationSettings()
{
    // 应用到终端
    m_terminal->setBaseFont(m_appSettings.terminalFont);
    m_terminal->setBufferSize(m_appSettings.terminalBufferSize);
    m_terminal->setScrollPolicy(static_cast<HighPerformanceTerminal::ScrollPolicy>(m_appSettings.terminalScrollPolicy));
    m_terminal->setNewLineMode(m_appSettings.sendNewLineMode);
    m_terminal->setScrollOnInput(m_appSettings.scrollOnInput);
    // 应用自动换行设置到终端
    m_terminal->setWordWrapEnabled(m_appSettings.wordWrapEnabled);
    m_pauseOnResizeEnabled = m_appSettings.pauseOnResize;

    // 应用到串口处理器
    m_serialProcessor->setEchoEnabled(m_appSettings.serialEchoEnabled);
    // newLineMode 现在由终端处理，但我们保留此设置以备将来使用
    m_serialProcessor->setNewLineMode(static_cast<NewLineMode>(m_appSettings.sendNewLineMode));

    // 应用自动重连设置
    m_serialProcessor->setAutoReconnectEnabled(m_appSettings.autoReconnectEnabled);
    m_serialProcessor->setMaxReconnectAttempts(m_appSettings.maxReconnectAttempts);
    m_serialProcessor->setReconnectInterval(m_appSettings.reconnectIntervalMs);
    m_serialProcessor->setReconnectIntervalMultiplier(m_appSettings.reconnectIntervalMultiplier);

    // 应用到日志记录器
    m_logger->updateSettings(m_appSettings.logTimestampEnabled, m_appSettings.logTimestampFormat);

    // 应用VT100命令设置
    if (m_serialProcessor != nullptr && m_serialProcessor->parser() != nullptr)
    {
        m_serialProcessor->parser()->setCommandConfig(m_appSettings.vt100CommandConfig);
    }

    // 更新UI元素以反映当前设置
    m_portComboBox->setCurrentText(m_appSettings.portName);
    m_baudRateComboBox->setCurrentText(QString::number(m_appSettings.baudRate));

    // 语言更改需要重启，如果更改了可以显示一个消息框。
    updateQuickCommandsMenu();
    updateTrayIconVisibility(); // 应用可见性更改

    // 应用窗口置顶状态
    m_pinAction->setChecked(m_appSettings.windowStaysOnTop);
    onPinActionToggled(m_appSettings.windowStaysOnTop);

    // 应用自动换行菜单状态
    m_wordWrapAction->setChecked(m_appSettings.wordWrapEnabled);

    // 应用显示模式设置
    switch (m_appSettings.displayMode)
    {
        case DisplayMode::ASCII:
            m_asciiModeAction->setChecked(true);
            break;
        case DisplayMode::HEX:
            m_hexModeAction->setChecked(true);
            break;
        case DisplayMode::MIXED:
            m_mixedModeAction->setChecked(true);
            break;
    }
    m_serialProcessor->setDisplayMode(m_appSettings.displayMode);
    m_terminal->setDisplayMode(m_appSettings.displayMode);
}

void MainWindow::showSettingsDialog()
{
    // 在打开对话框之前，将UI选择同步到设置中
    m_appSettings.portName = m_portComboBox->currentText();
    m_appSettings.baudRate = m_baudRateComboBox->currentText().toInt();

    // 在打开对话框前保存一份设置的副本
    const ::AppSettings kOriginalSettings = m_appSettings;

    SettingsDialog dialog(this);
    dialog.setSettings(m_appSettings);
    dialog.setSerialSettingsEnabled(!m_serialProcessor->isOpen());

    // 应用主题样式到设置对话框
    if (m_themeManager != nullptr)
    {
        const QString kDialogStyle = m_themeManager->generateDialogStyle() + m_themeManager->generateButtonStyle()
                                     + m_themeManager->generateComboBoxStyle() + m_themeManager->generateLineEditStyle()
                                     + m_themeManager->generateScrollBarStyle();
        dialog.setStyleSheet(kDialogStyle);
    }

    // 使用连接来处理“应用”按钮
    connect(&dialog, &SettingsDialog::settingsApplied, this, [&]() {
        m_appSettings = dialog.getSettings();
        applyApplicationSettings();
        // 注意：我们直到点击“确定”或应用关闭时才保存。
        // 或者我们可以在应用时保存，这也是一种常见的模式。我们选择保存。
        saveApplicationSettings();
    });

    if (dialog.exec() == QDialog::Accepted)
    {
        // “确定”按钮被点击，设置已经通过信号应用
        // 现在我们确保它们被保存。
        m_appSettings = dialog.getSettings();
        applyApplicationSettings();
        saveApplicationSettings();
    }
    else
    {
        // “取消”被点击，恢复到对话框打开前的状态
        // 而不是从磁盘重新加载，那会丢失未保存的UI更改。
        m_appSettings = kOriginalSettings;
        applyApplicationSettings();
    }
}

void MainWindow::showQuickCommandsSettings()
{
    SettingsDialog dialog(this);
    dialog.setSettings(m_appSettings);
    dialog.setSerialSettingsEnabled(!m_serialProcessor->isOpen());
    dialog.switchToQuickCommandsTab(); // 切换到正确的标签页

    // 应用主题样式到设置对话框
    if (m_themeManager != nullptr)
    {
        const QString kDialogStyle = m_themeManager->generateDialogStyle() + m_themeManager->generateButtonStyle()
                                     + m_themeManager->generateComboBoxStyle() + m_themeManager->generateLineEditStyle()
                                     + m_themeManager->generateScrollBarStyle();
        dialog.setStyleSheet(kDialogStyle);
    }
    // 使用连接来处理“应用”按钮
    connect(&dialog, &SettingsDialog::settingsApplied, this, [&]() {
        m_appSettings = dialog.getSettings();
        applyApplicationSettings();
        saveApplicationSettings();
    });

    if (dialog.exec() == QDialog::Accepted)
    {
        m_appSettings = dialog.getSettings();
        applyApplicationSettings();
        saveApplicationSettings();
    }
    else
    {
        // 在取消时，我们不需要还原，因为主设置对象在应用/确定之前没有被更改。
    }
}

void MainWindow::updateQuickCommandsMenu()
{
    // 清除之前的快捷命令动作，但不是整个菜单
    for (QAction *action : m_quickCommandsMenu->actions())
    {
        // 使用属性来识别快捷命令动作
        if (action->property("isQuickCommand").toBool())
        {
            m_quickCommandsMenu->removeAction(action);
            action->deleteLater();
        }
    }
    // 同时清除与旧动作关联的任何计时器
    qDeleteAll(m_quickCommandTimers);
    m_quickCommandTimers.clear();

    if (m_appSettings.quickCommands.isEmpty())
    {
        return;
    }

    for (const auto &command : std::as_const(m_appSettings.quickCommands))
    {
        if (command.enabled)
        {
            auto *action = new QAction(command.name, this);
            action->setProperty("isQuickCommand", true); // 标记为快捷命令
            action->setCheckable(true);
            action->setData(QVariant::fromValue(command));
            connect(action, &QAction::triggered, this, &MainWindow::onQuickCommandTriggered);
            m_quickCommandsMenu->addAction(action);
        }
    }
}

void MainWindow::onQuickCommandTriggered(bool checked)
{
    auto *action = qobject_cast<QAction *>(sender());
    if (action == nullptr)
    {
        return;
    }

    const QVariant kData = action->data();
    if (!kData.canConvert<QuickCommand>())
    {
        return;
    }
    auto cmd = kData.value<QuickCommand>();

    if (!cmd.isCycle)
    {
        // 对于非循环命令，立即发送并取消选中
        const QByteArray kDataToSend = CommandParser::parse(cmd);
        m_serialProcessor->writeDataRaw(kDataToSend);
        action->setChecked(false);
    }
    else
    {
        // 对于循环命令，管理计时器
        if (checked)
        {
            // 启动计时器
            if (m_quickCommandTimers.contains(action))
            {
                // 不应该发生，但作为安全措施
                m_quickCommandTimers.value(action)->stop();
                delete m_quickCommandTimers.value(action);
            }

            auto *timer = new QTimer(this);
            timer->setInterval(cmd.cycleInterval);
            connect(timer, &QTimer::timeout, this, &MainWindow::onQuickCommandTimerTimeout);

            m_quickCommandTimers[action] = timer;

            // 立即发送第一条命令
            const QByteArray kDataToSend = CommandParser::parse(cmd);
            m_serialProcessor->writeDataRaw(kDataToSend);
            timer->start();
        }
        else
        {
            // 停止计时器
            if (m_quickCommandTimers.contains(action))
            {
                QTimer *timer = m_quickCommandTimers.value(action);
                timer->stop();
                delete timer;
                m_quickCommandTimers.remove(action);
            }
        }
    }
}

void MainWindow::onQuickCommandTimerTimeout()
{
    auto *timer = qobject_cast<QTimer *>(sender());
    if (timer == nullptr)
    {
        return;
    }

    // 查找与此计时器关联的动作
    QAction *action = m_quickCommandTimers.key(timer, nullptr);
    if (action == nullptr)
    {
        return;
    }

    auto cmd = action->data().value<QuickCommand>();
    const QByteArray kDataToSend = CommandParser::parse(cmd);
    m_serialProcessor->writeDataRaw(kDataToSend);
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 如果我们是通过一个 action 退出，确保串口关闭后再退出
    if (m_isQuittingFromAction)
    {
        // 确保串口连接正确关闭
        if (m_serialProcessor != nullptr && m_serialProcessor->isOpen())
        {
            m_serialProcessor->closePort();
        }
        event->accept();
        return;
    }

    // 否则，处理最小化到托盘的逻辑。
    if (m_appSettings.minimizeToTrayOnClose)
    {
        if (!m_trayMessageShown)
        {
            m_trayIcon->showMessage("提示", "程序已最小化到系统托盘", QSystemTrayIcon::Information,
                                    kTrayIconMessageDurationMs);
            m_trayMessageShown = true;
        }
        hide(); // hide() 会触发 hideEvent，进而调用 updateTrayIconVisibility()
        event->ignore();
    }
    else
    {
        event->accept(); // 正常关闭，将导致应用程序退出。
    }
}

void MainWindow::updateUiForConnectionState(bool connected)
{
    // 更新连接切换按钮的状态和文本
    m_connectionToggleAction->setChecked(connected);
    m_connectionToggleAction->setText(connected ? "断开" : "连接");
    m_connectionToggleAction->setToolTip(connected ? "断开串口连接 (Alt+C)" : "连接到串口 (Alt+C)");

    // 为连接按钮设置动态属性以应用不同样式
    QWidget *connectionButton = m_toolBar->widgetForAction(m_connectionToggleAction);
    if (connectionButton != nullptr)
    {
        connectionButton->setProperty("connectionState", connected ? "connected" : "disconnected");
        connectionButton->style()->unpolish(connectionButton);
        connectionButton->style()->polish(connectionButton);
    }

    m_refreshAction->setEnabled(!connected);
    m_portComboBox->setEnabled(!connected);
    m_baudRateComboBox->setEnabled(!connected);

    // 更新快捷键状态
    updateShortcutsForConnectionState(connected);

    // 使用新的标题更新方法
    updateWindowTitleWithCustomTitle();
}

void MainWindow::handleSerialError(const QString &rawError)
{
    // 如果正在自动重连过程中，不显示错误对话框
    if (m_serialProcessor->isReconnecting())
    {
        return;
    }

    // 步骤 1: 无条件重置UI状态
    updateUiForConnectionState(false);

    // 步骤 2: 在状态栏显示格式化后的错误信息
    m_statusLabel->setText("串口错误: " + rawError);

    // 步骤 3: 对所有连接失败的错误，统一弹出关键错误对话框
    QMessageBox::critical(this, "连接失败", QString("无法打开或维持串口连接。\n\n原因: %1").arg(rawError));
}

void MainWindow::handleConnectionLost()
{
    // 连接丢失时的处理
    m_statusLabel->setText("串口连接丢失");

    // 显示通知（使用critical级别，因为这是严重的连接问题）
    QMessageBox::critical(this, "连接丢失", "串口设备意外断开连接。\n请检查设备连接后重新连接。");

    // 更新UI状态（连接状态已通过connectionStatusChanged信号更新）
}

void MainWindow::handleConnectionHealthChanged(bool healthy)
{
    if (!healthy)
    {
        // 连接变得不健康时，在状态栏显示警告
        m_statusLabel->setText("串口连接不稳定，正在监控...");

        // 可以考虑在这里添加重连逻辑或其他恢复措施
        // 但目前只是提醒用户注意连接状态
    }
    else
    {
        // 连接恢复健康
        if (m_serialProcessor->isOpen())
        {
            m_statusLabel->setText("串口连接已恢复正常");
        }
    }
}

void MainWindow::onConnectionStateChanged(ConnectionState state, int currentAttempt, int maxAttempts)
{
    switch (state)
    {
        case ConnectionState::Connected:
            if (currentAttempt > 0)
            {
                // 重连成功，显示临时成功消息
                const QString kNormalMessage =
                    QString("已连接到 %1 : %2").arg(m_appSettings.portName).arg(m_appSettings.baudRate);
                m_normalStatusMessage = kNormalMessage;
                showTemporaryStatusMessage(QString("✔ 已成功重新连接至 %1").arg(m_appSettings.portName),
                                           kStatusMessageDurationMs);
            }
            else
            {
                // 正常连接
                m_statusLabel->setText(
                    QString("已连接到 %1 : %2").arg(m_appSettings.portName).arg(m_appSettings.baudRate));
            }
            // 连接状态下，按钮显示为"断开"
            m_connectionToggleAction->setText("断开");
            m_connectionToggleAction->setChecked(true);
            break;

        case ConnectionState::Disconnected:
            m_statusLabel->setText("已断开");
            m_statisticsLabel->clear();
            // 断开状态下，按钮显示为"连接"
            m_connectionToggleAction->setText("连接");
            m_connectionToggleAction->setChecked(false);
            break;

        case ConnectionState::Reconnecting:
            if (currentAttempt == 0)
            {
                m_statusLabel->setText("连接已断开，正在准备重连...");
            }
            else
            {
                m_statusLabel->setText(
                    QString("连接已断开，正在尝试第 %1 / %2 次重连...").arg(currentAttempt).arg(maxAttempts));
            }
            // 在重连过程中，连接按钮显示为"取消重连"
            m_connectionToggleAction->setText("取消重连");
            m_connectionToggleAction->setChecked(false);
            break;

        case ConnectionState::Failed:
            m_statusLabel->setText("❌ 自动重连失败");
            // 失败状态下，按钮显示为"连接"
            m_connectionToggleAction->setText("连接");
            m_connectionToggleAction->setChecked(false);
            // 显示最终失败对话框
            {
                QMessageBox msgBox(this);
                msgBox.setWindowTitle("自动重连失败");
                msgBox.setIcon(QMessageBox::Warning);
                msgBox.setText(
                    QString("经过 %1 次尝试后，无法重新连接到串口 %2。").arg(maxAttempts).arg(m_appSettings.portName));
                msgBox.setInformativeText("可能的原因：\n"
                                          "• 设备已断开连接\n"
                                          "• 串口被其他程序占用\n"
                                          "• 设备驱动程序问题\n\n"
                                          "请检查设备连接并手动重新连接。");
                msgBox.setStandardButtons(QMessageBox::Ok);
                msgBox.setDefaultButton(QMessageBox::Ok);

                // 应用主题样式
                if (m_themeManager != nullptr)
                {
                    const QString kDialogStyle =
                        m_themeManager->generateDialogStyle() + m_themeManager->generateCompactButtonStyle();
                    msgBox.setStyleSheet(kDialogStyle);
                }

                // 调整对话框大小以避免几何警告
                msgBox.adjustSize();

                msgBox.exec();
            }
            break;
    }
}

void MainWindow::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    updateTrayIconVisibility();
}

void MainWindow::hideEvent(QHideEvent *event)
{
    QWidget::hideEvent(event);
    updateTrayIconVisibility();
}

void MainWindow::updateTrayIconVisibility()
{
    // 如果“总是显示”被选中，或者窗口是隐藏的，那么图标就可见。
    m_trayIcon->setVisible(m_appSettings.alwaysShowInTray || isHidden());
}

void MainWindow::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason)
    {
        case QSystemTrayIcon::Trigger:     // 单击
        case QSystemTrayIcon::DoubleClick: // 双击
            showNormal();
            activateWindow();
            break;
        case QSystemTrayIcon::Context: // 右键单击，菜单会自动处理
        case QSystemTrayIcon::MiddleClick:
        case QSystemTrayIcon::Unknown:
            break;
    }
}

void MainWindow::onExitActionTriggered()
{
    m_isQuittingFromAction = true; // 在退出前设置标志
    qApp->quit();
}

void MainWindow::createTrayMenu()
{
    m_trayMenu = new QMenu(this);

    // 创建 "显示" 动作
    m_trayShowAction = new QAction("显示", this);
    connect(m_trayShowAction, &QAction::triggered, this, &QWidget::showNormal);
    m_trayMenu->addAction(m_trayShowAction);

    // 添加分隔符
    m_trayMenu->addSeparator();

    // 创建 "退出" 动作
    m_trayExitAction = new QAction("退出", this);
    // 连接到我们之前为 "文件" -> "退出" 创建的槽
    connect(m_trayExitAction, &QAction::triggered, this, &MainWindow::onExitActionTriggered);
    m_trayMenu->addAction(m_trayExitAction);

    // 将菜单设置给托盘图标
    m_trayIcon->setContextMenu(m_trayMenu);

    // 设置QSS以调整样式
    m_trayMenu->setStyleSheet("QMenu::item { padding: 3px 25px 3px 10px; }" // 减小左边距
                              "QMenu::icon { width: 0px; }");
}

void MainWindow::onOpenLogFileClicked()
{
    const QString kLogFilePath = m_logger->getCurrentLogFilePath();
    if (kLogFilePath.isEmpty() || !QFile::exists(kLogFilePath))
    {
        statusBar()->showMessage("当前没有可用的日志文件", kErrorMessageDurationMs);
        return;
    }

    if (m_appSettings.useDefaultLogViewer)
    {
        QDesktopServices::openUrl(QUrl::fromLocalFile(kLogFilePath));
    }
    else
    {
        if (m_appSettings.externalLogViewerPath.isEmpty() || !QFile::exists(m_appSettings.externalLogViewerPath))
        {
            statusBar()->showMessage("未设置有效的外部日志查看器", kErrorMessageDurationMs);
            QDesktopServices::openUrl(QUrl::fromLocalFile(kLogFilePath)); // 回退方案
            return;
        }
        QProcess::startDetached(m_appSettings.externalLogViewerPath, QStringList() << kLogFilePath);
    }
}

void MainWindow::onOpenLogDirectoryClicked()
{
    const QString kLogFilePath = m_logger->getCurrentLogFilePath();

    // 如果没有当前日志文件，则只打开日志目录
    if (kLogFilePath.isEmpty())
    {
        QDesktopServices::openUrl(QUrl::fromLocalFile(m_appSettings.logPath));
        return;
    }

    // 针对 Windows 平台，使用 explorer.exe /select 来选中文件
#if defined(Q_OS_WIN)
    const QString kExplorer = "explorer";
    QStringList params;
    params << "/select," << QDir::toNativeSeparators(kLogFilePath);
    QProcess::startDetached(kExplorer, params);
#else
    // 对于其他平台，保持原有行为：打开文件所在的目录
    QFileInfo fileInfo(kLogFilePath);
    QDesktopServices::openUrl(QUrl::fromLocalFile(fileInfo.absolutePath()));
#endif
}

void MainWindow::onPinActionToggled(bool checked)
{
    // 获取当前窗口的标志
    Qt::WindowFlags flags = windowFlags();
    if (checked)
    {
        // 添加置顶标志
        flags |= Qt::WindowStaysOnTopHint;
    }
    else
    {
        // 移除置顶标志
        flags &= ~Qt::WindowStaysOnTopHint;
    }
    // 重新设置窗口标志以应用更改
    setWindowFlags(flags);
    show(); // 必须调用 show() 来使窗口标志的更改生效

    // 同步FindWidget的置顶状态
    if (m_findWidget != nullptr)
    {
        // setWindowFlags()会隐藏窗口，所以我们先保存它的可见状态
        const bool kWasVisible = m_findWidget->isVisible();

        Qt::WindowFlags findFlags = m_findWidget->windowFlags();
        if (checked)
        {
            findFlags |= Qt::WindowStaysOnTopHint;
        }
        else
        {
            findFlags &= ~Qt::WindowStaysOnTopHint;
        }
        m_findWidget->setWindowFlags(findFlags);

        // 如果它之前是可见的，现在就重新显示它
        if (kWasVisible)
        {
            m_findWidget->show();
        }
    }

    // 保存设置
    if (m_appSettings.windowStaysOnTop != checked)
    {
        m_appSettings.windowStaysOnTop = checked;
        saveApplicationSettings();
    }
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    updateFindWidgetPosition();
}

void MainWindow::performSearch(const QString &term, HighPerformanceTerminal::FindFlags flags)
{
    // 每次触发搜索时，都先停止当前的计时器，然后重新开始
    // 这是实现防抖的关键
    m_searchDebounceTimer->stop();
    // 清除旧的连接，以防lambda中捕获的旧参数被意外调用
    disconnect(m_searchDebounceTimer, &QTimer::timeout, nullptr, nullptr);
    // 连接新的、捕获了当前参数的lambda
    connect(m_searchDebounceTimer, &QTimer::timeout, this, [this, term, flags]() {
        m_terminal->find(term, flags);
    });
    m_searchDebounceTimer->start();
}

auto MainWindow::nativeEvent(const QByteArray &eventType, void *message, qintptr *result) -> bool
{
    if (!m_pauseOnResizeEnabled)
    {
        return QMainWindow::nativeEvent(eventType, message, result);
    }

#ifdef Q_OS_WIN
    MSG *msg = static_cast<MSG *>(message);
    if (msg->message == WM_ENTERSIZEMOVE)
    {
        if (m_terminal != nullptr)
        {
            m_terminal->suspendRendering(true);
        }
    }
    else if (msg->message == WM_EXITSIZEMOVE)
    {
        if (m_terminal != nullptr)
        {
            m_terminal->suspendRendering(false);
        }
    }
#endif

    return QMainWindow::nativeEvent(eventType, message, result);
}

void MainWindow::updateStatusBarStatistics()
{
    if ((m_serialProcessor == nullptr) || !m_serialProcessor->isOpen())
    {
        m_statisticsLabel->clear();
        return;
    }

    // 获取统计数据
    const qint64 kBytesReceived = m_serialProcessor->getBytesReceived();
    const qint64 kBytesSent = m_serialProcessor->getBytesSent();
    const double kReceiveRate = m_serialProcessor->getReceiveRate();
    const double kSendRate = m_serialProcessor->getSendRate();
    const qint64 kConnectionDuration = m_serialProcessor->getConnectionDuration();

    // 格式化显示文本
    QString statisticsText;

    // 连接时长
    // 定义时间常量
    constexpr int kSecondsInMinute = 60;
    constexpr int kSecondsInHour = 3600;

    if (kConnectionDuration > 0)
    {
        const int kHours = static_cast<int>(kConnectionDuration / kSecondsInHour);
        const int kMinutes = static_cast<int>((kConnectionDuration % kSecondsInHour) / kSecondsInMinute);
        const int kSeconds = static_cast<int>(kConnectionDuration % kSecondsInMinute);

        if (kHours > 0)
        {
            statisticsText += QString("连接时长: %1:%2:%3 | ")
                                  .arg(kHours, 2, kTimeValueBase, QChar('0'))
                                  .arg(kMinutes, 2, kTimeValueBase, QChar('0'))
                                  .arg(kSeconds, 2, kTimeValueBase, QChar('0'));
        }
        else
        {
            statisticsText += QString("连接时长: %1:%2 | ")
                                  .arg(kMinutes, 2, kTimeValueBase, QChar('0'))
                                  .arg(kSeconds, 2, kTimeValueBase, QChar('0'));
        }
    }

    // 数据统计
    statisticsText += QString("接收: %1 字节").arg(formatBytes(kBytesReceived));
    statisticsText += QString(" | 发送: %1 字节").arg(formatBytes(kBytesSent));

    // 传输速率（只有在有数据传输时才显示）
    constexpr double kRateDisplayThreshold = 0.1;
    if (kReceiveRate > kRateDisplayThreshold || kSendRate > kRateDisplayThreshold)
    {
        statisticsText += QString(" | 速率: ↓%1/s ↑%2/s")
                              .arg(formatBytes(static_cast<qint64>(kReceiveRate)))
                              .arg(formatBytes(static_cast<qint64>(kSendRate)));
    }

    m_statisticsLabel->setText(statisticsText);
}

auto MainWindow::formatBytes(qint64 bytes) -> QString
{
    constexpr qint64 kBytesInKilobyte = 1024LL;
    constexpr qint64 kBytesInMegabyte = kBytesInKilobyte * 1024LL;
    constexpr qint64 kBytesInGigabyte = kBytesInMegabyte * 1024LL;

    if (bytes < kBytesInKilobyte)
    {
        return QString("%1 B").arg(bytes);
    }
    if (bytes < kBytesInMegabyte)
    {
        return QString("%1 KB").arg(static_cast<double>(bytes) / static_cast<double>(kBytesInKilobyte), 0, 'f', 1);
    }
    if (bytes < kBytesInGigabyte)
    {
        return QString("%1 MB").arg(static_cast<double>(bytes) / static_cast<double>(kBytesInMegabyte), 0, 'f', 1);
    }
    return QString("%1 GB").arg(static_cast<double>(bytes) / static_cast<double>(kBytesInGigabyte), 0, 'f', 1);
}

void MainWindow::showTemporaryStatusMessage(const QString &message, int durationMs)
{
    // 保存当前正常状态消息
    if (m_statusMessageTimer->isActive())
    {
        m_statusMessageTimer->stop();
    }
    else
    {
        m_normalStatusMessage = m_statusLabel->text();
    }

    // 显示临时消息
    m_statusLabel->setText(message);

    // 启动定时器以恢复正常消息
    m_statusMessageTimer->start(durationMs);
}

void MainWindow::restoreNormalStatusMessage()
{
    if (!m_normalStatusMessage.isEmpty())
    {
        m_statusLabel->setText(m_normalStatusMessage);
        m_normalStatusMessage.clear();
    }
}

void MainWindow::updateShortcutsForConnectionState(bool connected)
{
    // 当串口连接时，可以选择性地禁用某些快捷键以避免VT100冲突
    // 目前使用Alt组合键，冲突风险很低，所以保持启用
    // 但保留这个机制以便将来需要时使用

    Q_UNUSED(connected) // 暂时不使用，避免编译警告

    // 示例：如果将来需要在连接时禁用某些快捷键
    // enableGlobalShortcuts(!connected);
}

void MainWindow::enableGlobalShortcuts(bool enabled)
{
    // 启用或禁用可能与VT100冲突的快捷键
    for (QAction *action : m_vt100ConflictActions)
    {
        if (action != nullptr)
        {
            if (enabled)
            {
                // 恢复原始快捷键
                if (action == m_connectionToggleAction)
                {
                    action->setShortcut(QKeySequence("Alt+C"));
                }
                else if (action == m_refreshAction)
                {
                    action->setShortcut(QKeySequence("Alt+R"));
                }
                else if (action == m_findAction)
                {
                    action->setShortcut(QKeySequence("Ctrl+Shift+F"));
                }
                else if (action == m_wordWrapAction)
                {
                    action->setShortcut(QKeySequence("Alt+W"));
                }
                else if (action == m_pinAction)
                {
                    action->setShortcut(QKeySequence("Ctrl+Shift+T"));
                }
                else if (action == m_asciiModeAction)
                {
                    action->setShortcut(QKeySequence("Alt+1"));
                }
                else if (action == m_hexModeAction)
                {
                    action->setShortcut(QKeySequence("Alt+2"));
                }
                else if (action == m_mixedModeAction)
                {
                    action->setShortcut(QKeySequence("Alt+3"));
                }
            }
            else
            {
                // 清除快捷键
                action->setShortcut(QKeySequence());
            }
        }
    }

    // 安全的快捷键始终保持启用
    // m_safeActions 中的快捷键不受影响
}

auto MainWindow::formatLogFileName(const QString &format) const -> QString
{
    QString result = format;

    // 只替换 %P 占位符
    if (m_serialProcessor->isOpen())
    {
        result.replace("%P", m_serialProcessor->portName());
    }
    else
    {
        result.replace("%P", "NoPort");
    }

    // 处理日期和时间 (strftime 会处理 %Y, %m, %d 等)
    const time_t kNow = time(nullptr);
    tm timeinfo{}; // 定义一个结构体来存储时间结果

#ifdef _MSC_VER
    // 在 MSVC (Windows) 上，使用 localtime_s
    localtime_s(&timeinfo, &kNow);
#else
    // 在其他平台 (GCC, Clang) 上，使用 localtime_r
    localtime_r(&kNow, &timeinfo);
#endif

    std::array<char, kTimeBufferSize> timeBuf{};
    // 将安全获取到的时间信息传递给 strftime
    strftime(timeBuf.data(), timeBuf.size(), result.toLocal8Bit().constData(), &timeinfo);

    return QString::fromLocal8Bit(timeBuf.data());
}

void MainWindow::onScreenCleared()
{
    if (m_findWidget != nullptr)
    {
        // 当终端清屏时，重置搜索结果计数
        m_findWidget->updateResults(0, -1);
    }
}

void MainWindow::updateFindWidgetPosition()
{
    if (m_findWidget != nullptr && m_findWidget->isVisible())
    {
        // 保持在右上角
        m_findWidget->adjustSize(); // 确保尺寸更新
        const QPoint kGlobalPos = m_terminal->mapToGlobal(m_terminal->rect().topRight());
        m_findWidget->move(kGlobalPos.x() - m_findWidget->width() - kFindWidgetOffset,
                           kGlobalPos.y() + kFindWidgetOffset);
    }
}

void MainWindow::setupCustomTitleWidget()
{
    // 创建容器控件
    m_customTitleWidget = new QWidget(this);
    auto *layout = new QHBoxLayout(m_customTitleWidget);
    layout->setContentsMargins(kCustomTitleLayoutMargin, 0, kCustomTitleLayoutMargin, 0);
    layout->setSpacing(kCustomTitleLayoutSpacing);

    // 创建复选框（无文字）
    m_customTitleCheckBox = new QCheckBox(this);
    m_customTitleCheckBox->setToolTip("启用自定义窗口标题\n鼠标悬浮可编辑标题内容");
    layout->addWidget(m_customTitleCheckBox);

    // 创建输入框（初始隐藏）
    m_customTitleLineEdit = new QLineEdit(this);
    m_customTitleLineEdit->setPlaceholderText("输入自定义标题...");
    m_customTitleLineEdit->setMaximumWidth(0); // 初始宽度为0（隐藏）
    m_customTitleLineEdit->setToolTip("输入自定义标题，实时更新窗口标题");
    layout->addWidget(m_customTitleLineEdit);

    // 创建动画
    m_titleAnimation = new QPropertyAnimation(m_customTitleLineEdit, "maximumWidth", this);
    m_titleAnimation->setDuration(kCustomTitleAnimDurationMs);
    m_titleAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // 创建延迟隐藏定时器
    m_hideTimer = new QTimer(this);
    m_hideTimer->setSingleShot(true);
    m_hideTimer->setInterval(kHideTimerDelayMs); // 避免鼠标快速移动时的闪烁
    connect(m_hideTimer, &QTimer::timeout, [this]() {
        // 检查鼠标是否真的离开了控件区域
        if (!m_customTitleWidget->underMouse() && !m_customTitleLineEdit->hasFocus())
        {
            hideCustomTitleInput();
        }
    });

    // 连接信号
    connect(m_customTitleCheckBox, &QCheckBox::toggled, this, &MainWindow::onCustomTitleCheckBoxToggled);
    connect(m_customTitleLineEdit, &QLineEdit::textChanged, this, &MainWindow::onCustomTitleTextChanged);

    // 处理输入框失去焦点时隐藏
    connect(m_customTitleLineEdit, &QLineEdit::editingFinished, [this]() {
        m_hideTimer->start(); // 使用定时器延迟检查
    });

    // 设置鼠标事件
    m_customTitleWidget->installEventFilter(this);

    // 为输入框也安装事件过滤器，以便更精确地控制
    m_customTitleLineEdit->installEventFilter(this);

    // 安装全局事件过滤器来捕获鼠标点击
    qApp->installEventFilter(this);
}

void MainWindow::onCustomTitleCheckBoxToggled(bool checked)
{
    if (checked)
    {
        // 启用自定义标题，显示输入框
        showCustomTitleInput();
    }
    else
    {
        // 禁用自定义标题，隐藏输入框并清除标题
        hideCustomTitleInput();
        m_customTitle.clear();
        m_customTitleLineEdit->clear();
        updateWindowTitleWithCustomTitle();
    }
}

void MainWindow::onCustomTitleTextChanged(const QString &text)
{
    m_customTitle = text;
    updateWindowTitleWithCustomTitle();
}

void MainWindow::showCustomTitleInput()
{
    if (m_customTitleCheckBox->isChecked())
    {
        m_titleAnimation->setStartValue(m_customTitleLineEdit->maximumWidth());
        m_titleAnimation->setEndValue(kCustomTitleInputWidth);
        m_titleAnimation->start();
        m_customTitleLineEdit->setFocus();
    }
}

void MainWindow::hideCustomTitleInput()
{
    m_titleAnimation->setStartValue(m_customTitleLineEdit->maximumWidth());
    m_titleAnimation->setEndValue(0); // 收缩到0像素宽度
    m_titleAnimation->start();
}

void MainWindow::updateWindowTitleWithCustomTitle()
{
    const int kInstanceNum = m_instanceManager.getInstanceNumber();
    QString title;

    // 构建基础标题
    if (m_serialProcessor != nullptr && m_serialProcessor->isOpen())
    {
        title = QString("SerialT - %1").arg(m_appSettings.portName);
    }
    else
    {
        title = "SerialT";
    }

    // 添加实例编号
    if (kInstanceNum > 0)
    {
        title.append(QString(" [%1]").arg(kInstanceNum));
    }

    // 添加自定义标题
    if (m_customTitleCheckBox->isChecked() && !m_customTitle.isEmpty())
    {
        title.append(QString(" - %1").arg(m_customTitle));
    }

    setWindowTitle(title);
}

auto MainWindow::eventFilter(QObject *obj, QEvent *event) -> bool
{
    // 处理自定义标题控件的鼠标事件
    if (obj == m_customTitleWidget || obj == m_customTitleLineEdit)
    {
        if (event->type() == QEvent::Enter)
        {
            // 鼠标进入自定义标题区域，停止隐藏定时器
            m_hideTimer->stop();
            if (m_customTitleCheckBox->isChecked())
            {
                showCustomTitleInput();
            }
        }
        else if (event->type() == QEvent::Leave || (event->type() == QEvent::FocusOut && obj == m_customTitleLineEdit))
        {
            // 鼠标离开自定义标题区域或输入框失去焦点，启动延迟隐藏
            if (m_customTitleCheckBox->isChecked())
            {
                m_hideTimer->start();
            }
        }
        else if (event->type() == QEvent::FocusIn && obj == m_customTitleLineEdit)
        {
            // 输入框获得焦点，停止隐藏定时器
            m_hideTimer->stop();
        }
    }
    // 处理全局鼠标点击事件
    else if (event->type() == QEvent::MouseButtonPress)
    {
        // 如果点击的不是自定义标题相关控件，则隐藏输入框
        if (m_customTitleCheckBox->isChecked() && obj != m_customTitleWidget && obj != m_customTitleCheckBox
            && obj != m_customTitleLineEdit)
        {
            // 检查点击的对象是否是自定义标题控件的子控件
            QWidget *clickedWidget = qobject_cast<QWidget *>(obj);
            if (clickedWidget != nullptr && !m_customTitleWidget->isAncestorOf(clickedWidget))
            {
                hideCustomTitleInput();
            }
        }
    }

    return QMainWindow::eventFilter(obj, event);
}
