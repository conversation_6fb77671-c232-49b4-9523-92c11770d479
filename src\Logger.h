#ifndef LOGGER_H
#define LOGGER_H

#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QObject>

class Logger : public QObject {
    Q_OBJECT
public:
    explicit Logger(QObject *parent = nullptr);

public slots:
    void writeData(const QByteArray &data);

public:
    void startLogging(const QString &baseFilePath, bool splitEnabled, int splitSizeMB);
    void stopLogging();
    [[nodiscard]] auto isLogging() const -> bool;
    [[nodiscard]] auto getCurrentLogFilePath() const -> QString;
    void updateSettings(bool enabled, const QString &format);

private:
    void openNewLogFile();

private:
    QFile m_logFile;
    bool m_isLogging = false;
    bool m_splitEnabled = false;
    int m_splitSizeMB = 10;
    QString m_baseFilePath; // 不带序列号和后缀的基础文件路径
    int m_currentSplitIndex = 0;
    bool m_timestampEnabled = false;
    QString m_timestampFormat;
    bool m_isAtLineStart = true; // 新增：跟踪是否在行开始位置
};

#endif // LOGGER_H
