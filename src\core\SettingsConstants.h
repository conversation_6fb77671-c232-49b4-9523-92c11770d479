/**
 * @file SettingsConstants.h
 * @brief 定义了应用设置管理相关的所有常量，包括QSettings键名和UI配置参数。
 *
 * 本文件包含了SerialT项目中设置持久化系统的核心常量定义，涵盖配置键名、
 * 主题管理和用户界面配置等方面的参数。
 *
 * @note 这些常量影响设置的存储和读取，修改键名时需要考虑向后兼容性。
 */
#ifndef SETTINGSCONSTANTS_H
#define SETTINGSCONSTANTS_H

#include <string_view>

namespace App::Settings {

using namespace std::string_view_literals;

// #############################################################################
// # QSettings 键名常量 (QSettings Keys Constants)
// #############################################################################

/**
 * @brief QSettings键名命名空间
 * 
 * 包含所有用于QSettings存储和读取的键名常量，确保键名的一致性和可维护性。
 */
namespace Keys {

/**
 * @brief 主题设置组名
 * 
 * 在QSettings中用于组织主题相关设置的组名。
 */
inline constexpr std::string_view kThemeGroup = "Theme"sv;

/**
 * @brief 当前主题键名
 * 
 * 存储用户当前选择的主题名称。
 */
inline constexpr std::string_view kCurrentTheme = "currentTheme"sv;

/**
 * @brief 跟随系统主题键名
 * 
 * 存储是否跟随系统主题变化的布尔值设置。
 */
inline constexpr std::string_view kFollowSystemTheme = "followSystemTheme"sv;

} // namespace Keys

// #############################################################################
// # UI配置相关常量 (UI Configuration Related Constants)
// #############################################################################

/**
 * @brief 重连间隔调整步长
 * 
 * 在设置对话框中调整重连间隔时的数值步长，提供合适的调整精度。
 */
inline constexpr double kReconnectIntervalStep = 0.1;

/**
 * @brief 重连间隔显示小数位数
 * 
 * 在用户界面中显示重连间隔数值时的小数位数，平衡精度和可读性。
 */
inline constexpr int kReconnectIntervalDecimals = 1;

} // namespace App::Settings

#endif // SETTINGSCONSTANTS_H
