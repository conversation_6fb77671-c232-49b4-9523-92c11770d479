/**
 * @file UIConstants.h
 * @brief 定义了所有与UI尺寸、动画时长、颜色等相关的常量。
 */
#ifndef UICONSTANTS_H
#define UICONSTANTS_H

namespace App::UI {
    inline constexpr int kDefaultWindowWidth = 800;
    inline constexpr int kDefaultWindowHeight = 600;
    inline constexpr int kMinDialogWidth = 500;
    inline constexpr int kMinDialogHeight = 400;

    // Font
    inline constexpr int kMinFontSize = 6;
    inline constexpr int kMaxFontSize = 72;
    inline constexpr int kPreviewLabelMinHeight = 60;
    inline constexpr int kFontComboBoxExtraWidth = 50;

    // Buffer
    inline constexpr int kMinBufferSize = 1000;
    inline constexpr int kMaxBufferSize = 100000;
    inline constexpr int kBufferSizeStep = 1000;

    // Log
    inline constexpr int kMinLogSplitSize = 1;
    inline constexpr int kMaxLogSplitSize = 1024;
    inline constexpr int kLogPathButtonWidth = 40;

    // Quick Commands
    inline constexpr int kQuickCommandRowHeight = 32;
    inline constexpr int kMinQuickCommandInterval = 10;
    inline constexpr int kMaxQuickCommandInterval = 60000;
    inline constexpr int kDefaultQuickCommandInterval = 1000;

    // Layout
    inline constexpr int kLayoutSpacing = 10;
} // namespace App::UI

#endif // UICONSTANTS_H
