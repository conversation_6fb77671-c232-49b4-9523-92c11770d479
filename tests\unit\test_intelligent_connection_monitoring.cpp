#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QSignalSpy>
#include <QTest>
#include <QThread>
#include <QTimer>

#include "../../src/SerialProcessor.h"

class TestIntelligentConnectionMonitoring : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 智能连接监控测试
    void testConnectionQualityEvaluation();
    void testAdaptiveMonitorInterval();
    void testDataStallDetection();
    void testConnectionAnomalyDetection();
    void testQualityDescriptionMapping();
    void testMonitorIntervalRange();
    void testConnectionQualityChanged();
    void testSmartMonitoringIntegration();

private:
    SerialProcessor *m_processor;

    // 辅助方法
    void simulateHealthyConnection();
    void simulateUnhealthyConnection();
    void simulateDataActivity();
    void waitForMonitoringCycle();
};

void TestIntelligentConnectionMonitoring::initTestCase()
{
    qDebug() << "\n=== 智能连接监控测试套件 ===";
    qDebug() << "测试目标：验证智能连接监控的各项功能";
}

void TestIntelligentConnectionMonitoring::cleanupTestCase()
{
    qDebug() << "=== 智能连接监控测试完成 ===\n";
}

void TestIntelligentConnectionMonitoring::init()
{
    m_processor = new SerialProcessor(this);
}

void TestIntelligentConnectionMonitoring::cleanup()
{
    if (m_processor)
    {
        m_processor->closePort();
        delete m_processor;
        m_processor = nullptr;
    }
}

void TestIntelligentConnectionMonitoring::simulateHealthyConnection()
{
    // 模拟健康的连接状态
    // 注意：由于没有真实串口，我们主要测试API和逻辑
}

void TestIntelligentConnectionMonitoring::simulateUnhealthyConnection()
{
    // 模拟不健康的连接状态
}

void TestIntelligentConnectionMonitoring::simulateDataActivity()
{
    // 模拟数据活动
    QByteArray testData("Hello World\n");
    // 注意：这里不能直接调用writeData，因为没有真实串口
}

void TestIntelligentConnectionMonitoring::waitForMonitoringCycle()
{
    // 等待一个监控周期
    QTest::qWait(100);
    QApplication::processEvents();
}

void TestIntelligentConnectionMonitoring::testConnectionQualityEvaluation()
{
    qDebug() << "\n--- 测试：连接质量评估 ---";

    // 测试初始连接质量
    double initialQuality = m_processor->getConnectionQuality();
    qDebug() << "初始连接质量:" << initialQuality;

    QVERIFY(initialQuality >= 0.0 && initialQuality <= 1.0);
    QCOMPARE(initialQuality, 1.0); // 初始状态应该是最佳质量

    // 测试连接质量描述
    QString qualityDesc = m_processor->getConnectionQualityDescription();
    qDebug() << "连接质量描述:" << qualityDesc;

    QVERIFY(!qualityDesc.isEmpty());
    QCOMPARE(qualityDesc, "优秀"); // 初始状态应该是优秀

    qDebug() << "连接质量评估测试通过";
}

void TestIntelligentConnectionMonitoring::testAdaptiveMonitorInterval()
{
    qDebug() << "\n--- 测试：自适应监控间隔 ---";

    // 测试初始监控间隔
    int initialInterval = m_processor->getCurrentMonitorInterval();
    qDebug() << "初始监控间隔:" << initialInterval << "ms";

    QVERIFY(initialInterval > 0);
    QCOMPARE(initialInterval, 2000); // 默认2秒

    // 测试设置监控间隔范围
    m_processor->setMonitorIntervalRange(MonitorIntervalRange{1000, 8000});

    // 验证间隔仍在合理范围内
    int currentInterval = m_processor->getCurrentMonitorInterval();
    qDebug() << "设置范围后的监控间隔:" << currentInterval << "ms";

    QVERIFY(currentInterval >= 1000 && currentInterval <= 8000);

    // 测试极端值
    m_processor->setMonitorIntervalRange(MonitorIntervalRange{50, 100000}); // 应该被限制

    qDebug() << "自适应监控间隔测试通过";
}

void TestIntelligentConnectionMonitoring::testDataStallDetection()
{
    qDebug() << "\n--- 测试：数据停滞检测 ---";

    // 监听数据停滞信号
    QSignalSpy stallSpy(m_processor, &SerialProcessor::dataStallDetected);

    // 验证信号存在且有效
    QVERIFY(stallSpy.isValid());

    // 由于没有真实串口，我们无法直接测试数据停滞
    // 但可以验证信号的定义和基本功能

    qDebug() << "数据停滞检测功能验证通过";
}

void TestIntelligentConnectionMonitoring::testConnectionAnomalyDetection()
{
    qDebug() << "\n--- 测试：连接异常检测 ---";

    // 监听异常检测信号
    QSignalSpy anomalySpy(m_processor, &SerialProcessor::connectionAnomalyDetected);

    // 验证信号存在且有效
    QVERIFY(anomalySpy.isValid());

    // 由于没有真实连接，我们无法触发异常检测
    // 但可以验证信号的定义和基本功能

    qDebug() << "连接异常检测功能验证通过";
}

void TestIntelligentConnectionMonitoring::testQualityDescriptionMapping()
{
    qDebug() << "\n--- 测试：质量描述映射 ---";

    // 测试不同质量分数对应的描述
    struct QualityTest
    {
        double quality;
        QString expectedDesc;
    };

    QList<QualityTest> testCases = {{1.0, "优秀"},  {0.95, "优秀"}, {0.85, "良好"}, {0.65, "一般"},
                                    {0.45, "较差"}, {0.15, "很差"}, {0.0, "很差"}};

    // 注意：由于getConnectionQualityDescription()是基于内部状态的，
    // 我们无法直接设置质量分数，但可以验证方法的存在和基本逻辑

    QString currentDesc = m_processor->getConnectionQualityDescription();
    QVERIFY(!currentDesc.isEmpty());

    // 验证描述是预期值之一
    QStringList validDescriptions = {"优秀", "良好", "一般", "较差", "很差"};
    QVERIFY(validDescriptions.contains(currentDesc));

    qDebug() << "当前连接质量描述:" << currentDesc;
    qDebug() << "质量描述映射测试通过";
}

void TestIntelligentConnectionMonitoring::testMonitorIntervalRange()
{
    qDebug() << "\n--- 测试：监控间隔范围设置 ---";

    // 监听间隔变化信号
    QSignalSpy intervalSpy(m_processor, &SerialProcessor::monitorIntervalChanged);

    // 测试正常范围设置
    m_processor->setMonitorIntervalRange(MonitorIntervalRange{500, 5000});
    int currentInterval = m_processor->getCurrentMonitorInterval();
    QVERIFY(currentInterval >= 500 && currentInterval <= 5000);

    // 测试边界值
    m_processor->setMonitorIntervalRange(MonitorIntervalRange{100, 60000});
    currentInterval = m_processor->getCurrentMonitorInterval();
    QVERIFY(currentInterval >= 100 && currentInterval <= 60000);

    // 测试无效范围（应该被修正）
    m_processor->setMonitorIntervalRange(MonitorIntervalRange{50, 100000}); // 超出限制
    currentInterval = m_processor->getCurrentMonitorInterval();
    QVERIFY(currentInterval >= 100);   // 最小值应该被修正为100
    QVERIFY(currentInterval <= 60000); // 最大值应该被修正为60000

    qDebug() << "最终监控间隔:" << currentInterval << "ms";
    qDebug() << "监控间隔范围设置测试通过";
}

void TestIntelligentConnectionMonitoring::testConnectionQualityChanged()
{
    qDebug() << "\n--- 测试：连接质量变化信号 ---";

    // 监听质量变化信号
    QSignalSpy qualitySpy(m_processor, &SerialProcessor::connectionQualityChanged);

    // 验证信号存在
    QVERIFY(qualitySpy.isValid());

    // 由于没有真实连接，我们无法触发质量变化
    // 但可以验证信号的定义和基本功能

    double currentQuality = m_processor->getConnectionQuality();
    QVERIFY(currentQuality >= 0.0 && currentQuality <= 1.0);

    qDebug() << "连接质量变化信号验证通过";
}

void TestIntelligentConnectionMonitoring::testSmartMonitoringIntegration()
{
    qDebug() << "\n--- 测试：智能监控集成 ---";

    // 测试所有智能监控功能的集成

    // 1. 验证所有新增的公共接口
    QVERIFY(m_processor->getConnectionQuality() >= 0.0);
    QVERIFY(m_processor->getCurrentMonitorInterval() > 0);
    QVERIFY(!m_processor->getConnectionQualityDescription().isEmpty());

    // 2. 验证所有新增的信号
    QSignalSpy qualitySpy(m_processor, &SerialProcessor::connectionQualityChanged);
    QSignalSpy intervalSpy(m_processor, &SerialProcessor::monitorIntervalChanged);
    QSignalSpy stallSpy(m_processor, &SerialProcessor::dataStallDetected);
    QSignalSpy anomalySpy(m_processor, &SerialProcessor::connectionAnomalyDetected);

    QVERIFY(qualitySpy.isValid());
    QVERIFY(intervalSpy.isValid());
    QVERIFY(stallSpy.isValid());
    QVERIFY(anomalySpy.isValid());

    // 3. 测试监控间隔范围设置
    m_processor->setMonitorIntervalRange(MonitorIntervalRange{1000, 6000});
    int interval = m_processor->getCurrentMonitorInterval();
    QVERIFY(interval >= 1000 && interval <= 6000);

    qDebug() << "智能监控集成测试通过";
    qDebug() << "- 连接质量:" << m_processor->getConnectionQuality();
    qDebug() << "- 质量描述:" << m_processor->getConnectionQualityDescription();
    qDebug() << "- 监控间隔:" << m_processor->getCurrentMonitorInterval() << "ms";
}

QTEST_MAIN(TestIntelligentConnectionMonitoring)
#include "test_intelligent_connection_monitoring.moc"
