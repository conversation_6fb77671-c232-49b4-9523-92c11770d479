#ifndef HIGHPERFORMANCETERMINAL_H
#define HIGHPERFORMANCETERMINAL_H

#include "core/AppTypes.h"

#include <QFlags>
#include <QKeyEvent>
#include <QTimer>
#include <QWidget>

#include <climits>
#include <cstdint>

class VT100Parser;
struct TerminalCommand;
class QChar;
class QFontMetrics;
class QWheelEvent;
class QMouseEvent;
class QResizeEvent;
class QPaintEvent;
class QScrollBar;

class HighPerformanceTerminal : public QWidget {
    Q_OBJECT

public:
    // 常量定义
    static constexpr int kUnlimitedColumns = INT_MAX; // 自动换行模式下的无限列数
    static constexpr int kViewportSafetyMargin = 5;   // 视口剔除的安全余量
    enum class FindFlag : std::uint8_t
    {
        NoFlags = 0x00,
        CaseSensitive = 0x01,
        WholeWord = 0x02,
        Regex = 0x04,
        InSelection = 0x08
    };
    Q_DECLARE_FLAGS(FindFlags, FindFlag)

    enum ScrollPolicy : std::uint8_t
    {
        SmartScroll,      // 智能滚动
        ScrollToBottom,   // 始终滚动
        DisableAutoScroll // 禁用滚动
    };

    explicit HighPerformanceTerminal(QWidget *parent = nullptr);
    void setParser(VT100Parser *parser);

    // 设置缓冲区大小
    void setBufferSize(int lines);
    [[nodiscard]] auto bufferSize() const -> int { return m_bufferSize; }
    void setBaseFont(const QFont &baseFont);
    void setScrollPolicy(ScrollPolicy policy);
    void setNewLineMode(int mode);
    void setScrollOnInput(bool enabled);
    void setWordWrapEnabled(bool enabled);
    [[nodiscard]] auto wordWrapEnabled() const -> bool { return m_wordWrapEnabled; }
    void setDisplayMode(DisplayMode mode);
    [[nodiscard]] auto displayMode() const -> DisplayMode { return m_displayMode; }
    void appendRawData(const QByteArray &rawData);
    [[nodiscard]] auto fontMetrics() const -> QFontMetrics;

public slots:
    void clear();
    void scrollToBottom();
    void scheduleUpdate();
    void copy();
    void paste();
    void selectAll();
    void zoomIn();
    void zoomOut();
    void zoomReset();
    void suspendRendering(bool suspended);

    // 搜索功能
    void find(const QString &term, FindFlags flags);
    void findNext();
    void findPrevious();

signals:
    void dataReadyToSend(const QByteArray &data);
    void searchResultsUpdated(int matchCount, int currentIndex);

    void screenCleared();
private slots:
    void processPendingCommands();
    void autoScroll();

private:
    // 内部常量定义
    static constexpr int kDefaultCols = 80;
    static constexpr int kDefaultRows = 24;
    static constexpr int kDefaultBufferSize = 1000;
    static constexpr auto kDefaultFontFamily = "Courier New";
    static constexpr int kDefaultFontSize = 10;
    static constexpr int kMinFontSize = 6;
    static constexpr int kDefaultZoomLevel = 0;
    static constexpr int kScrollBarProximityTolerance = 5;
    static constexpr int kCommandsPerChunk = 5000;
    static constexpr int kMouseScrollHotZone = 20;
    static constexpr int kMouseScrollIntervalMs = 30;
    static constexpr int kDefaultTabWidth = 8;
    static constexpr int kInvalidSearchResultIndex = -1;
    static constexpr QColor kSearchHighlightColor = QColor(255, 165, 0, 178);        // 黄色半透明
    static constexpr QColor kActiveSearchHighlightColor = QColor(30, 144, 255, 204); // 蓝色半透明
    static constexpr int kNewLineModeCrLf = 0;
    static constexpr int kNewLineModeCr = 1;
    static constexpr int kNewLineModeLf = 2;

    // 定义一行终端数据的结构
    class TerminalLine {
    private:
        QVector<QChar> m_chars;     // 字符数据
        QVector<QColor> m_fgColors; // 前景色
        QVector<QColor> m_bgColors; // 背景色
        QVector<bool> m_boldFlags;  // 粗体标志

    public:
        // 构造函数，预分配空间
        explicit TerminalLine(int columns = kDefaultCols)
        {
            m_chars.resize(columns, ' ');
            m_fgColors.resize(columns, Qt::green);
            m_bgColors.resize(columns, Qt::black);
            m_boldFlags.resize(columns, false);
        }

        // 只读访问器
        [[nodiscard]] auto chars() const noexcept -> const QVector<QChar> & { return m_chars; }
        [[nodiscard]] auto fgColors() const noexcept -> const QVector<QColor> & { return m_fgColors; }
        [[nodiscard]] auto bgColors() const noexcept -> const QVector<QColor> & { return m_bgColors; }
        [[nodiscard]] auto boldFlags() const noexcept -> const QVector<bool> & { return m_boldFlags; }

        // 受控修改接口
        void setChar(int index, QChar ch, QColor fg, QColor bg, bool bold)
        {
            if (index >= 0 && index < m_chars.size())
            {
                m_chars[index] = ch;
                m_fgColors[index] = fg;
                m_bgColors[index] = bg;
                m_boldFlags[index] = bold;
            }
        }

        void resize(int newSize)
        {
            m_chars.resize(newSize, ' ');
            m_fgColors.resize(newSize, Qt::green);
            m_bgColors.resize(newSize, Qt::black);
            m_boldFlags.resize(newSize, false);
        }

        void resize(int newSize, QChar fillChar, QColor fillFg, QColor fillBg, bool fillBold)
        {
            m_chars.resize(newSize, fillChar);
            m_fgColors.resize(newSize, fillFg);
            m_bgColors.resize(newSize, fillBg);
            m_boldFlags.resize(newSize, fillBold);
        }

        // 重置行内容
        void reset(int columns = kDefaultCols)
        {
            // 确保向量在填充前大小正确
            if (m_chars.size() != columns)
            {
                m_chars.resize(columns);
                m_fgColors.resize(columns);
                m_bgColors.resize(columns);
                m_boldFlags.resize(columns);
            }
            m_chars.fill(' ');
            m_fgColors.fill(Qt::green); // 或你的默认颜色
            m_bgColors.fill(Qt::black); // 或你的默认颜色
            m_boldFlags.fill(false);
        }

        [[nodiscard]] auto size() const noexcept -> int { return static_cast<int>(m_chars.size()); }
        [[nodiscard]] auto isEmpty() const noexcept -> bool { return m_chars.isEmpty(); }
    };

    // 静态工厂函数 - 解决初始化依赖关系
    static auto createTerminalFontMetrics() -> QFontMetrics;
    static auto calculateCharDimensions(const QFontMetrics &fm) -> std::pair<int, int>;

    void applyFont();
    void appendChar(const QChar &ch);
    void setCursorPosition(int row, int col);
    void eraseInDisplay(int mode);
    void eraseInLine(int mode);
    void resetAttributes();
    void backspace();
    void horizontalTab();
    static void bell();
    void formFeed();
    void verticalTab();

protected:
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;

private:
    // 更新滚动条
    void updateScrollBar();
    void updateHorizontalScrollBar();
    void updateTerminalLayout();
    void reLayoutContent(); // 重新布局现有内容
    // 绘制相关的辅助函数
    // NOLINTBEGIN(misc-non-private-member-variables-in-classes)
    struct ViewportInfo
    {
        int startRow{0}, endRow{0};
        int startCol{0}, endCol{0};
        int horizontalOffset{0};
        int visibleStartLineAbs{0};
        [[nodiscard]] auto isValid() const -> bool { return startRow < endRow; }
    };
    // NOLINTEND(misc-non-private-member-variables-in-classes)

    [[nodiscard]] auto calculateViewport(const QRect &rect) const -> ViewportInfo;
    static void setupPainter(QPainter &painter);
    void drawTextContent(QPainter &painter, const ViewportInfo &viewport) const;
    void drawCursor(QPainter &painter, const ViewportInfo &viewport) const;
    void drawSelection(QPainter &painter, const ViewportInfo &viewport) const;
    struct TextSegmentInfo
    {
        int row{0};
        int startCol{0};
        int endCol{0};
        QColor fgColor{Qt::white};
        QColor bgColor{Qt::black};
    };

    void drawTextSegment(QPainter &painter, const TerminalLine &line, const TextSegmentInfo &segmentInfo,
                         const ViewportInfo &viewport) const;

    // 获取当前的水平偏移量（考虑自动换行模式）
    [[nodiscard]] auto getHorizontalOffset() const -> int;
    // 获取可视区域的起始行
    [[nodiscard]] auto visibleStartLine() const -> int;
    [[nodiscard]] auto bufferPosition(const QPoint &mousePos) const -> QPoint;
    [[nodiscard]] auto selectedText() const -> QString;
    void ensureSearchResultVisible();

    // 终端尺寸
    int m_columns;
    int m_rows;

    // 光标位置
    int m_cursorX;
    int m_cursorY;

    // 字体度量
    QFontMetrics m_fontMetrics;

    // 字符宽度和高度
    int m_charWidth;
    int m_charHeight;

    // 背景和前景色
    QColor m_backgroundColor;
    QColor m_foregroundColor;
    QColor m_currentForegroundColor;
    QColor m_currentBackgroundColor;
    bool m_currentBold;

    // 滚动缓冲区
    QVector<TerminalLine> m_buffer;
    int m_bufferSize;     // 缓冲区大小(行数)
    int m_firstLineIndex; // 缓冲区中第一行的索引
    int m_totalLines;     // 总行数（包括不可见的）
    int m_visibleTopLine; // 可视区域的顶行号（同步状态）

    // 滚动条
    QScrollBar *m_scrollBar;
    QScrollBar *m_horizontalScrollBar;
    ScrollPolicy m_scrollPolicy; // 滚动策略
    bool m_isScrolledByUser;     // 标记用户是否已手动滚动
    bool m_scrollOnInputEnabled; // 新增：是否启用输入后滚动
    bool m_wordWrapEnabled;      // 新增：是否启用自动换行
    DisplayMode m_displayMode;   // 新增：数据显示模式
    int m_newLineMode;           // 0: \\r\n, 1: \\r, 2: \n
    bool m_isUserInput = false;  // 标记是否为用户输入触发的数据
    bool m_updateScheduled = false;
    bool m_isReLayouting = false; // 标记是否正在重新布局，避免递归
    int m_lastLayoutWidth = 0;    // 上次布局时的宽度，用于检测变化
    VT100Parser *m_parser = nullptr;
    bool m_isRenderingSuspended = false;

    // 选择区
    bool m_isSelecting;
    QPoint m_selectionStart;
    QPoint m_selectionEnd;

    int m_maxLineWidth = 0;

    QTimer *m_scrollTimer;

    // 字体管理
    QFont m_baseFont;
    int m_zoomLevel = kDefaultZoomLevel;

    // 搜索相关
    QString m_searchTerm;
    FindFlags m_searchFlags;
    QVector<QPoint> m_searchResults; // 存储每个匹配项的 {行, 列}
    int m_currentSearchResultIndex;
    QColor m_searchHighlightColor;
    QColor m_activeSearchHighlightColor;
};

Q_DECLARE_OPERATORS_FOR_FLAGS(HighPerformanceTerminal::FindFlags)

#endif // HIGHPERFORMANCETERMINAL_H
