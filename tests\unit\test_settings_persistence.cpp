#include <QApplication>
#include <QFont>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QSerialPort>
#include <QSettings>
#include <QStandardPaths>
#include <QTemporaryDir>
#include <QtTest/QtTest>
#include "../../src/SettingsDialog.h"

class TestSettingsPersistence : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基础功能测试
    void testAppSettingsStructure();

    // 设置持久化测试
    void testGeneralSettingsPersistence();
    void testSerialSettingsPersistence();
    void testAppearanceSettingsPersistence();
    void testLoggingSettingsPersistence();
    void testQuickCommandsPersistence();

    // 设置验证测试
    void testSettingsValidation();
    void testDefaultSettings();
    void testSettingsUpgrade();

    // 边界条件测试
    void testInvalidSettings();
    void testCorruptedSettings();

private:
    QTemporaryDir *m_tempDir;
    QString m_originalSettingsPath;

    // 辅助方法
    AppSettings createTestSettings();
    void compareSettings(const AppSettings &expected, const AppSettings &actual);
    void saveSettingsManually(const AppSettings &settings);
    AppSettings loadSettingsManually();
    void clearAllSettings();
};

void TestSettingsPersistence::initTestCase()
{
    qDebug() << "Starting Settings Persistence tests";

    // 确保有QApplication实例
    if (QApplication::instance() == nullptr)
    {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }

    // 创建临时目录用于测试
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
}

void TestSettingsPersistence::cleanupTestCase()
{
    qDebug() << "Settings Persistence tests completed";

    if (m_tempDir != nullptr)
    {
        delete m_tempDir;
        m_tempDir = nullptr;
    }
}

void TestSettingsPersistence::init()
{
    // 每个测试前清理设置
    clearAllSettings();
}

void TestSettingsPersistence::cleanup()
{
    // 每个测试后清理设置
    clearAllSettings();
}

void TestSettingsPersistence::testAppSettingsStructure()
{
    // 测试AppSettings结构体的基本功能
    AppSettings settings;

    // 测试默认值设置
    settings.minimizeToTrayOnClose = true;
    settings.alwaysShowInTray = false;
    settings.terminalBufferSize = 5000;
    settings.baudRate = 115200;
    settings.dataBits = QSerialPort::Data8;
    settings.parity = QSerialPort::NoParity;
    settings.stopBits = QSerialPort::OneStop;

    // 验证设置
    QCOMPARE(settings.minimizeToTrayOnClose, true);
    QCOMPARE(settings.alwaysShowInTray, false);
    QCOMPARE(settings.terminalBufferSize, 5000);
    QCOMPARE(settings.baudRate, 115200);
    QCOMPARE(settings.dataBits, QSerialPort::Data8);
    QCOMPARE(settings.parity, QSerialPort::NoParity);
    QCOMPARE(settings.stopBits, QSerialPort::OneStop);
}

void TestSettingsPersistence::testGeneralSettingsPersistence()
{
    // 测试常规设置的持久化
    AppSettings settings = createTestSettings();

    // 修改常规设置
    settings.minimizeToTrayOnClose = false;
    settings.alwaysShowInTray = true;
    settings.terminalBufferSize = 8000;
    settings.terminalScrollPolicy = 1;
    settings.scrollOnInput = false;
    settings.pauseOnResize = true;
    settings.wordWrapEnabled = true;

    // 保存设置
    saveSettingsManually(settings);

    // 加载设置
    AppSettings loadedSettings = loadSettingsManually();

    // 验证常规设置
    QCOMPARE(loadedSettings.minimizeToTrayOnClose, false);
    QCOMPARE(loadedSettings.alwaysShowInTray, true);
    QCOMPARE(loadedSettings.terminalBufferSize, 8000);
    QCOMPARE(loadedSettings.terminalScrollPolicy, 1);
    QCOMPARE(loadedSettings.scrollOnInput, false);
    QCOMPARE(loadedSettings.pauseOnResize, true);
    QCOMPARE(loadedSettings.wordWrapEnabled, true);
}

void TestSettingsPersistence::testSerialSettingsPersistence()
{
    // 测试串口设置的持久化
    AppSettings settings = createTestSettings();

    // 修改串口设置
    settings.portName = "COM3";
    settings.baudRate = 9600;
    settings.dataBits = QSerialPort::Data7;
    settings.parity = QSerialPort::EvenParity;
    settings.stopBits = QSerialPort::TwoStop;
    settings.sendNewLineMode = 2; // LF
    settings.serialEchoEnabled = true;

    // 保存设置
    saveSettingsManually(settings);

    // 加载设置
    AppSettings loadedSettings = loadSettingsManually();

    // 验证串口设置
    QCOMPARE(loadedSettings.portName, QString("COM3"));
    QCOMPARE(loadedSettings.baudRate, 9600);
    QCOMPARE(loadedSettings.dataBits, QSerialPort::Data7);
    QCOMPARE(loadedSettings.parity, QSerialPort::EvenParity);
    QCOMPARE(loadedSettings.stopBits, QSerialPort::TwoStop);
    QCOMPARE(loadedSettings.sendNewLineMode, 2);
    QCOMPARE(loadedSettings.serialEchoEnabled, true);
}

void TestSettingsPersistence::testAppearanceSettingsPersistence()
{
    // 测试外观设置的持久化
    AppSettings settings = createTestSettings();

    // 修改字体设置
    QFont testFont("Arial", 14);
    testFont.setBold(true);
    settings.terminalFont = testFont;

    // 保存设置
    saveSettingsManually(settings);

    // 加载设置
    AppSettings loadedSettings = loadSettingsManually();

    // 验证字体设置
    QCOMPARE(loadedSettings.terminalFont.family(), QString("Arial"));
    QCOMPARE(loadedSettings.terminalFont.pointSize(), 14);
    QCOMPARE(loadedSettings.terminalFont.bold(), true);
}

void TestSettingsPersistence::testLoggingSettingsPersistence()
{
    // 测试日志设置的持久化
    AppSettings settings = createTestSettings();

    // 修改日志设置
    settings.logPath = "/test/log/path";
    settings.logFileNameFormat = "test_%Y%m%d.log";
    settings.logTimestampEnabled = false;
    settings.logTimestampFormat = "[HH:mm:ss] ";
    settings.logSplitEnabled = true;
    settings.logSplitSizeMB = 20;
    settings.useDefaultLogViewer = false;
    settings.externalLogViewerPath = "/path/to/viewer";
    settings.autoEnableLogOnPortOpen = false; // 测试非默认值

    // 保存设置
    saveSettingsManually(settings);

    // 加载设置
    AppSettings loadedSettings = loadSettingsManually();

    // 验证日志设置
    QCOMPARE(loadedSettings.logPath, QString("/test/log/path"));
    QCOMPARE(loadedSettings.logFileNameFormat, QString("test_%Y%m%d.log"));
    QCOMPARE(loadedSettings.logTimestampEnabled, false);
    QCOMPARE(loadedSettings.logTimestampFormat, QString("[HH:mm:ss] "));
    QCOMPARE(loadedSettings.logSplitEnabled, true);
    QCOMPARE(loadedSettings.logSplitSizeMB, 20);
    QCOMPARE(loadedSettings.useDefaultLogViewer, false);
    QCOMPARE(loadedSettings.externalLogViewerPath, QString("/path/to/viewer"));
    QCOMPARE(loadedSettings.autoEnableLogOnPortOpen, false);
}

void TestSettingsPersistence::testQuickCommandsPersistence()
{
    // 测试快捷命令的持久化
    AppSettings settings = createTestSettings();

    // 添加测试快捷命令
    QuickCommand cmd1;
    cmd1.uuid = "test-uuid-1";
    cmd1.name = "Test Command 1";
    cmd1.command = "AT+TEST1";
    cmd1.enabled = true;
    cmd1.format = CommandFormat::ASCII;
    cmd1.isCycle = false;
    cmd1.cycleInterval = 0;

    QuickCommand cmd2;
    cmd2.uuid = "test-uuid-2";
    cmd2.name = "Test Command 2";
    cmd2.command = "FF AA BB CC";
    cmd2.enabled = false;
    cmd2.format = CommandFormat::HEX;
    cmd2.isCycle = true;
    cmd2.cycleInterval = 1000;

    settings.quickCommands.append(cmd1);
    settings.quickCommands.append(cmd2);

    // 保存设置
    saveSettingsManually(settings);

    // 加载设置
    AppSettings loadedSettings = loadSettingsManually();

    // 验证快捷命令
    QCOMPARE(loadedSettings.quickCommands.size(), 2);

    const QuickCommand &loadedCmd1 = loadedSettings.quickCommands[0];
    QCOMPARE(loadedCmd1.uuid, QString("test-uuid-1"));
    QCOMPARE(loadedCmd1.name, QString("Test Command 1"));
    QCOMPARE(loadedCmd1.command, QString("AT+TEST1"));
    QCOMPARE(loadedCmd1.enabled, true);
    QCOMPARE(loadedCmd1.format, CommandFormat::ASCII);
    QCOMPARE(loadedCmd1.isCycle, false);

    const QuickCommand &loadedCmd2 = loadedSettings.quickCommands[1];
    QCOMPARE(loadedCmd2.uuid, QString("test-uuid-2"));
    QCOMPARE(loadedCmd2.name, QString("Test Command 2"));
    QCOMPARE(loadedCmd2.command, QString("FF AA BB CC"));
    QCOMPARE(loadedCmd2.enabled, false);
    QCOMPARE(loadedCmd2.format, CommandFormat::HEX);
    QCOMPARE(loadedCmd2.isCycle, true);
    QCOMPARE(loadedCmd2.cycleInterval, 1000);
}

void TestSettingsPersistence::testSettingsValidation()
{
    // 测试设置验证
    AppSettings settings = createTestSettings();

    // 测试边界值
    settings.terminalBufferSize = 999; // 低于最小值
    saveSettingsManually(settings);
    AppSettings loadedSettings = loadSettingsManually();
    // 注意：实际应用中可能会有验证逻辑，这里只测试保存/加载
    QCOMPARE(loadedSettings.terminalBufferSize, 999);

    // 测试超大值
    settings.terminalBufferSize = 200000; // 超过合理范围
    saveSettingsManually(settings);
    loadedSettings = loadSettingsManually();
    QCOMPARE(loadedSettings.terminalBufferSize, 200000);
}

void TestSettingsPersistence::testDefaultSettings()
{
    // 测试默认设置加载
    clearAllSettings();

    AppSettings defaultSettings = loadSettingsManually();

    // 验证默认值（基于MainWindow::loadApplicationSettings中的默认值）
    QCOMPARE(defaultSettings.minimizeToTrayOnClose, true);
    QCOMPARE(defaultSettings.alwaysShowInTray, true);
    QCOMPARE(defaultSettings.terminalBufferSize, 5000);
    QCOMPARE(defaultSettings.baudRate, 115200);
    QCOMPARE(defaultSettings.dataBits, QSerialPort::Data8);
    QCOMPARE(defaultSettings.scrollOnInput, true);
    QCOMPARE(defaultSettings.wordWrapEnabled, false);
}

void TestSettingsPersistence::testSettingsUpgrade()
{
    // 测试设置升级/迁移
    // 这里模拟旧版本设置的情况
    QSettings settings("LSDT", "SerialT");

    // 写入一些"旧版本"设置
    settings.beginGroup("General");
    settings.setValue("terminalBufferSize", 3000); // 旧的默认值
    settings.endGroup();

    // 加载设置（应该使用新的默认值填充缺失项）
    AppSettings loadedSettings = loadSettingsManually();

    // 验证旧设置被保留
    QCOMPARE(loadedSettings.terminalBufferSize, 3000);

    // 验证新设置使用默认值
    QCOMPARE(loadedSettings.scrollOnInput, true); // 新增的设置应该有默认值
}

void TestSettingsPersistence::testInvalidSettings()
{
    // 测试无效设置的处理
    QSettings settings("LSDT", "SerialT");

    // 写入无效的设置值
    settings.beginGroup("Serial");
    settings.setValue("baudRate", "invalid_number");
    settings.setValue("dataBits", 999); // 无效的数据位
    settings.endGroup();

    AppSettings loadedSettings = loadSettingsManually();

    // 验证无效的字符串转换为int会返回0
    QCOMPARE(loadedSettings.baudRate, 0); // QString::toInt()对无效字符串返回0
    // 注意：QSerialPort::DataBits的无效值处理取决于具体实现
}

void TestSettingsPersistence::testCorruptedSettings()
{
    // 测试损坏的设置文件处理
    QSettings settings("LSDT", "SerialT");

    // 写入损坏的JSON数据
    settings.beginGroup("QuickCommands");
    settings.setValue("json", "invalid json data {{{");
    settings.endGroup();

    AppSettings loadedSettings = loadSettingsManually();

    // 验证损坏的JSON不会导致崩溃，快捷命令列表应该为空
    QVERIFY(loadedSettings.quickCommands.isEmpty());
}

// 辅助方法实现
AppSettings TestSettingsPersistence::createTestSettings()
{
    AppSettings settings;

    // 设置默认测试值
    settings.minimizeToTrayOnClose = true;
    settings.alwaysShowInTray = true;
    settings.windowStaysOnTop = false;
    settings.terminalBufferSize = 5000;
    settings.terminalScrollPolicy = 0;
    settings.scrollOnInput = true;
    settings.pauseOnResize = false;
    settings.wordWrapEnabled = false;
    settings.displayMode = DisplayMode::ASCII;

    settings.portName = "COM1";
    settings.baudRate = 115200;
    settings.dataBits = QSerialPort::Data8;
    settings.parity = QSerialPort::NoParity;
    settings.stopBits = QSerialPort::OneStop;
    settings.sendNewLineMode = 0;
    settings.serialEchoEnabled = false;

    settings.logPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    settings.logFileNameFormat = "log_%Y-%m-%d_%H-%M-%S.txt";
    settings.logTimestampEnabled = true;
    settings.logTimestampFormat = "[yyyy-MM-dd hh:mm:ss.zzz] ";
    settings.logSplitEnabled = false;
    settings.logSplitSizeMB = 10;
    settings.useDefaultLogViewer = true;
    settings.externalLogViewerPath = "";
    settings.autoEnableLogOnPortOpen = true; // 默认值

    settings.terminalFont = QFont("Courier New", 10);

    return settings;
}

void TestSettingsPersistence::compareSettings(const AppSettings &expected, const AppSettings &actual)
{
    QCOMPARE(actual.minimizeToTrayOnClose, expected.minimizeToTrayOnClose);
    QCOMPARE(actual.alwaysShowInTray, expected.alwaysShowInTray);
    QCOMPARE(actual.terminalBufferSize, expected.terminalBufferSize);
    QCOMPARE(actual.baudRate, expected.baudRate);
    QCOMPARE(actual.wordWrapEnabled, expected.wordWrapEnabled);
    // 可以添加更多比较项
}

void TestSettingsPersistence::saveSettingsManually(const AppSettings &settings)
{
    // 模拟MainWindow::saveApplicationSettings的逻辑
    QSettings qsettings("LSDT", "SerialT");

    qsettings.beginGroup("General");
    qsettings.setValue("minimizeToTrayOnClose", settings.minimizeToTrayOnClose);
    qsettings.setValue("alwaysShowInTray", settings.alwaysShowInTray);
    qsettings.setValue("terminalBufferSize", settings.terminalBufferSize);
    qsettings.setValue("terminalScrollPolicy", settings.terminalScrollPolicy);
    qsettings.setValue("scrollOnInput", settings.scrollOnInput);
    qsettings.setValue("pauseOnResize", settings.pauseOnResize);
    qsettings.setValue("wordWrapEnabled", settings.wordWrapEnabled);
    qsettings.setValue("displayMode", static_cast<int>(settings.displayMode));
    qsettings.endGroup();

    qsettings.beginGroup("Serial");
    qsettings.setValue("portName", settings.portName);
    qsettings.setValue("baudRate", settings.baudRate);
    qsettings.setValue("dataBits", static_cast<int>(settings.dataBits));
    qsettings.setValue("parity", static_cast<int>(settings.parity));
    qsettings.setValue("stopBits", static_cast<int>(settings.stopBits));
    qsettings.setValue("newLineMode", settings.sendNewLineMode);
    qsettings.setValue("echoEnabled", settings.serialEchoEnabled);
    qsettings.endGroup();

    qsettings.beginGroup("Logging");
    qsettings.setValue("path", settings.logPath);
    qsettings.setValue("fileNameFormat", settings.logFileNameFormat);
    qsettings.setValue("logTimestampEnabled", settings.logTimestampEnabled);
    qsettings.setValue("logTimestampFormat", settings.logTimestampFormat);
    qsettings.setValue("splitEnabled", settings.logSplitEnabled);
    qsettings.setValue("splitSizeMB", settings.logSplitSizeMB);
    qsettings.setValue("useDefaultLogViewer", settings.useDefaultLogViewer);
    qsettings.setValue("externalLogViewerPath", settings.externalLogViewerPath);
    qsettings.setValue("autoEnableLogOnPortOpen", settings.autoEnableLogOnPortOpen);
    qsettings.endGroup();

    qsettings.beginGroup("Terminal");
    qsettings.setValue("font", settings.terminalFont.toString());
    qsettings.endGroup();

    // 保存快捷命令
    qsettings.beginGroup("QuickCommands");
    QJsonArray jsonArray;
    for (const auto &cmd : settings.quickCommands)
    {
        QJsonObject obj;
        obj["uuid"] = cmd.uuid;
        obj["name"] = cmd.name;
        obj["command"] = cmd.command;
        obj["enabled"] = cmd.enabled;
        obj["format"] = (cmd.format == CommandFormat::HEX) ? "HEX" : "ASCII";
        obj["isCycle"] = cmd.isCycle;
        obj["cycleInterval"] = cmd.cycleInterval;
        jsonArray.append(obj);
    }
    QJsonDocument doc(jsonArray);
    qsettings.setValue("json", QString(doc.toJson(QJsonDocument::Compact)));
    qsettings.endGroup();
}

AppSettings TestSettingsPersistence::loadSettingsManually()
{
    // 模拟MainWindow::loadApplicationSettings的逻辑
    QSettings qsettings("LSDT", "SerialT");
    AppSettings settings;

    qsettings.beginGroup("General");
    settings.minimizeToTrayOnClose = qsettings.value("minimizeToTrayOnClose", true).toBool();
    settings.alwaysShowInTray = qsettings.value("alwaysShowInTray", true).toBool();
    settings.windowStaysOnTop = qsettings.value("windowStaysOnTop", false).toBool();
    settings.terminalBufferSize = qsettings.value("terminalBufferSize", 5000).toInt();
    settings.terminalScrollPolicy = qsettings.value("terminalScrollPolicy", 0).toInt();
    settings.scrollOnInput = qsettings.value("scrollOnInput", true).toBool();
    settings.pauseOnResize = qsettings.value("pauseOnResize", false).toBool();
    settings.wordWrapEnabled = qsettings.value("wordWrapEnabled", false).toBool();
    settings.displayMode =
        static_cast<DisplayMode>(qsettings.value("displayMode", static_cast<int>(DisplayMode::ASCII)).toInt());
    qsettings.endGroup();

    qsettings.beginGroup("Serial");
    settings.portName = qsettings.value("portName", "").toString();
    settings.baudRate = qsettings.value("baudRate", 115200).toInt();
    settings.dataBits = static_cast<QSerialPort::DataBits>(qsettings.value("dataBits", QSerialPort::Data8).toInt());
    settings.parity = static_cast<QSerialPort::Parity>(qsettings.value("parity", QSerialPort::NoParity).toInt());
    settings.stopBits = static_cast<QSerialPort::StopBits>(qsettings.value("stopBits", QSerialPort::OneStop).toInt());
    settings.sendNewLineMode = qsettings.value("newLineMode", 0).toInt();
    settings.serialEchoEnabled = qsettings.value("echoEnabled", false).toBool();
    qsettings.endGroup();

    qsettings.beginGroup("Logging");
    settings.logPath =
        qsettings.value("path", QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)).toString();
    settings.logFileNameFormat = qsettings.value("fileNameFormat", "log_%Y-%m-%d_%H-%M-%S.txt").toString();
    settings.logTimestampEnabled = qsettings.value("logTimestampEnabled", true).toBool();
    settings.logTimestampFormat = qsettings.value("logTimestampFormat", "[yyyy-MM-dd hh:mm:ss.zzz] ").toString();
    settings.logSplitEnabled = qsettings.value("splitEnabled", false).toBool();
    settings.logSplitSizeMB = qsettings.value("splitSizeMB", 10).toInt();
    settings.useDefaultLogViewer = qsettings.value("useDefaultLogViewer", true).toBool();
    settings.externalLogViewerPath = qsettings.value("externalLogViewerPath", "").toString();
    settings.autoEnableLogOnPortOpen = qsettings.value("autoEnableLogOnPortOpen", true).toBool();
    qsettings.endGroup();

    qsettings.beginGroup("Terminal");
    QFont font;
    font.fromString(qsettings.value("font", "Courier New,10,-1,5,50,0,0,0,0,0").toString());
    settings.terminalFont = font;
    qsettings.endGroup();

    // 加载快捷命令
    qsettings.beginGroup("QuickCommands");
    QString jsonString = qsettings.value("json").toString();
    if (!jsonString.isEmpty())
    {
        QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
        if (doc.isArray())
        {
            QJsonArray jsonArray = doc.array();
            settings.quickCommands.clear();
            for (const auto value : jsonArray)
            {
                if (value.isObject())
                {
                    QJsonObject obj = value.toObject();
                    QuickCommand cmd;
                    cmd.uuid = obj["uuid"].toString();
                    cmd.name = obj["name"].toString();
                    cmd.command = obj["command"].toString();
                    cmd.enabled = obj["enabled"].toBool();
                    QString formatStr = obj["format"].toString("ASCII");
                    cmd.format = (formatStr.toUpper() == "HEX") ? CommandFormat::HEX : CommandFormat::ASCII;
                    cmd.isCycle = obj["isCycle"].toBool();
                    cmd.cycleInterval = obj["cycleInterval"].toInt();
                    settings.quickCommands.append(cmd);
                }
            }
        }
    }
    qsettings.endGroup();

    return settings;
}

void TestSettingsPersistence::clearAllSettings()
{
    QSettings settings("LSDT", "SerialT");
    settings.clear();
}

#include "test_settings_persistence.moc"
QTEST_MAIN(TestSettingsPersistence)
