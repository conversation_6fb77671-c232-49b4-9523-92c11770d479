#include <QDebug>
#include <QSignalSpy>
#include <QThread>
#include <QTimer>
#include <QtTest/QtTest>

#include "../../src/SerialProcessor.h"

class TestDisconnectionHandling : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 串口断开处理测试
    void testConnectionMonitoringStart();
    void testConnectionMonitoringStop();
    void testConnectionHealthCheck();
    void testConnectionHealthSignals();
    void testResourceErrorHandling();
    void testConnectionStateConsistency();
    void testMonitoringAfterReconnection();

private:
    SerialProcessor *m_processor;
};

void TestDisconnectionHandling::initTestCase()
{
    qDebug() << "Starting disconnection handling tests";
}

void TestDisconnectionHandling::cleanupTestCase()
{
    qDebug() << "Disconnection handling tests completed";
}

void TestDisconnectionHandling::init()
{
    m_processor = new SerialProcessor(this);
}

void TestDisconnectionHandling::cleanup()
{
    if (m_processor)
    {
        if (m_processor->isOpen())
        {
            m_processor->closePort();
        }
        delete m_processor;
        m_processor = nullptr;
    }
}

void TestDisconnectionHandling::testConnectionMonitoringStart()
{
    // 测试连接监控的启动
    qDebug() << "Testing connection monitoring start";

    QSignalSpy healthSpy(m_processor, &SerialProcessor::connectionHealthChanged);
    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 初始状态应该是健康的（即使没有连接）
    QVERIFY(m_processor->isConnectionHealthy());

    // 尝试连接（会失败，但应该启动监控）
    QString testPort = "COM999"; // 不存在的端口
    bool result = m_processor->openPort(testPort, 9600);

    // 连接应该失败
    QVERIFY(!result);
    QVERIFY(!m_processor->isOpen());

    // 验证监控相关的方法可以正常调用
    m_processor->startConnectionMonitoring();
    m_processor->stopConnectionMonitoring();

    // 基础功能测试通过
    QVERIFY(true);
}

void TestDisconnectionHandling::testConnectionMonitoringStop()
{
    // 测试连接监控的停止
    qDebug() << "Testing connection monitoring stop";

    // 测试在未连接状态下停止监控
    m_processor->stopConnectionMonitoring();

    // 测试在连接状态下停止监控
    m_processor->startConnectionMonitoring();
    m_processor->stopConnectionMonitoring();

    // 验证关闭端口时会停止监控
    QString testPort = "COM999";
    m_processor->openPort(testPort, 9600); // 会失败，但不影响测试
    m_processor->closePort();

    // 基础功能测试通过
    QVERIFY(true);
}

void TestDisconnectionHandling::testConnectionHealthCheck()
{
    // 测试连接健康检查
    qDebug() << "Testing connection health check";

    QSignalSpy healthSpy(m_processor, &SerialProcessor::connectionHealthChanged);

    // 初始状态应该是健康的
    QVERIFY(m_processor->isConnectionHealthy());

    // 在未连接状态下，健康状态应该保持不变
    QVERIFY(m_processor->isConnectionHealthy());

    // 验证没有意外的信号发射
    QCOMPARE(healthSpy.count(), 0);
}

void TestDisconnectionHandling::testConnectionHealthSignals()
{
    // 测试连接健康状态信号
    qDebug() << "Testing connection health signals";

    QSignalSpy healthSpy(m_processor, &SerialProcessor::connectionHealthChanged);
    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);
    QSignalSpy errorSpy(m_processor, &SerialProcessor::errorOccurred);

    // 验证信号连接正确
    QVERIFY(healthSpy.isValid());
    QVERIFY(connectionSpy.isValid());
    QVERIFY(errorSpy.isValid());

    // 尝试连接到不存在的端口
    QString testPort = "COM999";
    bool result = m_processor->openPort(testPort, 9600);

    // 连接应该失败
    QVERIFY(!result);

    // 验证信号状态
    QCOMPARE(connectionSpy.count(), 0); // 连接失败，不应该有连接状态变化

    // 关闭端口（即使没有打开）
    m_processor->closePort();

    // 验证对象状态正常
    QVERIFY(m_processor != nullptr);
    QVERIFY(m_processor->parser() != nullptr);
}

void TestDisconnectionHandling::testResourceErrorHandling()
{
    // 测试资源错误处理
    qDebug() << "Testing resource error handling";

    QSignalSpy connectionLostSpy(m_processor, &SerialProcessor::connectionLost);
    QSignalSpy healthSpy(m_processor, &SerialProcessor::connectionHealthChanged);

    // 验证connectionLost信号连接正确
    QVERIFY(connectionLostSpy.isValid());

    // 在这个测试中，我们无法模拟真实的ResourceError
    // 但可以验证信号连接和基础功能
    QCOMPARE(connectionLostSpy.count(), 0);

    // 验证健康状态保持正常
    QVERIFY(m_processor->isConnectionHealthy());
}

void TestDisconnectionHandling::testConnectionStateConsistency()
{
    // 测试连接状态的一致性
    qDebug() << "Testing connection state consistency";

    QSignalSpy healthSpy(m_processor, &SerialProcessor::connectionHealthChanged);
    QSignalSpy connectionSpy(m_processor, &SerialProcessor::connectionStatusChanged);

    // 初始状态检查
    QVERIFY(!m_processor->isOpen());
    QVERIFY(m_processor->isConnectionHealthy());

    // 尝试多次连接和断开
    QString testPort = "COM999";
    for (int i = 0; i < 5; ++i)
    {
        bool result = m_processor->openPort(testPort, 9600);
        QVERIFY(!result); // 应该失败
        QVERIFY(!m_processor->isOpen());

        m_processor->closePort();
        QVERIFY(!m_processor->isOpen());

        // 健康状态应该保持一致
        QVERIFY(m_processor->isConnectionHealthy());
    }

    // 验证状态一致性
    QVERIFY(!m_processor->isOpen());
    QVERIFY(m_processor->isConnectionHealthy());
}

void TestDisconnectionHandling::testMonitoringAfterReconnection()
{
    // 测试重连后的监控状态
    qDebug() << "Testing monitoring after reconnection";

    QSignalSpy healthSpy(m_processor, &SerialProcessor::connectionHealthChanged);

    QString testPort = "COM999";

    // 第一次连接尝试
    bool result1 = m_processor->openPort(testPort, 9600);
    QVERIFY(!result1);

    // 关闭连接
    m_processor->closePort();

    // 第二次连接尝试
    bool result2 = m_processor->openPort(testPort, 9600);
    QVERIFY(!result2);

    // 验证健康状态
    QVERIFY(m_processor->isConnectionHealthy());

    // 最终清理
    m_processor->closePort();
    QVERIFY(!m_processor->isOpen());
    QVERIFY(m_processor->isConnectionHealthy());
}

QTEST_MAIN(TestDisconnectionHandling)
#include "test_disconnection_handling.moc"
