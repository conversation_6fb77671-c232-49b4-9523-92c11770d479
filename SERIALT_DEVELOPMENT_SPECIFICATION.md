# SerialT C++/Qt6/CMake 开发规范 (v2.0)

## 1. 宗旨与原则

本规范旨在为 SerialT 项目建立一套清晰、统一、可执行的开发标准。其核心目标是：
*   **提升代码质量**：通过标准化实践，减少 Bug，增强可读性和可维护性。
*   **保障系统健壮性**：聚焦内存安全、错误处理和资源管理，确保应用的稳定运行。
*   **提高开发效率**：提供明确的指导，减少不确定性，使开发者能专注于业务逻辑。

所有开发活动均需遵循**现代C++（C++20）**、**Qt6** 及 **CMake** 的最佳实践。

---

## 2. 编码风格与格式化

代码格式的统一性是协作的基础。本项目强制使用 `.clang-format` 进行自动化代码格式化，以消除个人风格差异。

*   **配置文件**: 项目根目录的 `.clang-format` 是唯一标准。
*   **核心风格**: 基于 `LLVM`，并进行了如下关键定制：
    *   **缩进**: 4个空格，禁止使用Tab。
    *   **行宽**: 120个字符。
    *   **大括号**: `Allman` 风格（大括号总是换行）。
    *   **指针/引用**: 与类型名右对齐 (例如 `int* p` 或 `const QString& s`)。
    *   **命名空间**: 不缩进 `namespace` 内的内容。
*   **头文件排序**: 为了保持逻辑清晰，头文件引用必须遵循以下顺序，由 `.clang-format` 自动管理：
    1.  当前实现文件对应的主头文件 (例如 `MainWindow.cpp` 中的 `MainWindow.h`)。
    2.  C++ 标准库头文件 (例如 `<vector>`, `<memory>`)。
    3.  Qt 核心库头文件 (例如 `<QApplication>`, `<QWidget>`)。
    4.  项目内部其他模块的头文件 (例如 `SerialProcessor.h`)。
*   **执行**: 开发者应配置其IDE（如 VSCode）在保存文件时自动运行 `clang-format`。

---

## 3. C++ 最佳实践 (C++20)

### 3.1. 命名规范

遵循 `.clang-tidy` 中定义的规则，以增强代码自解释性。

*   **类/结构体/枚举**: `UpperCamelCase` (例如 `MainWindow`, `SerialSettings`)。
*   **函数/方法**: `lowerCamelCase` (例如 `openPort`, `processData`)。
*   **变量/参数**: `lowerCamelCase` (例如 `baudRate`, `portName`)。
*   **私有成员变量**: 必须使用 `m_` 前缀，后跟 `lowerCamelCase` (例如 `m_serialPort`, `m_buffer`)。
*   **常量**: `k` 前缀，后跟 `UpperCamelCase` (例如 `kDefaultTimeout`, `kMaxBufferSize`)。
*   **枚举值**: `UpperCamelCase` (例如 `CrLf`, `Baud115200`)。
*   **宏定义**: `ALL_CAPS_WITH_UNDERSCORES` (例如 `SERIALT_VERSION`)，但应优先使用 `constexpr`。

### 3.2. `const` 正确性

*   **强制 `const`**: 任何不修改对象状态的成员函数都**必须**声明为 `const`。
*   **按值传递**: 对于 trivially-copyable 的类型（如 `int`, `double`, 指针）或小型对象，优先按值传递。对于可能被修改的输入参数，也应按值传递并结合 `std::move`。
*   **按 `const` 引用传递**: 对于大型对象（如 `QString`, `QByteArray`, `std::vector`）作为只读输入参数时，**必须**使用 `const` 引用传递，以避免不必要的拷贝开销。
*   **返回值**: 优先返回值，利用返回值优化（RVO）。对于大型对象，现代 C++ 的 RVO 使其同样高效。

### 3.3. 现代 C++ 特性

*   **智能指针**:
    *   **`std::unique_ptr`**: 作为对象所有权的**默认选择**。用于管理生命周期明确、所有权唯一的对象。
    *   **`std::shared_ptr`**: 仅在需要共享所有权时使用（例如，多个对象需要共同持有一个资源）。注意避免循环引用。
    *   **`std::weak_ptr`**: 用于打破 `std::shared_ptr` 的循环引用，或在不增加引用计数的情况下观察一个对象。
    *   **禁止裸 `new`/`delete`**: 在业务逻辑代码中，严禁直接使用 `new` 和 `delete`。应通过 `std::make_unique` 和 `std::make_shared` 创建对象。Qt 的父子对象树是此规则的一个重要例外（见 4.1）。
*   **`auto`**: 谨慎使用 `auto` 来简化复杂的类型名（如迭代器、lambda），但**不要**在简单或接口声明中使用它，以免降低代码清晰度。
*   **`nullptr`**: **必须**使用 `nullptr` 代替 `NULL` 或 `0`。
*   **`override` 和 `final`**:
    *   **`override`**: 派生类中重写基类的虚函数时，**必须**使用 `override` 关键字。
    *   **`final`**: 如果一个类不希望被继承，或一个虚函数不希望被再次重写，应使用 `final` 关键字。
*   **`enum class`**: **必须**使用强类型枚举 `enum class` 代替传统 `enum`，以避免命名空间污染和意外的整型转换。
*   **范围 `for` 循环**: 优先使用范围 `for` 循环处理容器遍历。
*   **结构化绑定 (C++17)**: 使用 `auto [var1, var2] = ...` 来简化对 `std::pair`, `std::tuple` 或结构体的访问。
*   **`using` 别名**: 使用 `using` 代替 `typedef` 来定义类型别名，语法更清晰、更通用。
*   **Lambda 表达式**: 广泛用于替代简单的本地函数或连接信号槽，使代码更紧凑。

### 3.4. 成员变量初始化规范

现代C++提供了多种成员变量初始化方式，正确选择初始化方式对代码的可读性、性能和安全性都有重要影响。

#### 3.4.1. 初始化方式优先级

**优先级1：类内初始化（推荐）**

对于以下类型的成员变量，**优先**使用类内初始化：

```cpp
class SerialProcessor {
private:
    // 基础数据类型
    int m_dataStallCount = 0;
    double m_connectionQuality = 1.0;
    bool m_autoReconnectEnabled = true;
    
    // 枚举类型
    ConnectionState m_connectionState = ConnectionState::Disconnected;
    QSerialPort::DataBits m_lastDataBits = QSerialPort::Data8;
    
    // 有明确默认值的配置参数
    int m_maxReconnectAttempts = App::Reconnect::kDefaultMaxReconnectAttempts;
    
    // 可以为空的指针
    QTimer* m_reconnectTimer = nullptr;
    
    // 容器类型（默认为空）
    QList<qint64> m_responseTimeHistory;
    QString m_lastPortName;
};
```

**优势**：
*   **DRY原则**：避免在多个构造函数中重复初始化代码。
*   **异常安全**：即使构造函数抛出异常，已初始化的成员也有确定的值。
*   **可读性**：成员变量的默认值一目了然。
*   **维护性**：修改默认值只需要在一个地方。

**优先级2：构造函数初始化列表（必要时）**

以下情况**必须**或**推荐**使用构造函数初始化列表：

```cpp
class SerialProcessor : public QObject {
private:
    const int m_maxRetries;              // const成员必须在初始化列表中初始化
    QSerialPort& m_serialPort;          // 引用成员必须在初始化列表中初始化
    QTimer* const m_timer;               // const指针必须在初始化列表中初始化
    
    // 需要动态计算的值
    int m_reconnectIntervalMs;
    int m_currentReconnectInterval;
    
    // 需要设置parent关系的Qt对象
    QTimer* m_statisticsTimer;
    
public:
    explicit SerialProcessor(int retries, QSerialPort& port, QObject* parent = nullptr)
        : QObject(parent)                                    // 基类初始化
        , m_maxRetries(retries)                             // const成员（必须）
        , m_serialPort(port)                                // 引用成员（必须）
        , m_timer(new QTimer(this))                         // const指针（必须）
        , m_reconnectIntervalMs(calculateInterval())        // 需要计算的值
        , m_currentReconnectInterval(m_reconnectIntervalMs) // 依赖其他成员
        , m_statisticsTimer(new QTimer(this))               // Qt对象设置parent
    {
        // 构造函数体保持简洁
        setupTimers();
        connectSignals();
    }
};
```

**优先级3：构造函数体内初始化（复杂逻辑）**

对于需要复杂逻辑的初始化，在构造函数体内进行：

```cpp
SerialProcessor::SerialProcessor(QObject* parent)
    : QObject(parent)
    , m_statisticsTimer(new QTimer(this))
{
    // 复杂的初始化逻辑
    setupTimers();
    connectSignals();
    initializeStateMachine();
}

private:
    void setupTimers() {
        m_statisticsTimer->setInterval(App::Connection::kDefaultStatisticsIntervalMs);
        m_statisticsTimer->setSingleShot(false);
        // ... 其他复杂设置
    }
    
    void connectSignals() {
        connect(m_statisticsTimer, &QTimer::timeout, this, &SerialProcessor::updateStatistics);
        // ... 其他信号连接
    }
```

#### 3.4.2. 混合使用策略

**推荐的混合使用模式**：

```cpp
class SerialProcessor : public QObject {
private:
    // 类内初始化：提供默认值
    bool m_autoReconnectEnabled = true;
    int m_maxReconnectAttempts = App::Reconnect::kDefaultMaxReconnectAttempts;
    double m_reconnectMultiplier = App::Reconnect::kDefaultReconnectIntervalMultiplier;
    
    // 构造函数初始化：需要参数或计算
    int m_reconnectIntervalMs;
    QTimer* m_statisticsTimer;
    
public:
    // 使用默认值的构造函数
    explicit SerialProcessor(QObject* parent = nullptr)
        : QObject(parent)
        , m_reconnectIntervalMs(App::Reconnect::kDefaultReconnectIntervalMs)
        , m_statisticsTimer(new QTimer(this))
    {
        setupConnections();
    }
    
    // 使用自定义参数的构造函数
    SerialProcessor(int maxAttempts, int intervalMs, QObject* parent = nullptr)
        : QObject(parent)
        , m_maxReconnectAttempts(maxAttempts)        // 覆盖类内默认值
        , m_reconnectIntervalMs(intervalMs)          // 使用参数值
        , m_statisticsTimer(new QTimer(this))
    {
        setupConnections();
    }
};
```

#### 3.4.3. 格式化规范

**构造函数初始化列表格式**：

```cpp
// 推荐的格式化风格
ClassName::ClassName(parameters)
    : BaseClass(args)                    // 基类初始化
    , m_member1(value1)                  // 每行一个成员
    , m_member2(value2)                  // 使用逗号前置风格
    , m_member3(value3)                  // 保持对齐
{
    // 构造函数体保持简洁
    setupConnections();
    initializeState();
}
```

**注意事项**：
*   初始化顺序必须与成员变量在类中的声明顺序一致。
*   使用逗号前置风格，便于添加和删除成员。
*   保持适当的缩进和对齐，提高可读性。

### 3.5. 常量管理与分类规范

为了提高代码的可维护性和模块化程度，SerialT项目采用按业务域分类的常量管理策略。

#### 3.5.1. 常量分类原则

**分类依据**：
*   **业务域分离**：按照功能模块和业务逻辑进行分类。
*   **职责单一**：每个常量文件只负责一个特定的业务域。
*   **依赖最小化**：减少不必要的编译依赖。
*   **扩展友好**：为未来的功能扩展预留架构空间。

**常量作用域规则**：
*   **私有常量**：在类内声明，仅供该类使用。
*   **模块内共享**：在模块内部头文件中定义。
*   **跨模块共享**：在 `src/core/` 下的专用常量文件中定义。
*   **应用级全局**：在对应的业务域常量文件中定义。

#### 3.5.2. 常量文件分类体系

SerialT项目的常量按以下业务域进行分类：

**核心业务域常量文件**：

1. **`src/core/AppInfo.h`** - 应用元信息
   ```cpp
   namespace App::Info {
       inline constexpr std::string_view kAppName = "SerialT"sv;
       inline constexpr std::string_view kAppVersion = "1.4.1"sv;
       inline constexpr std::string_view kOrgName = "LSDT"sv;
   }
   ```

2. **`src/core/SerialConstants.h`** - 串口通信相关
   ```cpp
   namespace App::Serial {
       inline constexpr int kDefaultBaudRate = 115200;
       inline const std::array<const char*, 12> kBaudRateList = {...};
       inline constexpr bool kDefaultSerialEchoEnabled = false;
   }
   ```

3. **`src/core/UIConstants.h`** - 用户界面相关
   ```cpp
   namespace App::UI {
       inline constexpr int kDefaultWindowWidth = 800;
       inline constexpr int kDefaultWindowHeight = 600;
       inline constexpr int kMinFontSize = 6;
   }
   ```

4. **`src/core/TerminalConstants.h`** - VT100终端相关
   ```cpp
   namespace App::Terminal {
       inline constexpr int kDefaultBufferSize = 5000;
       inline constexpr std::uint8_t kEscapeChar = 0x1B;
       inline constexpr int kSGRForegroundRed = 31;
   }
   ```

5. **`src/core/ConnectionConstants.h`** - 连接管理相关
   ```cpp
   namespace App::Connection {
       inline constexpr int kDefaultStatisticsIntervalMs = 1000;
       inline constexpr double kQualityThresholdExcellent = 0.9;
       inline constexpr int kMinMonitorIntervalMs = 500;
   }
   ```

6. **`src/core/ReconnectConstants.h`** - 重连策略相关
   ```cpp
   namespace App::Reconnect {
       inline constexpr bool kDefaultAutoReconnectEnabled = true;
       inline constexpr int kDefaultMaxReconnectAttempts = 5;
       inline constexpr double kDefaultReconnectIntervalMultiplier = 1.5;
   }
   ```

7. **`src/core/LoggingConstants.h`** - 日志系统相关
   ```cpp
   namespace App::Logging {
       inline constexpr std::string_view kDefaultLogFileNameFormat = "%P_%Y-%m-%d_%H-%M-%S.log"sv;
       inline constexpr bool kDefaultLogTimestampEnabled = true;
   }
   ```

8. **`src/core/SettingsConstants.h`** - 设置管理相关
   ```cpp
   namespace App::Settings {
       namespace Keys {
           inline constexpr std::string_view kThemeGroup = "Theme"sv;
           inline constexpr std::string_view kCurrentTheme = "currentTheme"sv;
       }
   }
   ```

9. **`src/core/CommonConstants.h`** - 通用工具常量
   ```cpp
   namespace App::Common {
       inline constexpr int kPercentageMultiplier = 100;
       inline constexpr int kBytesPerKilobyte = 1024;
   }
   ```

#### 3.5.3. 使用规范

**推荐的使用方式**：

```cpp
// 推荐：直接包含需要的常量文件
#include "core/TerminalConstants.h"
#include "core/ConnectionConstants.h"

class MyClass {
private:
    // 使用命名空间限定的常量
    int m_bufferSize = App::Terminal::kDefaultBufferSize;
    int m_interval = App::Connection::kDefaultStatisticsIntervalMs;
};
```

**避免的使用方式**：

```cpp
// 不推荐：包含过多的常量文件
#include "core/AppConstants.h"  // 包含所有常量，增加编译依赖

// 不推荐：使用using声明污染命名空间
using namespace App::Terminal;  // 可能导致命名冲突
```

#### 3.5.4. 扩展指南

**为新功能添加常量**：

1. **确定业务域**：新常量属于哪个业务域？
2. **选择文件**：是否有对应的常量文件？
3. **创建新文件**：如果没有，按照命名规范创建新文件。
4. **使用命名空间**：遵循 `App::ModuleName` 的命名规范。

**未来扩展预留**：

```cpp
// 为未来的功能模块预留常量文件
// src/core/ProtocolConstants.h - 协议解析工具
namespace App::Protocol {
    // 为未来的协议解析工具预留
}

// src/core/UtilityConstants.h - 小工具常量
namespace App::Utility {
    // 为未来的小工具预留
}

// src/core/PluginConstants.h - 插件系统
namespace App::Plugin {
    // 为未来的插件系统预留
}
```

---

## 4. Qt6 特定指南

### 4.1. QObject 生命周期与内存管理

*   **父子对象树**: 这是 Qt 的核心内存管理机制。当一个 `QObject` 被 `delete` 时，它的所有子对象也会被自动 `delete`。
*   **黄金法则**:
    *   所有 `QWidget` 派生类在创建时**必须**指定一个 `parent`。
    *   对于非 `QWidget` 的 `QObject`，如果其生命周期应与某个窗口或另一个 `QObject` 绑定，也应为其设置 `parent`。
    *   **不要**对已设置 `parent` 的 `QObject` 使用 `std::unique_ptr` 或 `std::shared_ptr` 管理，这会导致双重释放。
*   **`deleteLater()`**: 当需要在一个事件处理过程中（如槽函数内）安全地删除一个 `QObject` 时，**必须**使用 `deleteLater()`。它会将删除操作推迟到事件循环空闲时执行，避免了在对象自身方法栈上删除对象的问题。

### 4.2. 信号与槽

*   **首选语法**: **必须**使用 C++11 的函数指针语法连接信号和槽。它提供了编译期类型检查，更安全、更直观。

    ```cpp
    // 推荐
    connect(m_serialPort, &QSerialPort::readyRead, this, &MainWindow::onReadyRead);

    // 严禁
    // connect(m_serialPort, SIGNAL(readyRead()), this, SLOT(onReadyRead()));
    ```
*   **Lambda 表达式**: 对于简单的、一次性的连接，可以直接使用 Lambda 表达式作为槽，以增强代码的局部性。

    ```cpp
    connect(button, &QPushButton::clicked, this, [this]() {
        // ... 逻辑 ...
    });
    ```
*   **连接类型**: 默认使用 `Qt::AutoConnection`。只有在多线程场景下，需要精确控制时，才显式指定 `Qt::QueuedConnection` 或 `Qt::BlockingQueuedConnection`。

### 4.3. 容器与数据类型

*   **`QString` vs `std::string`**:
    *   与 Qt API 交互时（UI显示、文件路径等），**必须**使用 `QString`。
    *   在纯粹的、与 Qt 无关的 C++ 内部逻辑中，可以使用 `std::string`。如果需要，使用 `QString::toStdString()` 和 `QString::fromStdString()` 进行转换。
*   **容器**:
    *   优先使用 Qt 的隐式共享容器（`QVector`, `QList`, `QString`, `QByteArray` 等），它们在按值传递时开销很小。
    *   对于性能敏感、频繁修改的大型数据集，考虑使用 `std::vector`，因为它提供更可预测的性能。

### 4.4. 并发模型

*   **主线程 GUI**: 所有 `QWidget` 及其派生类只能在主线程中创建、访问和修改。
*   **工作线程**: 对于耗时操作（如数据解析、文件读写），**必须**将其移至工作线程，以避免冻结 UI。
    *   **推荐模式**: 创建一个继承自 `QObject` 的 `Worker` 类，使用 `QThread::moveToThread()` 将其移动到新线程。通过信号和槽与主线程进行安全通信。

---

## 5. 资源与内存管理

*   **RAII (Resource Acquisition Is Initialization)**: RAII 是 C++ 的核心思想，**必须**应用于所有资源管理，包括内存、文件、锁、数据库连接、串口等。
    *   **内存**: `std::unique_ptr`, `std::shared_ptr` 和 Qt 的父子对象树。
    *   **锁**: `std::lock_guard` 或 `std::unique_lock`，确保互斥锁在离开作用域时自动释放。
    *   **文件/串口**: 封装在类中，利用构造函数打开资源，析构函数关闭资源。`QSerialPort` 本身就是 RAII 的良好实践。

### 5.1. 动态内存分析 (ASan)

*   **目的**: 在开发和调试阶段，主动发现内存错误（越界、释放后使用、内存泄漏）。
*   **实施**:
    1.  提供一个 `DebugASan` CMake 构建类型。
    2.  开发者应定期（尤其是在合入重要功能前）使用此模式构建和运行程序，以捕获潜在的内存问题。
    3.  当 ASan 报告错误时，程序会立即终止并打印详细的调用栈，**必须**立即修复所报告的问题。

---

## 6. 错误处理与异常安全

*   **错误来源**:
    *   **Qt 调用**: 很多 Qt 函数通过返回值或特定的错误信号来报告失败（例如 `QSerialPort::errorOccurred`）。
    *   **C++ 库**: 可能抛出异常（例如 `std::bad_alloc`）。
    *   **逻辑错误**: 程序自身逻辑缺陷。
*   **处理策略**:
    *   **Qt 信号**: **必须**连接到对应的错误处理槽函数，并向用户提供有意义的反馈（例如通过状态栏、日志或对话框）。
    *   **异常**:
        *   我们的程序**不应**在顶层（如事件循环或槽函数）抛出未捕获的异常。
        *   在可能抛出异常的代码边界（如调用第三方库），使用 `try-catch` 块进行捕获。
        *   **异常安全**: 编写的代码应至少满足**基本异常安全**保证：即当异常发生时，对象状态有效，没有资源泄漏。对于关键数据结构，应努力实现**强异常安全**（提交或回滚）。

---

## 7. CMake 项目规范

### 7.1. 静态分析集成

*   **Clang-Tidy**: 为了在编码阶段即时发现问题，`clang-tidy` 将被集成到开发构建中。
    *   **强制执行**: 在 `CMakeLists.txt` 中配置，使得在 Debug 构建时自动调用 `clang-tidy`。
    *   **视警告为错误**: 配置 `-Werror` (或类似标志)，确保任何 `clang-tidy` 报告的问题都会导致构建失败，强制开发者修复。

### 7.2. 编译器警告

*   **最大化警告等级**: 所有构建都应开启最高级别的编译器警告。
    *   **MSVC**: `/W4`
    *   **Clang/GCC**: `-Wall -Wextra -Wpedantic`
*   **视警告为错误**: 在 Release 构建中，**必须**将警告视为错误 (`/WX` for MSVC, `-Werror` for Clang/GCC)，确保发布版本中没有潜在问题。

### 7.3. 结构与可维护性

*   **源文件管理**:
    *   推荐使用 `target_sources` 配合 `file(GLOB_RECURSE ...)` 来自动发现和添加源文件，避免手动维护冗长的文件列表。

    ```cmake
    # 在 qt_add_executable 之前
    file(GLOB_RECURSE SOURCES "src/*.h" "src/*.cpp" "resources/*.qrc")
    qt_add_executable(SerialT WIN32 MANUAL_FINALIZATION ${SOURCES})
    ```
*   **构建类型**: 标准化 `CMAKE_BUILD_TYPE` 的使用。
    *   `Debug`: 用于日常开发，包含调试信息，开启静态分析。
    *   `Release`: 用于最终发布，进行完全优化，开启 `/WX` 或 `-Werror`。
    *   `RelWithDebInfo`: 用于性能分析，进行优化但包含调试符号。
    *   `DebugASan`: 用于内存分析，开启 AddressSanitizer。
*   **模块化**: 鼓励使用 `target_link_libraries` 的 `PUBLIC`/`PRIVATE`/`INTERFACE` 关键字来精确控制依赖传递，尽管在当前单体应用中影响不大，但这是良好的工程习惯。

---

## 8. 文档与注释标准

### 8.1. 注释原则与格式

*   **目标**: 注释是为了解释 **"为什么"**，而不是 **"是什么"**。代码本身应清晰地表达"是什么"。
*   **语言**: 项目采用**中文注释**，提高团队协作效率和代码可读性。
*   **格式**: 采用 Doxygen 兼容的 Javadoc 风格，支持中文文档生成。

#### 8.1.1. 类和函数注释

```cpp
/**
 * @brief 处理来自串口的原始数据流
 *
 * 本函数根据VT100协议解析数据流，更新终端模型，并触发终端控件的重绘。
 * 该函数是高性能终端的核心处理逻辑，直接影响终端显示的实时性和准确性。
 *
 * @param data 从串口接收到的原始字节数组
 * @return 如果数据处理成功返回true，否则返回false
 * @note 此函数必须在SerialProcessor的线程中调用
 * @warning 传入的数据不能为空，否则会导致未定义行为
 * @see VT100Parser::parseSequence()
 */
auto HighPerformanceTerminal::processData(const QByteArray& data) -> bool;
```

#### 8.1.2. 常量注释规范

**常量文件头部注释**：

```cpp
/**
 * @file TerminalConstants.h
 * @brief 定义了VT100终端相关的所有常量，包括终端基础设置、VT100解析常量和缓冲区管理常量。
 *
 * 本文件包含了SerialT项目中VT100终端模拟器的核心常量定义，涵盖终端显示、
 * 字符解析、颜色控制和缓冲区管理等方面的配置参数。
 *
 * @note 这些常量是VT100终端功能的基础，修改时需要充分测试兼容性。
 * <AUTHOR>
 * @date 2024-01-15
 * @version 1.4.1
 */
```

**常量组注释**：

```cpp
// #############################################################################
// # VT100 SGR (Select Graphic Rendition) 颜色代码常量
// #############################################################################

/**
 * @brief SGR 禁用粗体命令代码
 *
 * 用于取消文本的粗体显示效果，对应VT100标准中的SGR 22命令。
 */
inline constexpr int kSGRBoldDisable = 22;

/**
 * @brief 前景色常量定义
 *
 * 定义了VT100标准的8种基本前景色，用于终端文本颜色控制。
 * 这些常量对应SGR 30-37命令序列。
 */
inline constexpr int kSGRForegroundBlack = 30;   ///< 前景色：黑色
inline constexpr int kSGRForegroundRed = 31;     ///< 前景色：红色
inline constexpr int kSGRForegroundGreen = 32;   ///< 前景色：绿色
```

**重要常量的详细注释**：

```cpp
/**
 * @brief 缓冲区溢出时数据保留比例
 *
 * 当终端缓冲区达到最大容量时，保留85%的历史数据，删除最旧的15%。
 * 这个比例在内存使用和历史数据保存之间提供了良好的平衡。
 *
 * @details 计算公式：保留行数 = 总行数 * kBufferOverflowRetentionRatio
 * @note 修改此值会影响内存使用模式和用户体验
 * @see HighPerformanceTerminal::trimBuffer()
 */
inline constexpr double kBufferOverflowRetentionRatio = 0.85;
```

#### 8.1.3. 成员变量注释

```cpp
class SerialProcessor : public QObject {
private:
    /**
     * @brief 连接质量评分 (范围: 0.0-1.0)
     *
     * 基于响应时间、数据传输成功率等因素计算的连接质量指标。
     * 1.0表示完美连接，0.0表示连接完全不可用。
     */
    double m_connectionQuality = App::Connection::kInitialConnectionQuality;
    
    /**
     * @brief 响应时间历史记录
     *
     * 存储最近的响应时间数据，用于连接质量评估和异常检测。
     * 列表大小会自动限制以避免内存无限增长。
     */
    QList<qint64> m_responseTimeHistory;
    
    /// 数据停滞计数器，用于检测连接异常
    int m_dataStallCount = 0;
};
```

### 8.2. 注释要求与规范

#### 8.2.1. 必须注释的内容

*   **所有公共和保护的类、结构体、枚举**都**必须**有完整的文档注释。
*   **所有公共和保护的方法**都**必须**有文档注释，包括参数、返回值和异常说明。
*   **所有常量文件**都**必须**有文件头注释和常量组注释。
*   **重要的私有成员变量**应该有简要注释说明其用途。
*   **复杂的算法和业务逻辑**必须有详细的行内注释。

#### 8.2.2. 推荐注释的内容

*   **性能敏感的代码块**：说明性能考虑和优化策略。
*   **线程安全相关的代码**：说明线程安全保证和注意事项。
*   **与外部系统交互的代码**：说明协议、格式和错误处理。
*   **配置和常量的选择依据**：解释为什么选择特定的数值或策略。

#### 8.2.3. 特殊注释标记

使用标准的Doxygen标记来标识特殊情况：

```cpp
/**
 * @brief 解析十六进制命令字符串
 *
 * @param command 待解析的十六进制字符串
 * @return 解析后的字节数组
 * @note 对于奇数长度的字符串，会在最后一个字符前补'0'
 * @warning 输入字符串必须只包含有效的十六进制字符
 * @deprecated 建议使用新的parseHexCommandV2函数
 * @todo 添加对Unicode十六进制字符的支持
 * @bug 在某些边界条件下可能产生错误的结果
 * @see parseHexCommandV2(), validateHexString()
 */
auto CommandParser::parseHexCommand(const QString& command) -> QByteArray;
```

#### 8.2.4. 行内注释规范

```cpp
void SerialProcessor::updateConnectionQuality()
{
    // 计算当前响应时间的移动平均值
    const double kCurrentAverage = calculateMovingAverage(m_responseTimeHistory);
    
    // 应用指数平滑算法更新质量评分
    // 使用较小的平滑因子以减少评分波动
    m_connectionQuality = m_connectionQuality * (1.0 - App::Connection::kQualitySmoothingFactor)
                        + newQualityScore * App::Connection::kQualitySmoothingFactor;
    
    // TODO: 考虑添加基于数据包丢失率的质量评估
    // FIXME: 在高频数据传输时评分更新可能过于频繁
}
```

### 8.3. 文档生成与维护

#### 8.3.1. Doxygen配置

项目应该配置Doxygen以支持：
*   **中文文档生成**：设置正确的字符编码和字体。
*   **代码示例高亮**：启用语法高亮显示。
*   **图表生成**：支持类关系图和调用图。
*   **搜索功能**：生成可搜索的HTML文档。

#### 8.3.2. 文档维护原则

*   **同步更新**：代码修改时必须同步更新相关注释。
*   **定期审查**：在代码审查过程中检查注释的准确性和完整性。
*   **版本标记**：重要的API变更应该在注释中标记版本信息。
*   **示例代码**：复杂的API应该提供使用示例。

---

## 9. 总结与实施指南

### 9.1. 规范实施优先级

**高优先级（立即实施）**：
1. **代码格式化**：配置并启用 `.clang-format` 自动格式化
2. **静态分析**：集成 `clang-tidy` 到构建流程
3. **命名规范**：确保所有新代码遵循命名约定
4. **成员变量初始化**：在新类中应用推荐的初始化策略

**中优先级（逐步实施）**：
5. **常量重构**：将现有常量迁移到新的分类体系
6. **文档注释**：为公共API添加完整的文档注释
7. **错误处理**：完善异常安全和错误处理机制

**低优先级（长期改进）**：
8. **代码重构**：逐步应用现代C++特性
9. **性能优化**：基于性能分析结果进行优化
10. **文档生成**：配置Doxygen生成项目文档

### 9.2. 团队协作建议

*   **代码审查**：所有代码变更都应经过同行审查
*   **规范培训**：定期组织团队学习和讨论规范内容
*   **工具配置**：统一开发环境和工具配置
*   **持续改进**：根据项目发展不断完善规范

### 9.3. 质量保证措施

*   **自动化检查**：通过CI/CD流程自动执行代码质量检查
*   **单元测试**：为关键功能编写全面的单元测试
*   **集成测试**：定期执行端到端的集成测试
*   **性能监控**：持续监控应用性能和资源使用

---

## 附录

### A. 常用工具配置

#### A.1. VSCode配置示例

```json
{
    "editor.formatOnSave": true,
    "cmake.generator": "Ninja",
    "cmake.outputLogEncoding": "gbk",
}
```

#### A.2. .clang-tidy配置要点

```yaml
Checks: >
  readability-*,
  modernize-*,
  performance-*,
  bugprone-*,
  clang-analyzer-*,
  cppcoreguidelines-*
```

### B. 参考资源

*   [C++ Core Guidelines](https://isocpp.github.io/CppCoreGuidelines/)
*   [Qt6 Documentation](https://doc.qt.io/qt-6/)
*   [Modern CMake](https://cliutils.gitlab.io/modern-cmake/)
*   [Effective Modern C++](https://www.oreilly.com/library/view/effective-modern-c/9781491908419/)

---

**版本**: 2.0
**最后更新**: 2025-08-04
**维护者**: GWF