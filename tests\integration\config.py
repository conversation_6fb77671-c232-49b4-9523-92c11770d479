"""
SerialT 集成测试配置文件
定义测试环境的串口配置和测试参数
"""

# 串口配置
SERIAL_CONFIG = {
    # SerialT应用使用的串口
    'SERIALT_PORT': 'COM30',
    
    # 测试工具使用的串口
    'TEST_TOOL_PORT': 'COM31',
    
    # 默认波特率
    'DEFAULT_BAUDRATE': 115200,
    
    # 支持的波特率列表（用于兼容性测试）
    'SUPPORTED_BAUDRATES': [9600, 19200, 38400, 57600, 115200, 230400, 460800],
    
    # 串口超时设置
    'TIMEOUT': 1.0,
    
    # 数据位、停止位、校验位
    'DATA_BITS': 8,
    'STOP_BITS': 1,
    'PARITY': 'N'  # N=None, E=Even, O=Odd
}

# 测试参数配置
TEST_CONFIG = {
    # 大数据量测试
    'LARGE_DATA_SIZE_KB': 1024,  # 1MB
    'CHUNK_SIZE_BYTES': 1024,    # 1KB chunks
    'CHUNK_DELAY_MS': 10,        # 10ms delay between chunks
    
    # 高频测试
    'HIGH_FREQ_PACKET_COUNT': 1000,
    'HIGH_FREQ_PACKET_SIZE': 64,
    'HIGH_FREQ_INTERVAL_MS': 10,
    
    # 长时间运行测试
    'LONG_RUN_DURATION_HOURS': 24,
    'LONG_RUN_CHECK_INTERVAL_MINUTES': 5,
    
    # VT100测试
    'VT100_TEST_DELAY_MS': 100,  # VT100序列间延迟
    
    # 性能基准
    'PERFORMANCE_BENCHMARK': {
        'MAX_MEMORY_GROWTH_MB_PER_HOUR': 10,
        'MAX_CPU_USAGE_PERCENT': 20,
        'MAX_DATA_TRANSFER_TIME_SECONDS': 10,  # 1MB数据传输时间
    }
}

# 测试数据路径
TEST_DATA_PATHS = {
    'BASE_DIR': 'tests/integration/data',
    'LARGE_TEXT_FILE': 'tests/integration/data/large_text.txt',
    'VT100_SAMPLES_FILE': 'tests/integration/data/vt100_samples.txt',
    'BINARY_DATA_FILE': 'tests/integration/data/binary_data.bin',
    'RESULTS_DIR': 'tests/integration/results',
}

# VT100控制序列定义
VT100_SEQUENCES = {
    # 光标控制
    'CURSOR_HOME': '\033[H',
    'CURSOR_UP': '\033[A',
    'CURSOR_DOWN': '\033[B',
    'CURSOR_RIGHT': '\033[C',
    'CURSOR_LEFT': '\033[D',
    'CURSOR_POSITION': lambda row, col: f'\033[{row};{col}H',
    
    # 屏幕控制
    'CLEAR_SCREEN': '\033[2J',
    'CLEAR_LINE': '\033[K',
    'CLEAR_TO_END': '\033[J',
    
    # 颜色控制
    'RESET': '\033[0m',
    'BOLD': '\033[1m',
    'UNDERLINE': '\033[4m',
    'REVERSE': '\033[7m',
    
    # 前景色
    'FG_BLACK': '\033[30m',
    'FG_RED': '\033[31m',
    'FG_GREEN': '\033[32m',
    'FG_YELLOW': '\033[33m',
    'FG_BLUE': '\033[34m',
    'FG_MAGENTA': '\033[35m',
    'FG_CYAN': '\033[36m',
    'FG_WHITE': '\033[37m',
    
    # 背景色
    'BG_BLACK': '\033[40m',
    'BG_RED': '\033[41m',
    'BG_GREEN': '\033[42m',
    'BG_YELLOW': '\033[43m',
    'BG_BLUE': '\033[44m',
    'BG_MAGENTA': '\033[45m',
    'BG_CYAN': '\033[46m',
    'BG_WHITE': '\033[47m',
}

# 测试场景定义
TEST_SCENARIOS = {
    'BASIC_COMMUNICATION': {
        'name': '基础通信测试',
        'description': '测试简单的文本收发功能',
        'duration_minutes': 5,
        'data_types': ['ascii_text', 'unicode_text', 'special_chars']
    },
    
    'LARGE_DATA_TRANSFER': {
        'name': '大数据量传输测试',
        'description': '测试大量数据的传输能力',
        'duration_minutes': 15,
        'data_size_mb': 10,
        'chunk_sizes': [64, 256, 1024, 4096]
    },
    
    'VT100_RENDERING': {
        'name': 'VT100渲染测试',
        'description': '测试VT100控制序列的渲染效果',
        'duration_minutes': 10,
        'sequences': ['colors', 'cursor_movement', 'screen_control', 'text_attributes']
    },
    
    'HIGH_FREQUENCY': {
        'name': '高频数据测试',
        'description': '测试高频率小数据包的处理能力',
        'duration_minutes': 10,
        'packet_rate_per_second': 100,
        'packet_size_bytes': 64
    },
    
    'STRESS_TEST': {
        'name': '压力测试',
        'description': '综合压力测试，包含各种数据类型',
        'duration_minutes': 30,
        'concurrent_streams': 3,
        'mixed_data_types': True
    },
    
    'LONG_RUNNING': {
        'name': '长时间运行测试',
        'description': '24小时连续运行稳定性测试',
        'duration_hours': 24,
        'check_interval_minutes': 30,
        'monitor_memory': True,
        'monitor_performance': True
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': 'tests/integration/results/test.log',
    'max_file_size_mb': 10,
    'backup_count': 5
}

def get_serial_config():
    """获取串口配置"""
    return SERIAL_CONFIG.copy()

def get_test_config():
    """获取测试配置"""
    return TEST_CONFIG.copy()

def get_vt100_sequences():
    """获取VT100序列定义"""
    return VT100_SEQUENCES.copy()

def get_test_scenario(scenario_name):
    """获取指定的测试场景配置"""
    return TEST_SCENARIOS.get(scenario_name, {})

def get_all_test_scenarios():
    """获取所有测试场景"""
    return TEST_SCENARIOS.copy()

if __name__ == '__main__':
    # 配置验证
    print("=== SerialT 集成测试配置 ===")
    print(f"SerialT端口: {SERIAL_CONFIG['SERIALT_PORT']}")
    print(f"测试工具端口: {SERIAL_CONFIG['TEST_TOOL_PORT']}")
    print(f"默认波特率: {SERIAL_CONFIG['DEFAULT_BAUDRATE']}")
    print(f"支持的波特率: {SERIAL_CONFIG['SUPPORTED_BAUDRATES']}")
    print(f"测试场景数量: {len(TEST_SCENARIOS)}")
    print("\n测试场景列表:")
    for name, config in TEST_SCENARIOS.items():
        print(f"  - {config['name']}: {config['description']}")
