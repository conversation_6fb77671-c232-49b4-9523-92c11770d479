#include "HighPerformanceTerminal.h"
#include "DataFormatter.h" // 为了使用数据格式化功能
#include "VT100Parser.h"   // 为了使用 TerminalCommand

#include <QApplication>
#include <QClipboard>
#include <QContextMenuEvent>
#include <QEvent>
#include <QFontmetrics>
#include <QMenu>
#include <QPaintEvent>
#include <QPainter>
#include <QResizeEvent>
#include <QScrollBar>
#include <QWheelEvent>
#include <QtMath>
#include <QtTypes>

#include <QDebug>

#include <algorithm>

auto HighPerformanceTerminal::createTerminalFontMetrics() -> QFontMetrics
{
    QFont terminalFont(kDefaultFontFamily, kDefaultFontSize);
    terminalFont.setFixedPitch(true);
    terminalFont.setHintingPreference(QFont::PreferFullHinting);
    terminalFont.setStyleStrategy(QFont::PreferAntialias);
    return QFontMetrics(terminalFont);
}

auto HighPerformanceTerminal::calculateCharDimensions(const QFontMetrics &fm) -> std::pair<int, int>
{
    return {fm.horizontalAdvance(' '), fm.height()};
}

HighPerformanceTerminal::HighPerformanceTerminal(QWidget *parent)
    : QWidget(parent),
      m_columns(kDefaultCols),
      m_rows(kDefaultRows),
      m_cursorX(0),
      m_cursorY(0),
      m_fontMetrics(createTerminalFontMetrics()),
      m_charWidth(calculateCharDimensions(m_fontMetrics).first),
      m_charHeight(calculateCharDimensions(m_fontMetrics).second),
      m_backgroundColor(Qt::black),
      m_foregroundColor(Qt::green),
      m_currentForegroundColor(Qt::green),
      m_currentBackgroundColor(Qt::black),
      m_currentBold(false),
      m_bufferSize(kDefaultBufferSize),
      m_firstLineIndex(0),
      m_totalLines(0),
      m_visibleTopLine(0),
      m_scrollBar(new QScrollBar(Qt::Vertical, this)),
      m_horizontalScrollBar(new QScrollBar(Qt::Horizontal, this)),
      m_scrollPolicy(SmartScroll),
      m_isScrolledByUser(false),
      m_scrollOnInputEnabled(false),
      m_wordWrapEnabled(true),           // 默认启用自动换行
      m_displayMode(DisplayMode::ASCII), // 默认使用ASCII模式
      m_newLineMode(kNewLineModeCrLf),   // 默认使用 \r\n
      m_isSelecting(false),
      m_searchFlags(FindFlag::NoFlags),
      m_currentSearchResultIndex(kInvalidSearchResultIndex),
      m_searchHighlightColor(kSearchHighlightColor),
      m_activeSearchHighlightColor(kActiveSearchHighlightColor)
{
    // 设置属性以优化绘制性能
    setAttribute(Qt::WA_OpaquePaintEvent);
    setAttribute(Qt::WA_NoSystemBackground);

    // 设置字体
    QFont terminalFont(kDefaultFontFamily, kDefaultFontSize);
    terminalFont.setFixedPitch(true);
    terminalFont.setHintingPreference(QFont::PreferFullHinting);
    terminalFont.setStyleStrategy(QFont::PreferAntialias);
    setFont(terminalFont);

    // 配置滚动条
    m_scrollBar->setVisible(true);
    m_scrollBar->setPageStep(m_rows);
    connect(m_scrollBar, &QScrollBar::valueChanged, this, [this](int value) {
        m_visibleTopLine = value;
        // 如果用户通过滚动条交互，则标记为用户滚动
        if (m_scrollBar->isSliderDown())
        {
            m_isScrolledByUser = true;
        }
        update();
    });
    connect(m_scrollBar, &QScrollBar::sliderReleased, this, [this]() {
        if (m_scrollBar->maximum() - m_scrollBar->value() <= kScrollBarProximityTolerance)
        { // 允许容差
            m_isScrolledByUser = false;
            scrollToBottom(); // 主动滚动一次，确保视觉上在底部
        }
    });

    m_horizontalScrollBar->setVisible(false);
    connect(m_horizontalScrollBar, &QScrollBar::valueChanged, this, [this](int /*value*/) {
        // The value parameter is the new position, we don't need it explicitly
        // because the paintEvent will read the scrollbar's value directly.
        // We just need to trigger a repaint.
        update();
    });

    // 预分配缓冲区
    m_buffer.resize(m_bufferSize);
    for (int i = 0; i < m_bufferSize; ++i)
    {
        m_buffer[i] = TerminalLine();
    }

    // 设置交点策略
    setFocusPolicy(Qt::StrongFocus);

    setStyleSheet(R"(
        QScrollBar:vertical {
            border: none;
            background: #202020;
            width: 10px;
            margin: 0px 0px 0px 0px;
        }
        QScrollBar::handle:vertical {
            background: #505050;
            min-height: 20px;
            border-radius: 5px;
        }
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            border: none;
            background: none;
        }
        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
            background: none;
        }
        QScrollBar:horizontal {
            border: none;
            background: #202020;
            height: 10px;
            max-height: 10px; /* 强制约束高度以解决样式覆盖问题 */
            margin: 0px 0px 0px 0px;
        }
        QScrollBar::handle:horizontal {
            background: #505050;
            min-width: 20px;
            border-radius: 5px;
        }
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
            border: none;
            background: none;
        }
        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
            background: none;
        }
    )");

    // 初始化自动滚动计时器
    m_scrollTimer = new QTimer(this);
    connect(m_scrollTimer, &QTimer::timeout, this, &HighPerformanceTerminal::autoScroll);
}

void HighPerformanceTerminal::setBufferSize(int lines)
{
    lines = std::max(lines, m_rows); // 确保至少能显示一屏

    if (lines == m_bufferSize)
    {
        return; // 大小没变，不需要调整
    }

    // 保存当前内容
    QVector<TerminalLine> oldBuffer = m_buffer;
    const int kOldTotalLines = m_totalLines;

    // 调整缓冲区大小
    m_bufferSize = lines;
    m_buffer.resize(m_bufferSize);

    // 初始化新的缓冲区
    for (int i = 0; i < m_bufferSize; ++i)
    {
        m_buffer[i] = TerminalLine();
    }

    // 复制旧内容到新缓冲区
    const int kLinesToCopy = std::min(kOldTotalLines, m_bufferSize);
    for (int i = 0; i < kLinesToCopy; ++i)
    {
        const qsizetype kOldIndex = (m_firstLineIndex + kOldTotalLines - kLinesToCopy + i) % oldBuffer.size();
        m_buffer[i] = oldBuffer[kOldIndex];
    }

    // 更新索引
    m_firstLineIndex = 0;
    m_totalLines = kLinesToCopy;

    // 重置光标位置到缓冲区末尾
    if (m_totalLines > 0)
    {
        m_cursorX = 0;
        m_cursorY = m_totalLines;
    }
    else
    {
        m_cursorX = 0;
        m_cursorY = 0;
    }

    // 更新滚动条
    updateScrollBar();
}

void HighPerformanceTerminal::setParser(VT100Parser *parser)
{
    m_parser = parser;
}

void HighPerformanceTerminal::setScrollPolicy(ScrollPolicy policy)
{
    m_scrollPolicy = policy;
}

void HighPerformanceTerminal::setNewLineMode(int mode)
{
    m_newLineMode = mode;
}

void HighPerformanceTerminal::setScrollOnInput(bool enabled)
{
    m_scrollOnInputEnabled = enabled;
}

void HighPerformanceTerminal::setWordWrapEnabled(bool enabled)
{
    if (m_wordWrapEnabled != enabled)
    {
        m_wordWrapEnabled = enabled;

        // 重置最大行宽度
        m_maxLineWidth = 0;

        // 当切换换行模式时，需要重新计算布局
        updateTerminalLayout();
        update();
    }
}

void HighPerformanceTerminal::scheduleUpdate()
{
    if (m_isRenderingSuspended)
    {
        return;
    }
    if (!m_updateScheduled)
    {
        m_updateScheduled = true;
        QTimer::singleShot(0, this, &HighPerformanceTerminal::processPendingCommands);
    }
}

void HighPerformanceTerminal::processPendingCommands()
{
    m_updateScheduled = false;

    if (m_parser == nullptr)
    {
        return;
    }

    const int kCommandsToProcessPerChunk = kCommandsPerChunk; // 增加每次处理的命令数，提高消费效率
    qsizetype processedCount = 0;

    // 使用循环来分块处理，直到所有命令都被处理完毕
    while (processedCount < kCommandsToProcessPerChunk)
    {
        const QList<TerminalCommand> kCommands = m_parser->takeCommands(kCommandsToProcessPerChunk);
        if (kCommands.isEmpty())
        {
            break; // 没有更多命令了，退出循环
        }

        for (const auto &cmd : kCommands)
        {
            switch (cmd.type)
            {
                case TerminalCommand::PrintableChar:
                    appendChar(cmd.params[0].toChar());
                    break;
                case TerminalCommand::ForegroundColorChanged:
                    m_currentForegroundColor = cmd.params[0].value<QColor>();
                    break;
                case TerminalCommand::BackgroundColorChanged:
                    m_currentBackgroundColor = cmd.params[0].value<QColor>();
                    break;
                case TerminalCommand::BoldChanged:
                    m_currentBold = cmd.params[0].toBool();
                    break;
                case TerminalCommand::AttributesReset:
                    resetAttributes();
                    break;
                case TerminalCommand::CursorUp:
                    m_cursorY = std::max(0, m_cursorY - cmd.params[0].toInt());
                    break;
                case TerminalCommand::CursorDown:
                    m_cursorY += cmd.params[0].toInt();
                    if (m_cursorY >= m_totalLines)
                    {
                        m_totalLines = m_cursorY + 1;
                    }
                    break;
                case TerminalCommand::CursorForward:
                    m_cursorX += cmd.params[0].toInt();
                    break;
                case TerminalCommand::CursorBack:
                    m_cursorX = std::max(0, m_cursorX - cmd.params[0].toInt());
                    break;
                case TerminalCommand::SetCursorPosition:
                    setCursorPosition(cmd.params[0].toInt(), cmd.params[1].toInt());
                    break;
                case TerminalCommand::EraseInDisplay:
                    eraseInDisplay(cmd.params[0].toInt());
                    break;
                case TerminalCommand::EraseInLine:
                    eraseInLine(cmd.params[0].toInt());
                    break;
                case TerminalCommand::Backspace:
                    backspace();
                    break;
                case TerminalCommand::HorizontalTab:
                    horizontalTab();
                    break;
                case TerminalCommand::Bell:
                    bell();
                    break;
                case TerminalCommand::FormFeed:
                    formFeed();
                    break;
                case TerminalCommand::VerticalTab:
                    verticalTab();
                    break;
            }
        }
        processedCount += kCommands.size();
    }

    // 处理所有命令，然后一次性更新滚动条和重绘。
    updateScrollBar();

    bool shouldScroll = false;
    // 优先处理独立的“输入后滚动”逻辑
    if (m_scrollOnInputEnabled && m_isUserInput)
    {
        shouldScroll = true;
    }
    else
    {
        // 否则，应用常规的滚动策略
        switch (m_scrollPolicy)
        {
            case SmartScroll:
                // 仅当用户没有手动滚动（即滚动条在最底部）时才滚动
                shouldScroll = !m_isScrolledByUser;
                break;
            case ScrollToBottom:
                // 始终滚动
                shouldScroll = true;
                break;
            case DisableAutoScroll:
                // 从不滚动
                shouldScroll = false;
                break;
        }
    }

    if (shouldScroll)
    {
        scrollToBottom();
    }

    // 在处理完所有命令后，重置用户输入标志
    m_isUserInput = false;

    // 如果当前有搜索词，则重新执行搜索以刷新高亮
    if (!m_searchTerm.isEmpty())
    {
        find(m_searchTerm, m_searchFlags);
    }

    update();

    // 如果解析器中仍有待处理的命令，则再次调度更新
    if (m_parser->hasPendingCommands())
    {
        scheduleUpdate();
    }
}

// NOLINTNEXTLINE(readability-function-cognitive-complexity)
void HighPerformanceTerminal::appendChar(const QChar &ch)
{
    if (ch == QChar::LineFeed) // '\n'
    {
        m_cursorX = 0;
        m_cursorY++;
    }
    else if (ch == QChar::CarriageReturn) // '\r'
    {
        m_cursorX = 0;
    }
    else // 普通字符
    {
        // 确保我们在有效行上
        if (m_cursorY >= m_totalLines)
        {
            m_totalLines = m_cursorY + 1;
        }

        // 处理缓冲区循环
        if (m_totalLines > m_bufferSize)
        {
            m_firstLineIndex = (m_firstLineIndex + 1) % m_bufferSize;
            m_totalLines = m_bufferSize;
            if (m_cursorY >= m_totalLines)
            {
                m_cursorY = m_totalLines - 1;
            }
            // 清除被循环使用的新行
            m_buffer[(m_firstLineIndex + m_cursorY) % m_bufferSize].reset();
        }

        const int kBufferActualStorageRow = (m_firstLineIndex + m_cursorY) % m_bufferSize;
        TerminalLine &currentLine = m_buffer[kBufferActualStorageRow];

        // 如果需要，扩展行以容纳新字符
        if (m_cursorX >= currentLine.size())
        {
            currentLine.resize(m_cursorX + 1, ' ', m_foregroundColor, m_backgroundColor, false);
        }

        currentLine.setChar(m_cursorX, ch, m_currentForegroundColor, m_currentBackgroundColor, m_currentBold);
        m_cursorX++;

        // 检查是否需要自动换行
        if (m_wordWrapEnabled)
        {
            // 计算当前可视区域能容纳的字符数
            const int kAvailableWidth = width() - m_scrollBar->width();
            const int kMaxCharsPerLine = std::max(1, kAvailableWidth / m_charWidth); // 确保至少为1

            // 如果当前光标位置超过了每行最大字符数，需要换行
            if (m_cursorX >= kMaxCharsPerLine)
            {
                // 自动换行到下一行
                m_cursorX = 0;
                m_cursorY++;
            }
        }
        else
        {
            // 非换行模式：计算当前行的像素宽度用于水平滚动
            // 注意：为了性能，我们只在添加字符时计算，而不是每次重绘
            // 这意味着如果字体改变，宽度可能不会立即更新，这是一个可以接受的权衡
            const QFontMetrics kFm(font());
            const int kLineWidth = kFm.horizontalAdvance(QString(currentLine.chars()));
            if (kLineWidth > m_maxLineWidth)
            {
                m_maxLineWidth = kLineWidth;
                updateHorizontalScrollBar();
            }
        }
    }

    // 确保总行数正确
    if (m_cursorY >= m_totalLines)
    {
        m_totalLines = m_cursorY + 1;
    }

    // 再次处理缓冲区循环
    if (m_totalLines > m_bufferSize)
    {
        m_firstLineIndex = (m_firstLineIndex + 1) % m_bufferSize;
        m_totalLines = m_bufferSize;

        // --- 坐标同步 ---
        m_cursorY--;
        m_selectionStart.setY(m_selectionStart.y() - 1);
        m_selectionEnd.setY(m_selectionEnd.y() - 1);

        // --- 修正无效坐标 ---
        m_cursorY = std::max(m_cursorY, 0);

        // 清理或修正无效选区
        if (m_selectionStart.y() < 0 && m_selectionEnd.y() < 0)
        {
            // 如果选区的两端都滚出了缓冲区，则清空选区
            m_selectionStart = m_selectionEnd;
        }
        else
        {
            // 如果只有一端滚出，则将其修正到缓冲区的开头
            if (m_selectionStart.y() < 0)
            {
                m_selectionStart.setY(0);
                m_selectionStart.setX(0);
            }
            if (m_selectionEnd.y() < 0)
            {
                m_selectionEnd.setY(0);
                m_selectionEnd.setX(0);
            }
        }

        // 更新并清理搜索结果
        for (auto &point : m_searchResults)
        {
            point.setY(point.y() - 1);
        }
        // NOLINTNEXTLINE(modernize-use-ranges)
        m_searchResults.erase(std::remove_if(m_searchResults.begin(), m_searchResults.end(),
                                             [](const QPoint &point) {
                                                 return point.y() < 0;
                                             }),
                              m_searchResults.end());

        // 更新当前搜索结果索引
        if (m_currentSearchResultIndex > 0)
        {
            m_currentSearchResultIndex--;
        }
        else if (m_currentSearchResultIndex == 0)
        {
            if (m_searchResults.empty())
            {
                m_currentSearchResultIndex = kInvalidSearchResultIndex;
            }
        }

        m_buffer[(m_firstLineIndex + m_cursorY) % m_bufferSize].reset();
    }
}

void HighPerformanceTerminal::clear()
{
    // 重置缓冲区状态
    m_totalLines = 0;
    m_firstLineIndex = 0;

    // 重置光标位置
    m_cursorX = 0;
    m_cursorY = 0;

    // 重置最大行宽
    m_maxLineWidth = 0;

    // 清空所有行的内容
    for (auto &i : m_buffer)
    {
        i.reset();
    }

    // 更新UI
    updateScrollBar();
    updateHorizontalScrollBar();

    // 清除搜索结果，但保留搜索词以便在有新数据时重新搜索
    m_searchResults.clear();
    m_currentSearchResultIndex = kInvalidSearchResultIndex;

    update(); // 立即重绘

    emit screenCleared();
}

void HighPerformanceTerminal::scrollToBottom()
{
    m_visibleTopLine = std::max(0, m_totalLines - m_rows);
    m_scrollBar->setValue(m_visibleTopLine);
    m_isScrolledByUser = false; // 任何自动滚动到底部的行为都应重置用户滚动状态
}

void HighPerformanceTerminal::updateScrollBar()
{
    const int kMaxRange = std::max(0, m_totalLines - m_rows);
    // 设置滚动条范围
    m_scrollBar->setRange(0, kMaxRange);
    m_scrollBar->setPageStep(m_rows);

    // 调整滚动条位置
    m_scrollBar->setGeometry(width() - m_scrollBar->sizeHint().width(), 0, m_scrollBar->sizeHint().width(), height());
}

auto HighPerformanceTerminal::visibleStartLine() const -> int
{
    return m_visibleTopLine;
}

void HighPerformanceTerminal::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    setupPainter(painter);

    const QRect &rect = event->rect();
    painter.fillRect(rect, m_backgroundColor);

    if (m_charHeight <= 0 || m_charWidth <= 0)
    {
        return;
    }

    const ViewportInfo kViewport = calculateViewport(rect);
    if (!kViewport.isValid())
    {
        return;
    }

    drawTextContent(painter, kViewport);
    drawCursor(painter, kViewport);
    drawSelection(painter, kViewport);
}

auto HighPerformanceTerminal::calculateViewport(const QRect &rect) const -> ViewportInfo
{
    ViewportInfo viewport;

    viewport.horizontalOffset = getHorizontalOffset();
    const int kViewportWidth = width() - (m_scrollBar->isVisible() ? m_scrollBar->width() : 0);
    const int kSafetyMargin = kViewportSafetyMargin;

    // 计算可见列范围
    viewport.startCol = m_wordWrapEnabled ? 0 : std::max(0, (viewport.horizontalOffset / m_charWidth) - kSafetyMargin);
    viewport.endCol = m_wordWrapEnabled
                          ? kUnlimitedColumns
                          : std::max(0, ((viewport.horizontalOffset + kViewportWidth) / m_charWidth) + kSafetyMargin);

    // 计算可见行范围
    viewport.startRow = rect.top() / m_charHeight;
    viewport.endRow = std::min(m_rows, qCeil(static_cast<qreal>(rect.bottom()) / m_charHeight));
    viewport.visibleStartLineAbs = visibleStartLine();

    return viewport;
}

void HighPerformanceTerminal::setupPainter(QPainter &painter)
{
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::TextAntialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

#ifdef Q_OS_WIN
    painter.setRenderHint(QPainter::VerticalSubpixelPositioning, true);
#endif
}

// NOLINTNEXTLINE(readability-function-cognitive-complexity)
void HighPerformanceTerminal::drawTextContent(QPainter &painter, const ViewportInfo &viewport) const
{
    for (int row = viewport.startRow; row < viewport.endRow; ++row)
    {
        const int kBufferRow = viewport.visibleStartLineAbs + row;
        if (kBufferRow >= m_totalLines)
        {
            continue;
        }

        const int kBufferIndex = (m_firstLineIndex + kBufferRow) % m_bufferSize;
        const auto &line = m_buffer[kBufferIndex];

        if (line.isEmpty())
        {
            continue;
        }

        // 分段绘制逻辑
        int segmentStartCol = 0;
        while (segmentStartCol < line.size())
        {
            // 确定当前段的颜色和高亮状态
            QColor segmentFgColor = line.fgColors()[segmentStartCol];
            QColor segmentBgColor = line.bgColors()[segmentStartCol];
            bool isHighlighted = false;

            // 检查搜索高亮
            for (int i = 0; i < m_searchResults.size(); ++i)
            {
                const QPoint &matchPos = m_searchResults[i];
                if (kBufferRow == matchPos.y() && segmentStartCol >= matchPos.x()
                    && segmentStartCol < matchPos.x() + m_searchTerm.length())
                {
                    segmentBgColor =
                        (i == m_currentSearchResultIndex) ? m_activeSearchHighlightColor : m_searchHighlightColor;
                    segmentFgColor = Qt::black;
                    isHighlighted = true;
                    break;
                }
            }

            // 找到当前同色/同高亮段的结束位置
            int segmentEndCol = segmentStartCol + 1;
            while (segmentEndCol < line.size())
            {
                bool nextIsHighlighted = false;
                QColor nextFgColor = line.fgColors()[segmentEndCol];
                QColor nextBgColor = line.bgColors()[segmentEndCol];

                for (int i = 0; i < m_searchResults.size(); ++i)
                {
                    const QPoint &matchPos = m_searchResults[i];
                    if (kBufferRow == matchPos.y() && segmentEndCol >= matchPos.x()
                        && segmentEndCol < matchPos.x() + m_searchTerm.length())
                    {
                        nextBgColor =
                            (i == m_currentSearchResultIndex) ? m_activeSearchHighlightColor : m_searchHighlightColor;
                        nextFgColor = Qt::black;
                        nextIsHighlighted = true;
                        break;
                    }
                }

                if (nextFgColor != segmentFgColor || nextBgColor != segmentBgColor
                    || nextIsHighlighted != isHighlighted)
                {
                    break;
                }
                segmentEndCol++;
            }

            const TextSegmentInfo kSegmentInfo{.row = row,
                                               .startCol = segmentStartCol,
                                               .endCol = segmentEndCol,
                                               .fgColor = segmentFgColor,
                                               .bgColor = segmentBgColor};
            drawTextSegment(painter, line, kSegmentInfo, viewport);
            segmentStartCol = segmentEndCol;
        }
    }
}

void HighPerformanceTerminal::drawTextSegment(QPainter &painter, const TerminalLine &line,
                                              const TextSegmentInfo &segmentInfo, const ViewportInfo &viewport) const
{
    // 视口剔除核心逻辑
    const int kIntersectStart = std::max(segmentInfo.startCol, viewport.startCol);
    const int kIntersectEnd = std::min(segmentInfo.endCol, viewport.endCol);

    if (kIntersectStart >= kIntersectEnd)
    {
        return;
    }

    // 精确计算绘制的X坐标
    const QString kPrefixText = QString(line.chars().mid(0, kIntersectStart));
    const int kStartPixelX = m_fontMetrics.horizontalAdvance(kPrefixText);

    const QString kTextToDraw = QString(line.chars().mid(kIntersectStart, kIntersectEnd - kIntersectStart));
    const int kTextWidth = m_fontMetrics.horizontalAdvance(kTextToDraw);

    const int kDrawX = kStartPixelX - viewport.horizontalOffset;
    const int kDrawY = segmentInfo.row * m_charHeight;

    // 绘制背景
    if (segmentInfo.bgColor != m_backgroundColor)
    {
        painter.fillRect(kDrawX, kDrawY, kTextWidth, m_charHeight, segmentInfo.bgColor);
    }

    // 绘制文本
    painter.setPen(segmentInfo.fgColor);
    painter.drawText(kDrawX, kDrawY + m_fontMetrics.ascent(), kTextToDraw);
}

void HighPerformanceTerminal::drawCursor(QPainter &painter, const ViewportInfo &viewport) const
{
    const int kCursorScreenY = m_cursorY - viewport.visibleStartLineAbs;
    if (kCursorScreenY < 0 || kCursorScreenY >= m_rows)
    {
        return;
    }

    int cursorPixelX = 0;
    if (m_cursorY < m_totalLines)
    {
        const int kBufferIndex = (m_firstLineIndex + m_cursorY) % m_bufferSize;
        const auto &line = m_buffer[kBufferIndex];
        if (m_cursorX <= line.size())
        {
            cursorPixelX = m_fontMetrics.horizontalAdvance(QString(line.chars().mid(0, m_cursorX)));
        }
    }

    const int kCursorDrawX = cursorPixelX - viewport.horizontalOffset;
    painter.fillRect(kCursorDrawX, kCursorScreenY * m_charHeight, m_charWidth, m_charHeight, m_foregroundColor);
}

void HighPerformanceTerminal::drawSelection(QPainter &painter, const ViewportInfo &viewport) const
{
    if (!m_isSelecting && m_selectionStart == m_selectionEnd)
    {
        return;
    }

    QPoint start = m_selectionStart;
    QPoint end = m_selectionEnd;

    if (start.y() > end.y() || (start.y() == end.y() && start.x() > end.x()))
    {
        qSwap(start, end);
    }

    painter.setCompositionMode(QPainter::CompositionMode_Difference);

    for (int row = start.y(); row <= end.y(); ++row)
    {
        if (row < viewport.visibleStartLineAbs || row >= viewport.visibleStartLineAbs + m_rows || row >= m_totalLines)
        {
            continue;
        }

        const int kScreenRow = row - viewport.visibleStartLineAbs;
        const int kBufferIndex = (m_firstLineIndex + row) % m_bufferSize;
        const auto &line = m_buffer[kBufferIndex];

        const int kSelStartCol = (row == start.y()) ? start.x() : 0;
        const int kSelEndCol = (row == end.y()) ? end.x() : line.size();

        if (kSelStartCol >= line.size())
        {
            continue;
        }

        const QString kStartText = QString(line.chars().mid(0, kSelStartCol));
        const QString kSelectionText = QString(line.chars().mid(kSelStartCol, kSelEndCol - kSelStartCol));

        const int kStartPixel = m_fontMetrics.horizontalAdvance(kStartText);
        const int kWidthPixel = m_fontMetrics.horizontalAdvance(kSelectionText);

        const int kSelectionDrawX = kStartPixel - viewport.horizontalOffset;
        painter.fillRect(kSelectionDrawX, kScreenRow * m_charHeight, kWidthPixel, m_charHeight, Qt::white);
    }
}

void HighPerformanceTerminal::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    updateTerminalLayout();
}

void HighPerformanceTerminal::wheelEvent(QWheelEvent *event)
{
    if ((event->modifiers() & Qt::ShiftModifier) != 0 && !m_wordWrapEnabled)
    {
        // Shift键被按下，执行水平滚动（仅在非自动换行模式下）
        const int kDelta = event->angleDelta().y(); // 我们将垂直滚动增量应用到水平滚动条
        m_horizontalScrollBar->setValue(m_horizontalScrollBar->value() - kDelta);
        event->accept();
    }
    else
    {
        // 默认行为：垂直滚动 (保留现有的实现)
        const int kNumDegress = event->angleDelta().y() / 8;
        const int kNumSteps = kNumDegress / 15;

        // 更新我们自己的状态
        int newTopLine = m_visibleTopLine - kNumSteps;
        newTopLine = std::max(0, std::min(newTopLine, m_totalLines - m_rows));

        if (newTopLine != m_visibleTopLine)
        {
            m_visibleTopLine = newTopLine;
            m_scrollBar->setValue(m_visibleTopLine); // 同步滚动条
            update();
        }

        // 如果用户手动滚动，则标记
        if (kNumSteps != 0)
        {
            m_isScrolledByUser = true;
        }

        // 如果滚动到了底部，则重置用户滚动标记，以便智能滚动可以恢复
        if (m_visibleTopLine == m_scrollBar->maximum())
        {
            m_isScrolledByUser = false;
        }

        event->accept();
    }
}

void HighPerformanceTerminal::setCursorPosition(int row, int col)
{
    // 根据诊断，正确处理逻辑坐标
    m_cursorY = std::max(0, row);
    m_cursorX = std::max(0, col);

    // 在无换行模式下，我们不根据 m_columns 限制光标的X坐标

    // 如果光标移动到尚不存在的行，则扩展总行数
    if (m_cursorY >= m_totalLines)
    {
        m_totalLines = m_cursorY + 1;
    }
    // 此处不调用 update()
}

void HighPerformanceTerminal::eraseInDisplay(int mode)
{
    switch (mode)
    {
        case 0: // 从光标擦除到屏幕末尾
            // 首先，从光标擦除到当前行尾
            eraseInLine(0);
            // 然后，擦除光标下方的所有行
            for (int rowIndex = m_cursorY + 1; rowIndex < m_totalLines; ++rowIndex)
            {
                const int kBufferRow = (m_firstLineIndex + rowIndex) % m_bufferSize;
                m_buffer[kBufferRow].reset();
            }
            break;
        case 1: // 从屏幕开头擦除到光标
            // 首先，从行首擦除到当前行的光标处
            eraseInLine(1);
            // 然后，擦除光标上方的所有行
            for (int rowIndex = 0; rowIndex < m_cursorY; ++rowIndex)
            {
                const int kBufferRow = (m_firstLineIndex + rowIndex) % m_bufferSize;
                m_buffer[kBufferRow].reset();
            }
            break;
        case 2: // 擦除整个屏幕
            clear();
            break;
        default:
            break;
    }
    // 此处不调用 update()
}

void HighPerformanceTerminal::eraseInLine(int mode)
{
    if (m_cursorY >= m_totalLines)
    {
        return;
    }

    const int kBufferRow = (m_firstLineIndex + m_cursorY) % m_bufferSize;
    if (kBufferRow < 0 || kBufferRow >= m_buffer.size())
    {
        return;
    }

    TerminalLine &line = m_buffer[kBufferRow];

    switch (mode)
    {
        case 0:
        { // 从光标擦除到行尾
            if (m_cursorX < line.size())
            {
                line.resize(m_cursorX);
            }
            break;
        }
        case 1:
        { // 从行首擦除到光标（含）
            if (m_cursorX < line.size())
            {
                for (int i = 0; i <= m_cursorX && i < line.size(); ++i)
                {
                    line.setChar(i, ' ', m_foregroundColor, m_backgroundColor, false);
                }
            }
            break;
        }
        case 2:
        { // 擦除整行
            line.reset();
            break;
        }
        default:
            break;
    }

    // 重新计算行宽并更新滚动条
    const QFontMetrics kFm(font());
    const int kLineWidth = kFm.horizontalAdvance(QString(line.chars()));
    // 注意：我们不在这里处理 m_maxLineWidth 的减小。
    // 这是一种简化，以避免遍历所有行来找到新的最大宽度。
    // 只有当添加更长的行时，m_maxLineWidth 才会增加。
    // clear() 函数是重置它的地方。
    m_maxLineWidth = std::max(m_maxLineWidth, kLineWidth);
    updateHorizontalScrollBar();
    // 此处不调用 update()
}

// 此函数现在是内部函数，不需要作为公共槽
void HighPerformanceTerminal::resetAttributes()
{
    m_currentForegroundColor = m_foregroundColor;
    m_currentBackgroundColor = m_backgroundColor;
    m_currentBold = false;
}

void HighPerformanceTerminal::keyPressEvent(QKeyEvent *event)
{
    QByteArray dataToSend;
    switch (event->key())
    {
        case Qt::Key_Up:
            dataToSend = "\x1B[A";
            break;
        case Qt::Key_Down:
            dataToSend = "\x1B[B";
            break;
        case Qt::Key_Right:
            dataToSend = "\x1B[C";
            break;
        case Qt::Key_Left:
            dataToSend = "\x1B[D";
            break;
        case Qt::Key_Home:
            dataToSend = "\x1B[H";
            break;
        case Qt::Key_End:
            dataToSend = "\x1B[F";
            break;
        case Qt::Key_Insert:
            dataToSend = "\x1B[2~";
            break;
        case Qt::Key_Delete:
            dataToSend = "\x1B[3~";
            break;
        case Qt::Key_PageUp:
            dataToSend = "\x1B[5~";
            break;
        case Qt::Key_PageDown:
            dataToSend = "\x1B[6~";
            break;
        case Qt::Key_F1:
            dataToSend = "\x1BOP";
            break;
        case Qt::Key_F2:
            dataToSend = "\x1BOQ";
            break;
        case Qt::Key_F3:
            dataToSend = "\x1BOR";
            break;
        case Qt::Key_F4:
            dataToSend = "\x1BOS";
            break;
        case Qt::Key_F5:
            dataToSend = "\x1B[15~";
            break;
        case Qt::Key_F6:
            dataToSend = "\x1B[17~";
            break;
        case Qt::Key_F7:
            dataToSend = "\x1B[18~";
            break;
        case Qt::Key_F8:
            dataToSend = "\x1B[19~";
            break;
        case Qt::Key_F9:
            dataToSend = "\x1B[20~";
            break;
        case Qt::Key_F10:
            dataToSend = "\x1B[21~";
            break;
        case Qt::Key_F11:
            dataToSend = "\x1B[23~";
            break;
        case Qt::Key_F12:
            dataToSend = "\x1B[24~";
            break;
        case Qt::Key_Return:
        case Qt::Key_Enter:
            switch (m_newLineMode)
            {
                case kNewLineModeCrLf:
                    dataToSend = "\r\n";
                    break;
                case kNewLineModeCr:
                    dataToSend = "\r";
                    break;
                case kNewLineModeLf:
                    dataToSend = "\n";
                    break;
                default:
                    dataToSend = "\r\n";
                    break; // Default case
            }
            break;
        default:
            dataToSend = event->text().toUtf8();
            break;
    }

    if (!dataToSend.isEmpty())
    {
        // 只要有用户输入（任何有效按键），就设置标志
        m_isUserInput = true;
        emit dataReadyToSend(dataToSend);
    }

    QWidget::keyPressEvent(event);
}

void HighPerformanceTerminal::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        const int kRow = (event->pos().y() / m_charHeight) + m_visibleTopLine;
        // const int col = event->pos().x() / m_charWidth;

        if (kRow >= m_totalLines)
        {
            // 如果点击位置超出了实际文本行数，则不进行任何选择操作
            return;
        }

        m_isSelecting = true;
        m_selectionStart = bufferPosition(event->pos());
        m_selectionEnd = m_selectionStart;
        update();
    }
}

void HighPerformanceTerminal::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isSelecting)
    {
        const int kRow = (event->pos().y() / m_charHeight) + m_visibleTopLine;

        if (kRow >= m_totalLines)
        {
            return;
        }
        m_selectionEnd = bufferPosition(event->pos());

        // 自动滚动逻辑
        const int kHotZone = kMouseScrollHotZone;
        if (event->pos().y() < kHotZone || event->pos().y() > height() - kHotZone)
        {
            m_scrollTimer->start(kMouseScrollIntervalMs);
        }
        else
        {
            m_scrollTimer->stop();
        }

        update();
    }
}

void HighPerformanceTerminal::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        m_isSelecting = false;
        m_scrollTimer->stop();
    }
}

auto HighPerformanceTerminal::bufferPosition(const QPoint &mousePos) const -> QPoint
{
    // 将像素x坐标转换回字符索引
    // 这是一个近似值，因为字符宽度不同
    const int kHorizontalOffset = getHorizontalOffset();
    const int kX = mousePos.x() + kHorizontalOffset;
    const int kRow = qBound(0, visibleStartLine() + (mousePos.y() / m_charHeight), m_totalLines - 1);

    // 找到最接近的字符列
    const int kBufferIndex = (m_firstLineIndex + kRow) % m_bufferSize;
    const auto &line = m_buffer[kBufferIndex];
    qsizetype col = 0;
    int minDiff = kX;
    const qsizetype kLineSize = line.size();
    for (qsizetype i = 0; i <= kLineSize; ++i)
    {
        const int kCharPos = m_fontMetrics.horizontalAdvance(QString(line.chars().mid(0, i)));
        if (qAbs(kCharPos - kX) < minDiff)
        {
            minDiff = qAbs(kCharPos - kX);
            col = i;
        }
    }

    return {static_cast<int>(col), kRow};
}

auto HighPerformanceTerminal::selectedText() const -> QString
{
    if (m_selectionStart == m_selectionEnd)
    {
        return {};
    }

    QString text;
    QPoint start = m_selectionStart;
    QPoint end = m_selectionEnd;

    if (start.y() > end.y() || (start.y() == end.y() && start.x() > end.x()))
    {
        qSwap(start, end);
    }

    for (int row = start.y(); row <= end.y(); ++row)
    {
        if (row >= m_totalLines)
        {
            continue;
        }

        const int kBufferIndex = (m_firstLineIndex + row) % m_bufferSize;
        const TerminalLine &line = m_buffer[kBufferIndex];

        const int kStartCol = (row == start.y()) ? start.x() : 0;
        const int kEndCol = (row == end.y()) ? end.x() : line.size();

        for (int col = kStartCol; col < kEndCol; ++col)
        {
            text += line.chars()[col];
        }

        if (row < end.y())
        {
            text += '\n';
        }
    }

    return text.trimmed();
}

void HighPerformanceTerminal::copy()
{
    const QString kText = selectedText();
    if (!kText.isEmpty())
    {
        QApplication::clipboard()->setText(kText);
    }
}

void HighPerformanceTerminal::paste()
{
    const QClipboard *clipboard = QApplication::clipboard();
    const QString kText = clipboard->text();
    if (!kText.isEmpty())
    {
        emit dataReadyToSend(kText.toUtf8());
    }
}

void HighPerformanceTerminal::selectAll()
{
    m_selectionStart = QPoint(0, 0);
    m_selectionEnd = QPoint(0, m_totalLines); // Select to the beginning of the last line
    if (m_totalLines > 0)
    {
        const int kLastLineIndex = (m_firstLineIndex + m_totalLines - 1) % m_bufferSize;
        m_selectionEnd.setX(m_buffer[kLastLineIndex].size());
    }
    update();
}

void HighPerformanceTerminal::setBaseFont(const QFont &baseFont)
{
    m_baseFont = baseFont;
    applyFont();
}

void HighPerformanceTerminal::applyFont()
{
    QFont newFont = m_baseFont;
    const int kNewSize = newFont.pointSize() + m_zoomLevel;
    if (kNewSize >= kMinFontSize) // 限制最小字体大小
    {
        newFont.setPointSize(kNewSize);
    }
    newFont.setFixedPitch(true);

    // 字体渲染优化设置
    newFont.setHintingPreference(QFont::PreferFullHinting); // 启用完整字体提示
    newFont.setStyleStrategy(QFont::PreferAntialias);       // 优先使用抗锯齿

#ifdef Q_OS_WIN
    // Windows上启用ClearType支持
    // The OR'ing of StyleStrategy flags is not valid as it's a regular enum, not a Q_FLAG.
    // This was caught by clang-tidy. We choose one strategy to fix the build.
    // PreferAntialias is generally desirable for text rendering.
    // The OR'ing of StyleStrategy flags is not valid as it's a regular enum, not a Q_FLAG.
    // This was caught by clang-tidy. We choose one strategy to fix the build.
    // PreferAntialias is generally desirable for text rendering.
    newFont.setStyleStrategy(QFont::PreferAntialias);
#endif

    setFont(newFont);
    m_fontMetrics = QFontMetrics(newFont);
    m_charWidth = m_fontMetrics.horizontalAdvance(' ');
    m_charHeight = m_fontMetrics.height();

    // 强制重新计算尺寸和布局
    QResizeEvent event(size(), size());
    resizeEvent(&event);
    update();
}

void HighPerformanceTerminal::zoomIn()
{
    m_zoomLevel++;
    applyFont();
}

void HighPerformanceTerminal::zoomOut()
{
    m_zoomLevel--;
    applyFont();
}

void HighPerformanceTerminal::zoomReset()
{
    m_zoomLevel = kDefaultZoomLevel;
    applyFont();
}

void HighPerformanceTerminal::suspendRendering(bool suspended)
{
    if (m_isRenderingSuspended == suspended)
    {
        return;
    }
    m_isRenderingSuspended = suspended;

    if (!suspended)
    {
        // 当渲染恢复时，立即安排一次更新以显示暂停期间累积的更改。
        // 我们调用 scheduleUpdate 而不是直接 update()，以确保它遵循现有的命令处理流程。
        scheduleUpdate();
    }
}

void HighPerformanceTerminal::setDisplayMode(DisplayMode mode)
{
    if (m_displayMode == mode)
    {
        return; // 模式没有变化，无需处理
    }

    m_displayMode = mode;

    // 模式切换后需要重新渲染显示内容
    update();
}

void HighPerformanceTerminal::appendRawData(const QByteArray &rawData)
{
    if (rawData.isEmpty())
    {
        return;
    }

    // 根据显示模式格式化数据
    const QString kFormattedText = DataFormatter::formatData(rawData, m_displayMode);

    // 将格式化后的文本逐字符添加到终端缓冲区
    for (const QChar &ch : kFormattedText)
    {
        appendChar(ch);
    }

    // 更新滚动条
    updateScrollBar();

    // 实现自动滚动逻辑（与processPendingCommands中的逻辑保持一致）
    bool shouldScroll = false;
    // 优先处理独立的"输入后滚动"逻辑
    if (m_scrollOnInputEnabled && m_isUserInput)
    {
        shouldScroll = true;
    }
    else
    {
        // 否则，应用常规的滚动策略
        switch (m_scrollPolicy)
        {
            case SmartScroll:
                // 仅当用户没有手动滚动（即滚动条在最底部）时才滚动
                shouldScroll = !m_isScrolledByUser;
                break;
            case ScrollToBottom:
                // 始终滚动
                shouldScroll = true;
                break;
            case DisableAutoScroll:
                // 从不滚动
                shouldScroll = false;
                break;
        }
    }

    if (shouldScroll)
    {
        scrollToBottom();
    }

    // 在处理完所有数据后，重置用户输入标志
    m_isUserInput = false;

    // 如果当前有搜索词，则重新执行搜索以刷新高亮
    if (!m_searchTerm.isEmpty())
    {
        find(m_searchTerm, m_searchFlags);
    }

    update();
}

void HighPerformanceTerminal::contextMenuEvent(QContextMenuEvent *event)
{
    QMenu menu(this);
    QAction *copyAction = menu.addAction("复制");
    connect(copyAction, &QAction::triggered, this, &HighPerformanceTerminal::copy);
    copyAction->setEnabled(!selectedText().isEmpty());
    menu.exec(event->globalPos());
}

void HighPerformanceTerminal::updateHorizontalScrollBar()
{
    updateTerminalLayout();
}

void HighPerformanceTerminal::reLayoutContent()
{
    // TODO: 实现智能重新布局功能
    // 功能需求：
    // 1. 模式切换时保持现有内容并重新布局
    // 2. 窗口缩放时重新计算现有内容的换行
    //
    // 当前采用简单的清空策略以确保稳定性
    // 未来版本可以实现完整的内容重新布局算法

    if (!m_wordWrapEnabled || m_isReLayouting)
    {
        return;
    }

    m_isReLayouting = true;

    // 当前策略：清空缓冲区
    // TODO: 替换为智能重新布局算法
    clear();

    m_isReLayouting = false;
}

auto HighPerformanceTerminal::getHorizontalOffset() const -> int
{
    // 在自动换行模式下，不使用水平偏移
    return m_wordWrapEnabled ? 0 : m_horizontalScrollBar->value();
}

void HighPerformanceTerminal::updateTerminalLayout()
{
    // First, determine if the horizontal scrollbar should be visible.
    const int kViewportWidth = width() - m_scrollBar->width();

    // TODO: 启用窗口缩放时的自动重新布局功能
    // 当窗口宽度变化时，应该重新布局现有内容以适应新的宽度
    // 当前禁用此功能以确保稳定性，未来版本可以启用
    /*
    if (m_wordWrapEnabled && viewportWidth != m_lastLayoutWidth && m_lastLayoutWidth > 0)
    {
        reLayoutContent();
    }
    */
    m_lastLayoutWidth = kViewportWidth;

    // 在自动换行模式下，禁用水平滚动条
    const bool kHScrollShouldBeVisible = !m_wordWrapEnabled && (m_maxLineWidth > kViewportWidth);
    m_horizontalScrollBar->setVisible(kHScrollShouldBeVisible);

    // Now, calculate the available height for the text area.
    int availableHeight = height();
    if (kHScrollShouldBeVisible)
    {
        availableHeight -= m_horizontalScrollBar->height();
    }

    // Recalculate the number of rows that can be displayed.
    if (m_charHeight > 0)
    {
        m_rows = availableHeight / m_charHeight;
    }
    else
    {
        m_rows = 0;
    }
    m_rows = std::max(1, m_rows); // Must have at least one row.

    // Update the geometry of the scrollbars.
    m_scrollBar->setGeometry(width() - m_scrollBar->width(), 0, m_scrollBar->width(), availableHeight);
    m_horizontalScrollBar->setGeometry(0, height() - m_horizontalScrollBar->height(), width() - m_scrollBar->width(),
                                       m_horizontalScrollBar->height());

    // Update the range of the scrollbars.
    updateScrollBar(); // This updates the vertical scrollbar's range based on m_totalLines and new m_rows.

    if (kHScrollShouldBeVisible)
    {
        m_horizontalScrollBar->setPageStep(kViewportWidth);
        m_horizontalScrollBar->setRange(0, m_maxLineWidth - kViewportWidth);
    }
    else
    {
        m_horizontalScrollBar->setRange(0, 0);
        m_horizontalScrollBar->setValue(0);
    }

    // After a layout change, we might need to adjust the visible top line.
    if (m_scrollPolicy == ScrollToBottom || (m_scrollPolicy == SmartScroll && !m_isScrolledByUser))
    {
        scrollToBottom();
    }
    else
    {
        // Ensure the current top line is not out of bounds.
        m_visibleTopLine = std::min(m_visibleTopLine, std::max(0, m_totalLines - m_rows));
        m_scrollBar->setValue(m_visibleTopLine);
    }

    update(); // Schedule a repaint with the new layout.
}

void HighPerformanceTerminal::backspace()
{
    if (m_cursorX > 0)
    {
        m_cursorX--;
    }
}

void HighPerformanceTerminal::horizontalTab()
{
    m_cursorX = (m_cursorX / kDefaultTabWidth + 1) * kDefaultTabWidth;
}

void HighPerformanceTerminal::bell()
{
    QApplication::beep();
}

void HighPerformanceTerminal::formFeed()
{
    // 现代终端通常忽略FormFeed字符或将其视为换行
    // 这里我们将其视为换行，这比清屏更合理
    appendChar('\n');
}

void HighPerformanceTerminal::verticalTab()
{
    m_cursorY++;
    // 确保总行数被更新，并处理缓冲区循环
    if (m_cursorY >= m_totalLines)
    {
        m_totalLines = m_cursorY + 1;
    }
    if (m_totalLines > m_bufferSize)
    {
        m_firstLineIndex = (m_firstLineIndex + 1) % m_bufferSize;
        m_totalLines = m_bufferSize;
        m_cursorY = m_totalLines - 1;
        m_buffer[(m_firstLineIndex + m_cursorY) % m_bufferSize].reset();
    }
}

void HighPerformanceTerminal::autoScroll()
{
    const QPoint kCursorPos = mapFromGlobal(QCursor::pos());
    int scrollAmount = 0;
    const int kHotZone = kMouseScrollHotZone;

    if (kCursorPos.y() < kHotZone)
    {
        scrollAmount = -1; // 向上滚动
    }
    else if (kCursorPos.y() > height() - kHotZone)
    {
        scrollAmount = 1; // 向下滚动
    }

    if (scrollAmount != 0)
    {
        m_scrollBar->setValue(m_scrollBar->value() + scrollAmount);
        // 关键：在滚动后，需要更新选择的终点
        m_selectionEnd = bufferPosition(kCursorPos);
        update(); // 触发重绘
    }
}

// NOLINTNEXTLINE(readability-function-cognitive-complexity)
void HighPerformanceTerminal::find(const QString &term, FindFlags flags)
{
    // 保存之前的索引，以便在内容更新后尝试恢复
    const int kPreviousIndex = m_currentSearchResultIndex;

    m_searchTerm = term;
    m_searchFlags = flags;
    m_searchResults.clear();
    m_currentSearchResultIndex = kInvalidSearchResultIndex; // 先重置

    if (term.isEmpty())
    {
        emit searchResultsUpdated(0, kInvalidSearchResultIndex);
        update();
        return;
    }

    const Qt::CaseSensitivity kCs =
        m_searchFlags.testFlag(FindFlag::CaseSensitive) ? Qt::CaseSensitive : Qt::CaseInsensitive;

    for (int row = 0; row < m_totalLines; ++row)
    {
        const int kBufferIndex = (m_firstLineIndex + row) % m_bufferSize;
        const auto &line = m_buffer[kBufferIndex];
        const QString kLineText(line.chars());

        qsizetype index = -1;
        qsizetype searchFrom = 0;
        while ((index = kLineText.indexOf(m_searchTerm, searchFrom, kCs)) != -1)
        {
            bool isMatch = true;
            if (m_searchFlags.testFlag(FindFlag::WholeWord))
            {
                // 检查前面
                if (index > 0 && kLineText.at(index - 1).isLetterOrNumber())
                {
                    isMatch = false;
                }
                // 检查后面
                if (isMatch && (index + m_searchTerm.length() < kLineText.length())
                    && kLineText.at(index + m_searchTerm.length()).isLetterOrNumber())
                {
                    isMatch = false;
                }
            }

            if (isMatch)
            {
                m_searchResults.append(QPoint(static_cast<int>(index), row));
            }
            searchFrom = index + 1; // 从下一个位置开始，以防万一
        }
    }

    if (!m_searchResults.isEmpty())
    {
        // 尝试恢复之前的索引
        if (kPreviousIndex >= 0 && kPreviousIndex < m_searchResults.size())
        {
            m_currentSearchResultIndex = kPreviousIndex;
        }
        else
        {
            // 如果无法恢复，则默认高亮第一个
            m_currentSearchResultIndex = 0;
        }
    }

    emit searchResultsUpdated(static_cast<int>(m_searchResults.size()), m_currentSearchResultIndex);
    update();
}

void HighPerformanceTerminal::findNext()
{
    if (m_searchResults.isEmpty())
    {
        return;
    }

    m_currentSearchResultIndex = (m_currentSearchResultIndex + 1) % static_cast<int>(m_searchResults.size());

    emit searchResultsUpdated(static_cast<int>(m_searchResults.size()), m_currentSearchResultIndex);

    ensureSearchResultVisible();

    update();
}

void HighPerformanceTerminal::findPrevious()
{
    if (m_searchResults.isEmpty())
    {
        return;
    }

    m_currentSearchResultIndex--;
    if (m_currentSearchResultIndex < 0)
    {
        m_currentSearchResultIndex = static_cast<int>(m_searchResults.size()) - 1;
    }

    emit searchResultsUpdated(static_cast<int>(m_searchResults.size()), m_currentSearchResultIndex);

    ensureSearchResultVisible();

    update();
}

void HighPerformanceTerminal::ensureSearchResultVisible()
{
    if (m_currentSearchResultIndex < 0 || m_currentSearchResultIndex >= m_searchResults.size())
    {
        return;
    }

    const QPoint &matchPos = m_searchResults[m_currentSearchResultIndex];
    const int kTargetLine = matchPos.y();

    // --- 垂直滚动 ---
    // 检查匹配行是否在当前可见区域之外
    if (kTargetLine < m_visibleTopLine || kTargetLine >= m_visibleTopLine + m_rows)
    {
        // 将其滚动到视图的中间位置
        m_visibleTopLine = std::max(0, kTargetLine - (m_rows / 2));
        m_scrollBar->setValue(m_visibleTopLine);
    }

    // --- 水平滚动 ---
    // 在自动换行模式下，不需要水平滚动
    if (!m_wordWrapEnabled)
    {
        const int kBufferIndex = (m_firstLineIndex + kTargetLine) % m_bufferSize;
        const auto &line = m_buffer[kBufferIndex];

        // 计算匹配项的起始和结束像素位置
        const QString kMatchText = QString(line.chars().mid(0, matchPos.x()));
        const int kMatchStartPixel = m_fontMetrics.horizontalAdvance(kMatchText);
        const int kMatchEndPixel = m_fontMetrics.horizontalAdvance(kMatchText + m_searchTerm);

        // 获取视口的可见区域
        const int kViewportStartPixel = m_horizontalScrollBar->value();
        const int kViewportWidth = m_horizontalScrollBar->pageStep();
        const int kViewportEndPixel = kViewportStartPixel + kViewportWidth;

        // 检查匹配项是否完全在视口内
        if (kMatchStartPixel < kViewportStartPixel)
        {
            // 如果匹配项在左侧不可见，则向左滚动
            m_horizontalScrollBar->setValue(kMatchStartPixel);
        }
        else if (kMatchEndPixel > kViewportEndPixel)
        {
            // 如果匹配项在右侧不可见，则向右滚动
            m_horizontalScrollBar->setValue(kMatchEndPixel - kViewportWidth);
        }
    }

    // 不需要在这里调用 update()，因为调用者 (findNext/findPrevious) 会调用
}

auto HighPerformanceTerminal::fontMetrics() const -> QFontMetrics
{
    return m_fontMetrics;
}
