#include "CommandParser.h"
#include "SettingsDialog.h"

#include <QByteArray>
#include <QChar>
#include <QRegularExpression>
#include <QString>
#include <QStringBuilder>

auto CommandParser::parse(const QuickCommand &command) -> QByteArray
{
    switch (command.format)
    {
        case CommandFormat::HEX:
            return parseHexCommand(command.command);
        case CommandFormat::ASCII:
            return parseAsciiCommand(command.command);
    }
    // Should not be reached if all formats are handled, but ensures a return path.
    return {};
}

auto CommandParser::parseHexCommand(const QString &command) -> QByteArray
{
    // 移除所有非十六进制字符，然后进行转换
    QString hexString = command;
    hexString.remove(QRegularExpression(R"([^0-9A-Fa-f])"));

    // 如果是奇数长度，只在最后一个数字前补0
    if (hexString.length() % 2 != 0)
    {
        const QChar kLastChar = hexString.back();
        hexString.chop(1);
        hexString.append('0');
        hexString.append(kLastChar);
    }

    return QByteArray::fromHex(hexString.toLatin1());
}

auto CommandParser::parseAsciiCommand(const QString &command) -> QByteArray
{
    QByteArray result;
    for (int i = 0; i < command.length(); ++i)
    {
        if (command[i] == '\\' && i + 1 < command.length())
        {
            // Move to the character after '\'
            i++;
            const QChar kControlChar = command[i];

            if (kControlChar == 'r')
            {
                result.append('\r');
            }
            else if (kControlChar == 'n')
            {
                result.append('\n');
            }
            else if (kControlChar == 't')
            {
                result.append('\t');
            }
            else if (kControlChar == '\\')
            {
                result.append('\\');
            }
            else if (kControlChar == 'x')
            {
                // Expect two hex characters following '\x'
                if (i + 2 < command.length())
                {
                    bool ok = false;
                    const QString kHexCode = command.mid(i + 1, 2);
                    const char kByte = static_cast<char>(kHexCode.toUShort(&ok, kHexBase));
                    if (ok)
                    {
                        result.append(kByte);
                        i += 2; // Skip the two hex characters
                    }
                    else
                    {
                        // If conversion fails, append the literal "\x"
                        result.append("\\x");
                    }
                }
                else
                {
                    // Not enough characters for a hex code, append literal "\x"
                    result.append("\\x");
                }
            }
            else
            {
                // Unrecognized escape sequence, append literally
                result.append('\\').append(kControlChar.toLatin1());
            }
        }
        else
        {
            result.append(command[i].toLatin1());
        }
    }
    return result;
}
