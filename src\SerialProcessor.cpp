#include "SerialProcessor.h"
#include "VT100Parser.h"
#include "core/AppTypes.h"
#include "core/CommonConstants.h"
#include "core/ConnectionConstants.h"

#include <QElapsedTimer>
#include <QObject>
#include <QSerialPort>
#include <QString>
#include <QTimer>
#include <Qt>
#include <QtMinMax>
#include <QtNumeric>
#include <QtTypes>
#include <qtmetamacros.h>

// 信号连接管理辅助类 - 使用RAII模式确保信号连接的正确恢复
class SerialProcessor::SignalConnectionGuard {
public:
    SignalConnectionGuard(QSerialPort *port, SerialProcessor *processor)
        : m_port(port),
          m_processor(processor),
          m_wasConnected(QObject::disconnect(m_port, &QSerialPort::errorOccurred, m_processor,
                                             &SerialProcessor::handleError)) // 检查信号是否已连接，然后断开
    {
    }

    ~SignalConnectionGuard()
    {
        // 析构时自动恢复信号连接
        if (m_wasConnected && (m_port != nullptr) && (m_processor != nullptr))
        {
            QObject::connect(m_port, &QSerialPort::errorOccurred, m_processor, &SerialProcessor::handleError,
                             Qt::DirectConnection);
        }
    }

    // 禁用拷贝和移动
    SignalConnectionGuard(const SignalConnectionGuard &) = delete;
    auto operator=(const SignalConnectionGuard &) -> SignalConnectionGuard & = delete;
    SignalConnectionGuard(SignalConnectionGuard &&) = delete;
    auto operator=(SignalConnectionGuard &&) -> SignalConnectionGuard & = delete;

private:
    QSerialPort *m_port;
    SerialProcessor *m_processor;
    bool m_wasConnected;
};

SerialProcessor::SerialProcessor(QObject *parent)
    : QObject(parent),
      m_parser(new VT100Parser(this)),
      m_connectionMonitor(new QTimer(this)),
      m_baseMonitorInterval(App::Connection::kDefaultBaseMonitorIntervalMs),
      m_currentMonitorInterval(App::Connection::kDefaultBaseMonitorIntervalMs),
      m_minMonitorInterval(App::Connection::kMinMonitorIntervalMs),
      m_maxMonitorInterval(App::Connection::kMaxMonitorIntervalMs),
      m_statisticsTimer(new QTimer(this)),
      m_reconnectTimer(new QTimer(this)),
      m_reconnectIntervalMs(App::Reconnect::kDefaultReconnectIntervalMs),
      m_currentReconnectInterval(App::Reconnect::kDefaultReconnectIntervalMs)
{
    // 使用直连方式确保数据立即在串口线程中处理
    connect(&m_serialPort, &QSerialPort::readyRead, this, &SerialProcessor::handleReadyRead, Qt::DirectConnection);

    // 连接错误信号处理
    connect(&m_serialPort, &QSerialPort::errorOccurred, this, &SerialProcessor::handleError, Qt::DirectConnection);

    // 初始化连接监控定时器
    m_connectionMonitor->setInterval(m_currentMonitorInterval);
    connect(m_connectionMonitor, &QTimer::timeout, this, &SerialProcessor::checkConnectionHealth);

    // 初始化统计定时器
    m_statisticsTimer->setInterval(App::Connection::kDefaultStatisticsIntervalMs);
    connect(m_statisticsTimer, &QTimer::timeout, this, &SerialProcessor::updateStatistics);

    // 初始化重连定时器
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &SerialProcessor::attemptReconnection);

    setNewLineMode(kDefaultNewLineMode);
}

SerialProcessor::~SerialProcessor()
{
    // 停止重连定时器
    cancelReconnection();

    stopConnectionMonitoring();
    if (m_statisticsTimer->isActive())
    {
        m_statisticsTimer->stop();
    }
    closePort();
}

auto SerialProcessor::openPort(const QString &portName, int baudRate, QSerialPort::DataBits dataBits,
                               QSerialPort::Parity parity, QSerialPort::StopBits stopBits) -> bool
{
    // 清除之前的错误信息
    m_lastError.clear();

    // 参数验证
    if (portName.isEmpty())
    {
        m_lastError = "端口名称不能为空";
        emit errorOccurred(m_lastError);
        return false;
    }

    if (baudRate <= 0)
    {
        m_lastError = QString("无效的波特率: %1").arg(baudRate);
        emit errorOccurred(m_lastError);
        return false;
    }

    // 关闭已打开的端口
    closePort();

    // 设置端口参数
    m_serialPort.setPortName(portName);
    m_serialPort.setBaudRate(baudRate);
    m_serialPort.setDataBits(dataBits);
    m_serialPort.setParity(parity);
    m_serialPort.setStopBits(stopBits);
    m_serialPort.setFlowControl(QSerialPort::NoFlowControl);

    // 打开端口
    if (m_serialPort.open(QIODevice::ReadWrite))
    {
        // 清除错误状态
        m_serialPort.clearError();

        // 备份连接参数（用于自动重连）
        backupConnectionParameters();

        // 重置连接健康状态
        m_isConnectionHealthy = true;
        m_consecutiveFailures = 0;

        // 重置智能监控状态
        m_connectionQuality = App::Connection::kInitialConnectionQuality;
        m_currentMonitorInterval = m_baseMonitorInterval;
        m_connectionMonitor->setInterval(m_currentMonitorInterval);
        m_responseTimeHistory.clear();
        m_healthCheckHistory.clear();
        m_dataStallCount = 0;
        m_lastDataActivity = QDateTime::currentDateTime();

        // 重置统计信息
        resetStatistics();
        m_connectionTime = QDateTime::currentDateTime();

        // 重置重连状态
        m_currentReconnectAttempt = 0;
        m_currentReconnectInterval = m_reconnectIntervalMs;

        // 设置连接状态
        setState(ConnectionState::Connected);

        // 开始连接监控
        startConnectionMonitoring();
        m_statisticsTimer->start(); // 开始统计更新

        emit connectionStatusChanged(true);
        emit connectionHealthChanged(true);
        return true;
    }

    // 记录打开失败的原因，但不发射errorOccurred信号
    // 串口操作失败的错误处理将由handleError方法统一处理（通过QSerialPort的errorOccurred信号）
    // 这样避免重复的错误信号
    m_lastError = QString("打开端口失败: %1 - %2").arg(portName, m_serialPort.errorString());
    return false;
}

void SerialProcessor::closePort()
{
    // 停止任何正在进行的重连
    cancelReconnection();

    // 停止连接监控
    stopConnectionMonitoring();

    // 停止统计更新
    if (m_statisticsTimer->isActive())
    {
        m_statisticsTimer->stop();
    }

    if (m_serialPort.isOpen())
    {
        m_serialPort.close();
        emit connectionStatusChanged(false);
    }

    // 设置断开状态
    setState(ConnectionState::Disconnected);

    // 重置连接状态
    m_isConnectionHealthy = true;
    m_consecutiveFailures = 0;
    emit connectionHealthChanged(true);
}

auto SerialProcessor::isOpen() const -> bool
{
    return m_serialPort.isOpen();
}

auto SerialProcessor::getLastError() const -> QString
{
    return m_lastError;
}

auto SerialProcessor::portName() const -> QString
{
    return m_serialPort.portName();
}

auto SerialProcessor::parser() const -> VT100Parser *
{
    return m_parser;
}

void SerialProcessor::handleReadyRead()
{
    // 检查串口是否仍然打开
    if (!m_serialPort.isOpen())
    {
        return;
    }

    // 一次性读取所有可用数据
    if (m_serialPort.bytesAvailable() > 0)
    {
        const QByteArray kData = m_serialPort.readAll();

        // 检查读取是否成功
        if (kData.isEmpty() && m_serialPort.error() != QSerialPort::NoError)
        {
            m_lastError = QString("读取数据失败: %1").arg(m_serialPort.errorString());
            emit errorOccurred(m_lastError);
            return;
        }

        if (!kData.isEmpty())
        {
            // 更新接收统计
            m_bytesReceived += kData.size();
            m_lastReceiveTime = QDateTime::currentDateTime();

            // 更新数据活动时间（用于智能监控）
            m_lastDataActivity = m_lastReceiveTime;
            m_dataStallCount = 0; // 重置停滞计数

            emit dataForLogging(kData);

            // 根据显示模式处理数据
            // 移除了 try-catch 块，因为异常被禁用且 m_parser 是 Qt 对象
            if (m_parser != nullptr)
            {
                if (m_displayMode == DisplayMode::ASCII)
                {
                    // ASCII模式：直接传递给VT100解析器，支持控制序列
                    m_parser->processData(kData);
                }
                else
                {
                    // 十六进制或混合模式：发送原始数据信号，绕过VT100解析
                    emit rawDataReceived(kData);
                }
            }
        }
    }
}

void SerialProcessor::writeData(const QByteArray &data)
{
    if (!m_serialPort.isOpen())
    {
        m_lastError = "串口未打开，无法写入数据";
        emit errorOccurred(m_lastError);
        return;
    }

    if (data.isEmpty())
    {
        return; // 空数据不需要处理
    }

    QByteArray processedData = data;
    // Replace \n with the selected newline sequence, but only if it's a single \n
    if (processedData.endsWith('\n') && !processedData.endsWith("\r\n"))
    {
        processedData.chop(kBytesToChopForNewLine);
        processedData.append(m_newLineSequence);
    }

    const qint64 kBytesWritten = m_serialPort.write(processedData);

    // 检查写入是否成功
    if (kBytesWritten == -1)
    {
        m_lastError = QString("写入数据失败: %1").arg(m_serialPort.errorString());
        emit errorOccurred(m_lastError);
        return;
    }

    if (kBytesWritten != processedData.size())
    {
        m_lastError =
            QString("数据写入不完整: 期望 %1 字节，实际写入 %2 字节").arg(processedData.size()).arg(kBytesWritten);
        emit errorOccurred(m_lastError);
    }
    else
    {
        // 更新发送统计（只有在完全写入成功时才统计）
        m_bytesSent += kBytesWritten;
        m_lastSendTime = QDateTime::currentDateTime();
    }

    if (m_echoEnabled)
    {
        emit dataForLogging(processedData);

        // 安全地处理回显数据
        // 移除了 try-catch 块
        if (m_parser != nullptr)
        {
            m_parser->processData(processedData);
        }
    }
}

void SerialProcessor::writeDataRaw(const QByteArray &data)
{
    if (!m_serialPort.isOpen())
    {
        m_lastError = "串口未打开，无法写入原始数据";
        emit errorOccurred(m_lastError);
        return;
    }

    if (data.isEmpty())
    {
        return; // 空数据不需要处理
    }

    const qint64 kBytesWritten = m_serialPort.write(data);

    // 检查写入是否成功
    if (kBytesWritten == -1)
    {
        m_lastError = QString("写入原始数据失败: %1").arg(m_serialPort.errorString());
        emit errorOccurred(m_lastError);
        return;
    }

    if (kBytesWritten != data.size())
    {
        m_lastError = QString("原始数据写入不完整: 期望 %1 字节，实际写入 %2 字节").arg(data.size()).arg(kBytesWritten);
        emit errorOccurred(m_lastError);
    }
    else
    {
        // 更新发送统计（只有在完全写入成功时才统计）
        m_bytesSent += kBytesWritten;
        m_lastSendTime = QDateTime::currentDateTime();
    }

    if (m_echoEnabled)
    {
        emit dataForLogging(data);

        // 安全地处理回显数据
        // 移除了 try-catch 块
        if (m_parser != nullptr)
        {
            m_parser->processData(data);
        }
    }
}

void SerialProcessor::sendBreak()
{
    if (m_serialPort.isOpen())
    {
        if (m_serialPort.setBreakEnabled(true))
        {
            // 中断条件将一直有效，直到调用 setBreakEnabled(false)。
            // 通常会使用一个短暂的延迟。
            QTimer::singleShot(App::Connection::kDefaultReconnectDelayMs, this, [this]() {
                m_serialPort.setBreakEnabled(false);
            });
        }
    }
}

void SerialProcessor::setEchoEnabled(bool enabled)
{
    m_echoEnabled = enabled;
}

void SerialProcessor::setNewLineMode(NewLineMode mode)
{
    m_newLineMode = mode;
    switch (m_newLineMode)
    {
        case NewLineMode::CrLf:
            m_newLineSequence = "\r\n";
            break;
        case NewLineMode::Cr:
            m_newLineSequence = "\r";
            break;
        case NewLineMode::Lf:
            m_newLineSequence = "\n";
            break;
    }
}

void SerialProcessor::setDisplayMode(DisplayMode mode)
{
    m_displayMode = mode;
}

void SerialProcessor::handleError(QSerialPort::SerialPortError error)
{
    if (error == QSerialPort::NoError)
    {
        return;
    }

    // 如果当前正在重连过程中，抑制错误信号
    if (isReconnecting())
    {
        return; // 不发射errorOccurred信号
    }

    // 首先检查是否应该触发自动重连（对于可能导致连接丢失的错误）
    const bool kIsConnectionLossError =
        (error == QSerialPort::ResourceError || error == QSerialPort::DeviceNotFoundError
         || error == QSerialPort::PermissionError || error == QSerialPort::OpenError);

    const bool kShouldTriggerReconnect =
        kIsConnectionLossError && m_autoReconnectEnabled && m_connectionState == ConnectionState::Connected;

    if (kShouldTriggerReconnect)
    {
        startReconnecting();
        return; // 不发射errorOccurred信号，避免弹窗
    }

    QString rawErrorString = m_serialPort.errorString();
    if (rawErrorString.isEmpty())
    {
        switch (error)
        {
            case QSerialPort::DeviceNotFoundError:
                rawErrorString = "设备未找到";
                break;
            case QSerialPort::PermissionError:
                rawErrorString = "权限错误，无法访问设备";
                break;
            case QSerialPort::OpenError:
                rawErrorString = "设备已被其他程序占用或无法打开";
                break;
            case QSerialPort::ResourceError:
                // 如果没有触发自动重连，按原有逻辑处理
                emit connectionLost();
                if (m_serialPort.isOpen())
                {
                    closePort();
                }
                return;
            case QSerialPort::WriteError:
                rawErrorString = "写入数据时发生错误";
                break;
            case QSerialPort::ReadError:
                rawErrorString = "读取数据时发生错误";
                break;
            case QSerialPort::UnsupportedOperationError:
                rawErrorString = "不支持的操作";
                break;
            case QSerialPort::TimeoutError:
                rawErrorString = "操作超时";
                break;
            case QSerialPort::NotOpenError:
                rawErrorString = "串口未打开";
                break;
            case QSerialPort::NoError:
                break; // NoError 不应触发 handleError，但为了完整性处理
            case QSerialPort::UnknownError:
                rawErrorString = QString("未知串口错误 (代码: %1)").arg(error);
                break;
        }
    }

    emit errorOccurred(rawErrorString);

    bool shouldClosePort = false;
    switch (error)
    {
        case QSerialPort::DeviceNotFoundError:
        case QSerialPort::PermissionError:
        case QSerialPort::OpenError:
        case QSerialPort::ResourceError:
            shouldClosePort = true;
            break;
        case QSerialPort::NoError:
        case QSerialPort::WriteError:
        case QSerialPort::ReadError:
        case QSerialPort::UnsupportedOperationError:
        case QSerialPort::UnknownError:
        case QSerialPort::TimeoutError:
        case QSerialPort::NotOpenError:
            // 对于这些错误，我们不关闭端口
            break;
    }
    if (shouldClosePort && m_serialPort.isOpen())
    {
        closePort();
    }
}

auto SerialProcessor::isConnectionHealthy() const -> bool
{
    // 如果串口没有打开，返回基础健康状态（通常是true）
    // 如果串口打开了，则返回实际的连接健康状态
    if (!m_serialPort.isOpen())
    {
        return m_isConnectionHealthy;
    }
    return m_isConnectionHealthy;
}

void SerialProcessor::startConnectionMonitoring()
{
    if (m_serialPort.isOpen() && !m_connectionMonitor->isActive())
    {
        m_connectionMonitor->start();
    }
}

void SerialProcessor::stopConnectionMonitoring()
{
    if (m_connectionMonitor->isActive())
    {
        m_connectionMonitor->stop();
    }
}

void SerialProcessor::checkConnectionHealth()
{
    // 如果串口已经关闭，停止监控
    if (!m_serialPort.isOpen())
    {
        stopConnectionMonitoring();
        return;
    }

    QElapsedTimer responseTimer;
    responseTimer.start();

    // 检查串口是否仍然可用
    bool currentlyHealthy = true;

    // 尝试获取串口状态
    if (m_serialPort.error() != QSerialPort::NoError)
    {
        currentlyHealthy = false;
        m_consecutiveFailures++;
    }
    else
    {
        // 尝试检查串口是否仍然可读写
        // 注意：这里不进行实际的读写操作，只检查状态
        if (!m_serialPort.isReadable() || !m_serialPort.isWritable())
        {
            currentlyHealthy = false;
            m_consecutiveFailures++;
        }
        else
        {
            // 连接正常，重置失败计数
            m_consecutiveFailures = 0;
        }
    }

    // 记录响应时间
    const qint64 kResponseTime = responseTimer.elapsed();
    m_responseTimeHistory.append(kResponseTime);
    if (m_responseTimeHistory.size() > kMaxHistorySize)
    {
        m_responseTimeHistory.removeFirst();
    }

    // 如果连续失败次数过多，认为连接不健康
    if (m_consecutiveFailures >= kConsecutiveFailuresThreshold)
    {
        currentlyHealthy = false;
    }

    // 添加健康检查结果到历史
    addHealthCheckResult(currentlyHealthy);

    // 如果健康状态发生变化，发射信号
    if (currentlyHealthy != m_isConnectionHealthy)
    {
        m_isConnectionHealthy = currentlyHealthy;
        emit connectionHealthChanged(m_isConnectionHealthy);

        // 如果连接变得不健康，记录错误
        if (!m_isConnectionHealthy)
        {
            m_lastError = QString("连接健康检查失败，连续失败次数: %1").arg(m_consecutiveFailures);
            // 注意：这里不发射connectionLost信号，因为可能只是暂时的问题
        }
    }

    // 执行智能监控功能
    updateConnectionQuality();
    adjustMonitorInterval();
    detectDataStall();
    detectConnectionAnomalies();
}

// 数据统计功能实现
auto SerialProcessor::getBytesReceived() const -> qint64
{
    return m_bytesReceived;
}

auto SerialProcessor::getBytesSent() const -> qint64
{
    return m_bytesSent;
}

auto SerialProcessor::getReceiveRate() const -> double
{
    return m_receiveRate;
}

auto SerialProcessor::getSendRate() const -> double
{
    return m_sendRate;
}

auto SerialProcessor::getConnectionTime() const -> QDateTime
{
    return m_connectionTime;
}

auto SerialProcessor::getConnectionDuration() const -> qint64
{
    if (!m_connectionTime.isValid() || !m_serialPort.isOpen())
    {
        return 0;
    }
    return m_connectionTime.secsTo(QDateTime::currentDateTime());
}

void SerialProcessor::resetStatistics()
{
    m_bytesReceived = 0;
    m_bytesSent = 0;
    m_lastReceiveBytes = 0;
    m_lastSendBytes = 0;
    m_receiveRate = 0.0;
    m_sendRate = 0.0;
    m_lastReceiveTime = QDateTime();
    m_lastSendTime = QDateTime();
}

void SerialProcessor::updateStatistics()
{
    if (!m_serialPort.isOpen())
    {
        return;
    }

    const QDateTime kCurrentTime = QDateTime::currentDateTime();

    // 计算接收速率（字节/秒）
    if (m_lastReceiveTime.isValid())
    {
        const qint64 kTimeDiff = m_lastReceiveTime.secsTo(kCurrentTime);
        if (kTimeDiff > 0)
        {
            const qint64 kBytesDiff = m_bytesReceived - m_lastReceiveBytes;
            m_receiveRate = static_cast<double>(kBytesDiff) / static_cast<double>(kTimeDiff);
        }
    }

    // 计算发送速率（字节/秒）
    if (m_lastSendTime.isValid())
    {
        const qint64 kTimeDiff = m_lastSendTime.secsTo(kCurrentTime);
        if (kTimeDiff > 0)
        {
            const qint64 kBytesDiff = m_bytesSent - m_lastSendBytes;
            m_sendRate = static_cast<double>(kBytesDiff) / static_cast<double>(kTimeDiff);
        }
    }

    // 更新上次统计的数据
    m_lastReceiveBytes = m_bytesReceived;
    m_lastSendBytes = m_bytesSent;

    // 发射统计更新信号
    emit statisticsUpdated();
}

// 智能连接监控方法实现

auto SerialProcessor::getConnectionQuality() const -> double
{
    return m_connectionQuality;
}

auto SerialProcessor::getCurrentMonitorInterval() const -> int
{
    return m_currentMonitorInterval;
}

void SerialProcessor::setMonitorIntervalRange(const MonitorIntervalRange &range)
{
    m_minMonitorInterval = qMax(App::Connection::kMinMonitorIntervalMs, range.minMs);
    m_maxMonitorInterval = qMin(App::Connection::kMaxMonitorIntervalMs, range.maxMs);

    // 确保当前间隔在新范围内
    m_currentMonitorInterval = qBound(m_minMonitorInterval, m_currentMonitorInterval, m_maxMonitorInterval);
    m_connectionMonitor->setInterval(m_currentMonitorInterval);
}

auto SerialProcessor::getConnectionQualityDescription() const -> QString
{
    if (m_connectionQuality >= App::Connection::kQualityThresholdExcellent)
    {
        return "优秀";
    }
    if (m_connectionQuality >= App::Connection::kQualityThresholdGood)
    {
        return "良好";
    }
    if (m_connectionQuality >= App::Connection::kQualityThresholdFair)
    {
        return "一般";
    }
    if (m_connectionQuality >= App::Connection::kQualityThresholdPoor)
    {
        return "较差";
    }
    return "很差";
}

void SerialProcessor::updateConnectionQuality()
{
    const double kNewQuality = calculateQualityScore();

    // 使用平滑算法更新质量评分，避免剧烈波动
    m_connectionQuality = App::Connection::kQualitySmoothingFactor * kNewQuality
                          + (1.0 - App::Connection::kQualitySmoothingFactor) * m_connectionQuality;

    // 限制在0.0-1.0范围内
    m_connectionQuality =
        qBound(App::Connection::kMinQualityScore, m_connectionQuality, App::Connection::kMaxConnectionQuality);

    emit connectionQualityChanged(m_connectionQuality);
}

void SerialProcessor::adjustMonitorInterval()
{
    // 根据连接质量自适应调整监控间隔
    // 质量越差，监控越频繁
    const double kQualityFactor = 1.0 - m_connectionQuality; // 0.0(优秀) - 1.0(很差)

    // 计算新的监控间隔
    int newInterval =
        m_baseMonitorInterval + static_cast<int>(kQualityFactor * (m_maxMonitorInterval - m_baseMonitorInterval));

    // 如果连接质量很好，可以适当延长间隔
    if (m_connectionQuality > App::Connection::kQualityThresholdVeryGood)
    {
        newInterval = qMin(newInterval + App::Connection::kMonitorIntervalIncrementMs, m_maxMonitorInterval);
    }

    // 限制在设定范围内
    newInterval = qBound(m_minMonitorInterval, newInterval, m_maxMonitorInterval);

    // 如果间隔发生显著变化，更新定时器
    if (qAbs(newInterval - m_currentMonitorInterval) > App::Connection::kMonitorIntervalChangeThresholdMs)
    {
        m_currentMonitorInterval = newInterval;
        m_connectionMonitor->setInterval(m_currentMonitorInterval);
        emit monitorIntervalChanged(m_currentMonitorInterval);
    }
}

void SerialProcessor::detectDataStall()
{
    if (!m_lastDataActivity.isValid())
    {
        return;
    }

    const QDateTime kCurrentTime = QDateTime::currentDateTime();
    const qint64 kStallTime = m_lastDataActivity.msecsTo(kCurrentTime);

    // 如果数据停滞时间超过阈值
    if (kStallTime > kMaxDataStallTime.count())
    {
        m_dataStallCount++;
        emit dataStallDetected(static_cast<int>(kStallTime));

        // 更新最后活动时间，避免重复触发
        m_lastDataActivity = kCurrentTime;
    }
}

void SerialProcessor::detectConnectionAnomalies()
{
    // 检测响应时间异常
    if (m_responseTimeHistory.size() >= kResponseTimeHistorySize)
    {
        // 计算平均响应时间
        qint64 totalTime = 0;
        for (const qint64 kTime : m_responseTimeHistory)
        {
            totalTime += kTime;
        }
        const double kAvgResponseTime =
            static_cast<double>(totalTime) / static_cast<double>(m_responseTimeHistory.size());

        // 检测最近的响应时间是否异常
        const qint64 kRecentTime = m_responseTimeHistory.last();
        if (static_cast<double>(kRecentTime) > kAvgResponseTime * App::Connection::kResponseTimeSpikeFactor)
        {
            emit connectionAnomalyDetected(
                QString("响应时间异常: %1ms (平均: %2ms)").arg(kRecentTime).arg(static_cast<int>(kAvgResponseTime)));
        }
    }

    // 检测健康检查模式异常
    if (m_healthCheckHistory.size() >= kHealthCheckHistorySize)
    {
        int recentFailures = 0;
        const int kCheckCount = qMin(kRecentHealthCheckCount, static_cast<int>(m_healthCheckHistory.size()));

        // 检查最近的失败率
        for (int i = static_cast<int>(m_healthCheckHistory.size()) - kCheckCount;
             i < static_cast<int>(m_healthCheckHistory.size()); ++i)
        {
            if (!m_healthCheckHistory[i])
            {
                recentFailures++;
            }
        }

        const double kFailureRate = static_cast<double>(recentFailures) / kCheckCount;
        if (kFailureRate > App::Connection::kRecentFailureRateThreshold)
        {
            emit connectionAnomalyDetected(
                QString("健康检查失败率异常: %1%")
                    .arg(static_cast<int>(kFailureRate * App::Common::kPercentageMultiplier)));
        }
    }
}

void SerialProcessor::addHealthCheckResult(bool healthy)
{
    m_healthCheckHistory.append(healthy);
    if (m_healthCheckHistory.size() > kMaxHistorySize)
    {
        m_healthCheckHistory.removeFirst();
    }
}

auto SerialProcessor::calculateQualityScore() const -> double
{
    double score = App::Connection::kInitialConnectionQuality;

    // 基于健康检查历史计算分数
    if (!m_healthCheckHistory.isEmpty())
    {
        int healthyCount = 0;
        for (const bool kHealthy : m_healthCheckHistory)
        {
            if (kHealthy)
            {
                healthyCount++;
            }
        }
        const double kHealthRatio =
            static_cast<double>(healthyCount) / static_cast<double>(m_healthCheckHistory.size());
        score *= kHealthRatio;
    }

    // 基于响应时间计算分数
    if (!m_responseTimeHistory.isEmpty())
    {
        qint64 totalTime = 0;
        for (const qint64 kTime : m_responseTimeHistory)
        {
            totalTime += kTime;
        }
        const double kAvgResponseTime =
            static_cast<double>(totalTime) / static_cast<double>(m_responseTimeHistory.size());

        // 响应时间越短，分数越高
        if (kAvgResponseTime <= App::Connection::kExcellentResponseTimeMs)
        {
            // 响应时间优秀，不扣分
        }
        else if (kAvgResponseTime <= App::Connection::kGoodResponseTimeMs)
        {
            score *= (1.0
                      - (kAvgResponseTime - App::Connection::kExcellentResponseTimeMs)
                            / App::Connection::kResponseTimePenaltyRange * App::Connection::kResponseTimePenaltyFactor);
        }
        else
        {
            score *= App::Connection::kPoorResponseTimePenalty;
        }
    }

    // 基于数据停滞情况调整分数
    if (m_dataStallCount > 0)
    {
        score *= qMax(App::Connection::kMinQualityScore,
                      1.0 - (m_dataStallCount * App::Connection::kDataStallPenaltyFactor));
    }

    // 基于连续失败次数调整分数
    if (m_consecutiveFailures > 0)
    {
        score *= qMax(App::Connection::kMinQualityScore,
                      1.0 - (m_consecutiveFailures * App::Connection::kFailurePenaltyFactor));
    }

    return qBound(App::Connection::kMinQualityScore, score, App::Connection::kMaxConnectionQuality);
}

// 自动重连功能实现

auto SerialProcessor::connectionState() const -> ConnectionState
{
    return m_connectionState;
}

auto SerialProcessor::isReconnecting() const -> bool
{
    return m_connectionState == ConnectionState::Reconnecting;
}

void SerialProcessor::setAutoReconnectEnabled(bool enabled)
{
    m_autoReconnectEnabled = enabled;
}

void SerialProcessor::setMaxReconnectAttempts(int attempts)
{
    m_maxReconnectAttempts =
        qBound(App::Reconnect::kMinReconnectAttempts, attempts, App::Reconnect::kMaxReconnectAttempts);
}

void SerialProcessor::setReconnectInterval(int intervalMs)
{
    m_reconnectIntervalMs =
        qBound(App::Reconnect::kMinReconnectIntervalMs, intervalMs, App::Reconnect::kMaxReconnectIntervalMs);
    m_currentReconnectInterval = m_reconnectIntervalMs;
}

void SerialProcessor::setReconnectIntervalMultiplier(double multiplier)
{
    m_reconnectIntervalMultiplier =
        qBound(App::Reconnect::kMinReconnectMultiplier, multiplier, App::Reconnect::kMaxReconnectMultiplier);
}

void SerialProcessor::cancelReconnection()
{
    if (m_reconnectTimer != nullptr && m_reconnectTimer->isActive())
    {
        m_reconnectTimer->stop();
    }

    if (isReconnecting())
    {
        setState(ConnectionState::Disconnected);
    }
}

void SerialProcessor::setState(ConnectionState newState)
{
    if (m_connectionState != newState)
    {
        m_connectionState = newState;
        emit connectionStateChanged(m_connectionState, m_currentReconnectAttempt, m_maxReconnectAttempts);
    }
}

void SerialProcessor::backupConnectionParameters()
{
    m_lastPortName = m_serialPort.portName();
    m_lastBaudRate = m_serialPort.baudRate();
    m_lastDataBits = m_serialPort.dataBits();
    m_lastParity = m_serialPort.parity();
    m_lastStopBits = m_serialPort.stopBits();
    m_lastFlowControl = m_serialPort.flowControl();
}

void SerialProcessor::startReconnecting()
{
    // 如果已经在重连状态，不重复启动
    if (isReconnecting())
    {
        return;
    }

    // 关闭当前连接并发射断开信号
    if (m_serialPort.isOpen())
    {
        m_serialPort.close();
        emit connectionStatusChanged(false); // 通知UI连接已断开
    }

    // 停止连接监控和统计
    stopConnectionMonitoring();
    if (m_statisticsTimer->isActive())
    {
        m_statisticsTimer->stop();
    }

    // 重置重连状态
    m_currentReconnectAttempt = 0;
    m_currentReconnectInterval = m_reconnectIntervalMs;

    // 设置为重连状态
    setState(ConnectionState::Reconnecting);

    // 立即开始第一次重连尝试
    QTimer::singleShot(0, this, &SerialProcessor::attemptReconnection);
}

void SerialProcessor::attemptReconnection()
{
    // 检查是否应该继续重连
    if (!isReconnecting())
    {
        return; // 用户可能已经取消了重连
    }

    m_currentReconnectAttempt++;

    // 更新状态以反映当前尝试次数
    emit connectionStateChanged(m_connectionState, m_currentReconnectAttempt, m_maxReconnectAttempts);

    // 使用RAII模式管理信号连接，确保异常安全
    // 移除了 try-catch 块，因为异常被禁用
    const SignalConnectionGuard kGuard(&m_serialPort, this);

    // 尝试重新连接
    if (reconnectPort())
    {
        onReconnectionSuccess();
    }
    else
    {
        onReconnectionFailed();
    }
}

void SerialProcessor::onReconnectionSuccess()
{
    // 重连成功，状态已在 reconnectPort() 中设置为 Connected。
    // 此处发射最终状态信号，通知UI和其他组件重连已完成，并包含尝试次数信息。
    emit connectionStateChanged(m_connectionState, m_currentReconnectAttempt, m_maxReconnectAttempts);
}

void SerialProcessor::onReconnectionFailed()
{
    // 检查是否已达到最大重连次数
    if (m_currentReconnectAttempt >= m_maxReconnectAttempts)
    {
        // 达到最大次数，设置为失败状态
        setState(ConnectionState::Failed);
        return;
    }

    // 还有重连机会，确保状态仍然是Reconnecting
    if (!isReconnecting())
    {
        setState(ConnectionState::Reconnecting);
    }

    // 计算下次重连间隔并安排下次尝试
    calculateNextReconnectInterval();

    // 安排下次重连尝试
    if (m_reconnectTimer != nullptr)
    {
        m_reconnectTimer->start(m_currentReconnectInterval);
    }
}

auto SerialProcessor::reconnectPort() -> bool
{
    // 重连专用的端口打开方法，不执行完整的closePort()逻辑
    if (m_serialPort.isOpen())
    {
        m_serialPort.close();
    }

    // 设置端口参数
    m_serialPort.setPortName(m_lastPortName);
    m_serialPort.setBaudRate(m_lastBaudRate);
    m_serialPort.setDataBits(m_lastDataBits);
    m_serialPort.setParity(m_lastParity);
    m_serialPort.setStopBits(m_lastStopBits);
    m_serialPort.setFlowControl(m_lastFlowControl);

    // 尝试打开端口
    if (m_serialPort.open(QIODevice::ReadWrite))
    {
        // 清除错误状态
        m_serialPort.clearError();

        // 重置连接健康状态
        m_isConnectionHealthy = true;
        m_consecutiveFailures = 0;

        // 重置智能监控状态
        m_connectionQuality = App::Connection::kInitialConnectionQuality;
        m_currentMonitorInterval = m_baseMonitorInterval;
        m_connectionMonitor->setInterval(m_currentMonitorInterval);
        m_responseTimeHistory.clear();
        m_healthCheckHistory.clear();
        m_dataStallCount = 0;
        m_lastDataActivity = QDateTime::currentDateTime();

        // 重置统计信息
        resetStatistics();
        m_connectionTime = QDateTime::currentDateTime();

        // 重置重连状态
        m_currentReconnectAttempt = 0;
        m_currentReconnectInterval = m_reconnectIntervalMs;

        // 设置连接状态
        setState(ConnectionState::Connected);

        // 开始连接监控
        startConnectionMonitoring();
        m_statisticsTimer->start();

        emit connectionStatusChanged(true);
        emit connectionHealthChanged(true);
        return true;
    }

    return false;
}

void SerialProcessor::calculateNextReconnectInterval()
{
    // 使用指数退避算法计算下次重连间隔
    m_currentReconnectInterval = static_cast<int>(m_currentReconnectInterval * m_reconnectIntervalMultiplier);

    // 限制最大间隔
    m_currentReconnectInterval = qMin(m_currentReconnectInterval, App::Reconnect::kMaxReconnectIntervalMs);
}
