---
# 测试文件专用的clang-tidy配置
# 这个配置会覆盖根目录的配置，专门用于测试代码

Checks: >-
  -*,
  bugprone-*,
  clang-analyzer-*,
  # This check gives false positives with <PERSON>t's Q_FLAGS system, where ORing flags
  # is incorrectly identified as an out-of-range cast.
  -clang-analyzer-optin.core.EnumCastOutOfRange,
  -bugprone-easily-swappable-parameters,
  -bugprone-unused-return-value,
  -bugprone-empty-catch,
  -bugprone-implicit-widening-of-multiplication-result,
  -readability-magic-numbers,
  -readability-function-size,
  -readability-function-cognitive-complexity,
  -readability-convert-member-functions-to-static,
  -performance-unnecessary-value-param,
  -readability-identifier-length

UseColor: false
HeaderFilterRegex: ".*"
FormatStyle: none

# 系统头文件检查配置
SystemHeaders: false

# 测试代码的特殊配置
CheckOptions:
  # 允许测试代码使用更长的函数
  - key: readability-function-size.LineThreshold
    value: 200
  
  # 允许测试代码使用魔法数字
  - key: readability-magic-numbers.IgnoredIntegerValues
    value: "0;1;2;3;4;5;10;100;1000"
  
  # 允许短变量名在测试中使用
  - key: readability-identifier-length.MinimumVariableNameLength
    value: 1
  
  # 允许短参数名在测试中使用
  - key: readability-identifier-length.MinimumParameterNameLength
    value: 1
