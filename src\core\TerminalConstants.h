/**
 * @file TerminalConstants.h
 * @brief 定义了VT100终端相关的所有常量，包括终端基础设置、VT100解析常量和缓冲区管理常量。
 *
 * 本文件包含了SerialT项目中VT100终端模拟器的核心常量定义，涵盖终端显示、
 * 字符解析、颜色控制和缓冲区管理等方面的配置参数。
 *
 * @note 这些常量是VT100终端功能的基础，修改时需要充分测试兼容性。
 */
#ifndef TERMINALCONSTANTS_H
#define TERMINALCONSTANTS_H

#include <cstdint>
#include <string_view>

namespace App::Terminal {

// #############################################################################
// # 终端基础设置常量 (Terminal Basic Settings Constants)
// #############################################################################

/**
 * @brief 默认终端缓冲区大小（行数）
 * 
 * 控制终端可以保存的历史行数，影响内存使用和滚动性能。
 * 较大的值提供更多历史记录，但会增加内存消耗。
 */
inline constexpr int kDefaultBufferSize = 5000;

/**
 * @brief 默认终端滚动策略
 * 
 * 对应 HighPerformanceTerminal::ScrollPolicy::ScrollToBottom
 * 0 = 自动滚动到底部，1 = 保持当前位置
 */
inline constexpr int kDefaultScrollPolicy = 0;

/**
 * @brief 默认输入时滚动行为
 * 
 * 当用户输入时是否自动滚动到终端底部，提升用户体验。
 */
inline constexpr bool kDefaultScrollOnInput = true;

/**
 * @brief 默认窗口大小调整时暂停行为
 * 
 * 控制在调整窗口大小时是否暂停终端更新，避免闪烁。
 */
inline constexpr bool kDefaultPauseOnResize = false;

/**
 * @brief 默认自动换行功能
 * 
 * 控制长行是否自动换行显示，影响文本布局和可读性。
 */
inline constexpr bool kDefaultWordWrapEnabled = true;

// #############################################################################
// # 默认终端字体设置常量 (Default Terminal Font Settings Constants)
// #############################################################################

using namespace std::string_view_literals;

/**
 * @brief 默认终端字体族
 * 
 * 选择等宽字体以确保字符对齐，Consolas是Windows平台的优秀选择。
 */
inline constexpr std::string_view kDefaultFontFamily = "Consolas"sv;

/**
 * @brief 默认终端字体大小
 * 
 * 平衡可读性和屏幕利用率的字体大小设置。
 */
inline constexpr int kDefaultFontSize = 10;

// #############################################################################
// # VT100 终端解析常量 (VT100 Terminal Parsing Constants)
// #############################################################################

/**
 * @brief ESC 转义字符
 * 
 * VT100控制序列的起始字符，用于识别控制命令的开始。
 */
inline constexpr std::uint8_t kEscapeChar = 0x1B;

/**
 * @brief 可打印字符范围开始（空格字符）
 * 
 * ASCII可打印字符的起始值，用于字符分类和显示判断。
 */
inline constexpr std::uint8_t kPrintableRangeStart = 0x20;

/**
 * @brief 可打印字符范围结束（波浪号字符）
 * 
 * ASCII可打印字符的结束值，用于字符分类和显示判断。
 */
inline constexpr std::uint8_t kPrintableRangeEnd = 0x7E;

/**
 * @brief CSI 最终字符范围开始（@字符）
 * 
 * Control Sequence Introducer最终字符的起始范围，用于解析VT100控制序列。
 */
inline constexpr std::uint8_t kCSIFinalRangeStart = 0x40;

/**
 * @brief CSI 最终字符范围结束（~字符）
 * 
 * Control Sequence Introducer最终字符的结束范围，用于解析VT100控制序列。
 */
inline constexpr std::uint8_t kCSIFinalRangeEnd = 0x7E;

// #############################################################################
// # VT100 SGR (Select Graphic Rendition) 颜色代码常量
// #############################################################################

/**
 * @brief SGR 禁用粗体命令代码
 * 
 * 用于取消文本的粗体显示效果。
 */
inline constexpr int kSGRBoldDisable = 22;

// 前景色常量定义
inline constexpr int kSGRForegroundBlack = 30;   ///< 前景色：黑色
inline constexpr int kSGRForegroundRed = 31;     ///< 前景色：红色
inline constexpr int kSGRForegroundGreen = 32;   ///< 前景色：绿色
inline constexpr int kSGRForegroundYellow = 33;  ///< 前景色：黄色
inline constexpr int kSGRForegroundBlue = 34;    ///< 前景色：蓝色
inline constexpr int kSGRForegroundMagenta = 35; ///< 前景色：洋红色
inline constexpr int kSGRForegroundCyan = 36;    ///< 前景色：青色
inline constexpr int kSGRForegroundWhite = 37;   ///< 前景色：白色

// 背景色常量定义
inline constexpr int kSGRBackgroundBlack = 40;   ///< 背景色：黑色
inline constexpr int kSGRBackgroundRed = 41;     ///< 背景色：红色
inline constexpr int kSGRBackgroundGreen = 42;   ///< 背景色：绿色
inline constexpr int kSGRBackgroundYellow = 43;  ///< 背景色：黄色
inline constexpr int kSGRBackgroundBlue = 44;    ///< 背景色：蓝色
inline constexpr int kSGRBackgroundMagenta = 45; ///< 背景色：洋红色
inline constexpr int kSGRBackgroundCyan = 46;    ///< 背景色：青色
inline constexpr int kSGRBackgroundWhite = 47;   ///< 背景色：白色

// #############################################################################
// # VT100 缓冲区相关常量 (VT100 Buffer Related Constants)
// #############################################################################

/**
 * @brief 缓冲区溢出时数据保留比例
 * 
 * 当缓冲区满时，保留85%的数据，删除最旧的15%，
 * 平衡内存使用和历史数据保存。
 */
inline constexpr double kBufferOverflowRetentionRatio = 0.85;

/**
 * @brief 最小命令缓冲区大小
 * 
 * 确保命令解析缓冲区的最小容量，避免频繁的内存重分配。
 */
inline constexpr int kMinCommandBufferSize = 1000;

/**
 * @brief 默认最大命令缓冲区大小
 * 
 * 提高高速数据处理能力的缓冲区大小设置，适应现代硬件性能。
 */
inline constexpr int kDefaultMaxCommandBufferSize = 100000;

/**
 * @brief 最大参数缓冲区大小
 * 
 * VT100控制序列参数解析的最大缓冲区大小，防止恶意或异常数据攻击。
 */
inline constexpr int kMaxParamsBufferSize = 1024;

} // namespace App::Terminal

#endif // TERMINALCONSTANTS_H
