#!/usr/bin/env python3
"""
SerialT 时间戳错位问题重现脚本 - 集成测试

依赖安装：
pip install pyserial

这个脚本用于验证时间戳错位问题的根因分析。
它会通过虚拟串口发送特定的数据模式来重现问题。

使用方法：
1. 确保有虚拟串口对（如COM30和COM31）
2. 在SerialT中连接到COM30，启用日志记录和时间戳
3. 运行此脚本连接到COM31发送测试数据
4. 检查SerialT的日志文件是否出现时间戳错位

测试场景：
- 分块数据发送（模拟网络延迟）
- 混合换行符测试（\r, \n, \r\n）
- 长行数据测试
- 二进制数据测试

作者：Unknown
日期：2025-07-18
"""

import serial
import time
import sys
import argparse
from typing import Optional

class TimestampBugReproducer:
    def __init__(self, port: str, baudrate: int = 115200):
        """
        初始化时间戳错位问题重现器
        
        Args:
            port: 串口名称（如COM31）
            baudrate: 波特率，默认115200
        """
        self.port = port
        self.baudrate = baudrate
        self.serial_conn: Optional[serial.Serial] = None
        
    def connect(self) -> bool:
        """连接到串口"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1
            )
            print(f"✓ 成功连接到 {self.port}")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            print(f"✓ 已断开 {self.port}")
    
    def send_chunked_data(self, data: str, chunk_size: int, delay_ms: int = 10):
        """
        分块发送数据，模拟网络传输或设备缓冲
        
        Args:
            data: 要发送的完整数据
            chunk_size: 每块的大小（字节）
            delay_ms: 块之间的延迟（毫秒）
        """
        if not self.serial_conn or not self.serial_conn.is_open:
            print("✗ 串口未连接")
            return
        
        data_bytes = data.encode('utf-8')
        total_chunks = (len(data_bytes) + chunk_size - 1) // chunk_size
        
        print(f"📤 发送数据: '{data}' ({len(data_bytes)} 字节)")
        print(f"📦 分块发送: {total_chunks} 块，每块 {chunk_size} 字节，延迟 {delay_ms}ms")
        
        for i in range(0, len(data_bytes), chunk_size):
            chunk = data_bytes[i:i + chunk_size]
            chunk_num = i // chunk_size + 1
            
            print(f"  📤 块 {chunk_num}/{total_chunks}: {chunk} ({len(chunk)} 字节)")
            
            self.serial_conn.write(chunk)
            self.serial_conn.flush()
            
            if delay_ms > 0:
                time.sleep(delay_ms / 1000.0)
    
    def test_scenario_1_long_line_chunked(self):
        """测试场景1：长行数据分块发送（无换行符）"""
        print("\n🧪 测试场景1: 长行数据分块发送")
        print("=" * 50)
        
        # 模拟你提到的问题数据
        test_data = "CDBK-load:8,4,193 do iP some additional data here that makes the line longer"
        
        # 分成小块发送，模拟网络传输
        self.send_chunked_data(test_data, chunk_size=8, delay_ms=20)
        
        print("⏳ 等待3秒...")
        time.sleep(3)
        
        # 发送换行符结束这一行
        print("📤 发送换行符")
        self.serial_conn.write(b'\n')
        self.serial_conn.flush()
    
    def test_scenario_2_mixed_data(self):
        """测试场景2：混合数据（有些有换行符，有些没有）"""
        print("\n🧪 测试场景2: 混合数据")
        print("=" * 50)
        
        # 第一行：正常行
        self.send_chunked_data("First normal line\n", chunk_size=5, delay_ms=10)
        time.sleep(0.5)
        
        # 第二行：分块发送，模拟问题场景
        test_data = "L                                      !D€   适]莇旂?拣?@BM(0,0)"
        self.send_chunked_data(test_data, chunk_size=6, delay_ms=15)
        time.sleep(1)
        
        # 第三行：继续发送数据
        self.send_chunked_data("\nThird line after problematic data\n", chunk_size=4, delay_ms=10)
    
    def test_scenario_3_rapid_chunks(self):
        """测试场景3：快速分块发送"""
        print("\n🧪 测试场景3: 快速分块发送")
        print("=" * 50)
        
        test_data = "RapidData123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        
        # 非常小的块，快速发送
        self.send_chunked_data(test_data, chunk_size=3, delay_ms=5)
        time.sleep(0.5)
        self.serial_conn.write(b'\n')
        self.serial_conn.flush()
    
    def test_scenario_4_binary_like_data(self):
        """测试场景4：类似二进制的数据"""
        print("\n🧪 测试场景4: 类似二进制的数据")
        print("=" * 50)
        
        # 模拟包含特殊字符的数据
        test_data = "Binary-like: \x01\x02\x03 DATA \xFF\xFE\xFD END"
        
        self.send_chunked_data(test_data, chunk_size=4, delay_ms=25)
        time.sleep(1)
        self.serial_conn.write(b'\n')
        self.serial_conn.flush()
    
    def run_all_tests(self):
        """运行所有测试场景"""
        print("🚀 开始时间戳错位问题重现测试")
        print("=" * 60)
        print("📋 请确保：")
        print("  1. SerialT已连接到对应的串口")
        print("  2. 已启用日志记录")
        print("  3. 已启用时间戳")
        print("=" * 60)
        
        input("按回车键开始测试...")
        
        try:
            self.test_scenario_1_long_line_chunked()
            time.sleep(2)
            
            self.test_scenario_2_mixed_data()
            time.sleep(2)
            
            self.test_scenario_3_rapid_chunks()
            time.sleep(2)
            
            self.test_scenario_4_binary_like_data()
            time.sleep(2)
            
            print("\n✅ 所有测试场景完成")
            print("📝 请检查SerialT的日志文件，查看是否出现时间戳错位")
            print("🔍 错误格式示例: 'CDBK-load:8,4,193[2025-07-18 15:35:58.336] do iP'")
            print("✅ 正确格式示例: '[2025-07-18 15:35:58.336] CDBK-load:8,4,193 do iP'")
            
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='SerialT 时间戳错位问题重现脚本')
    parser.add_argument('port', help='串口名称 (例如: COM31)')
    parser.add_argument('--baudrate', type=int, default=115200, help='波特率 (默认: 115200)')
    parser.add_argument('--list-ports', action='store_true', help='列出可用串口')
    
    args = parser.parse_args()
    
    if args.list_ports:
        print("📋 可用串口:")
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        for port in ports:
            print(f"  {port.device} - {port.description}")
        return
    
    reproducer = TimestampBugReproducer(args.port, args.baudrate)
    
    if reproducer.connect():
        try:
            reproducer.run_all_tests()
        finally:
            reproducer.disconnect()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
