/**
 * @file CommonConstants.h
 * @brief 定义了通用工具常量，包括数学计算、单位转换和其他跨模块使用的基础常量。
 *
 * 本文件包含了SerialT项目中各个模块都可能使用的通用常量定义，
 * 这些常量不属于特定的业务域，而是提供基础的计算和转换功能。
 *
 * @note 这些常量是基础工具常量，修改时需要考虑对整个项目的影响。
 */
#ifndef COMMONCONSTANTS_H
#define COMMONCONSTANTS_H

namespace App::Common {

// #############################################################################
// # 通用计算常量 (General Purpose Calculation Constants)
// #############################################################################

/**
 * @brief 百分比计算乘数
 *
 * 用于将小数形式的比例转换为百分比显示，例如：0.85 * 100 = 85%
 */
inline constexpr int kPercentageMultiplier = 100;

/**
 * @brief 字节到千字节的转换因子
 *
 * 用于内存和存储大小的单位转换，1KB = 1024字节（二进制标准）
 */
inline constexpr int kBytesPerKilobyte = 1024;

} // namespace App::Common

#endif // COMMONCONSTANTS_H
