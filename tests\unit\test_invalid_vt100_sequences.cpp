#include <QDebug>
#include <QSignalSpy>
#include <QtTest/QtTest>

#include "../../src/VT100Parser.h"

class TestInvalidVT100Sequences : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 无效VT100序列容错测试
    void testIncompleteEscapeSequences();
    void testMalformedCSISequences();
    void testInvalidParameters();
    void testTruncatedSequences();
    void testUnknownSequences();
    void testMixedValidInvalidSequences();
    void testExtremelyLongSequences();
    void testNullAndControlCharacters();
    void testRecoveryAfterInvalidSequence();
    void testConcurrentInvalidSequences();
    void testParserStateConsistency();
    void testMemoryLeakWithInvalidSequences();
    void testParameterBufferLimits();
    void testInvalidCharacterHandling();

private:
    VT100Parser *m_parser;

    // 辅助方法
    void processAndVerifyNoCrash(const QByteArray &data);
    int getCommandCount();
};

void TestInvalidVT100Sequences::initTestCase()
{
    qDebug() << "Starting invalid VT100 sequences tests";
}

void TestInvalidVT100Sequences::cleanupTestCase()
{
    qDebug() << "Invalid VT100 sequences tests completed";
}

void TestInvalidVT100Sequences::init()
{
    m_parser = new VT100Parser(this);
}

void TestInvalidVT100Sequences::cleanup()
{
    if (m_parser)
    {
        delete m_parser;
        m_parser = nullptr;
    }
}

void TestInvalidVT100Sequences::processAndVerifyNoCrash(const QByteArray &data)
{
    try
    {
        m_parser->processData(data);
        // 如果没有崩溃，测试通过
        QVERIFY(true);
    }
    catch (...)
    {
        QFAIL("Parser crashed when processing invalid VT100 sequence");
    }
}

int TestInvalidVT100Sequences::getCommandCount()
{
    return m_parser->getCurrentCommandCount();
}

void TestInvalidVT100Sequences::testIncompleteEscapeSequences()
{
    // 测试不完整的转义序列
    qDebug() << "Testing incomplete escape sequences";

    QStringList incompleteSequences = {
        "\x1b",      // 只有ESC
        "\x1b[",     // ESC[ 但没有结束
        "\x1b[3",    // ESC[3 但没有结束
        "\x1b[31",   // ESC[31 但没有结束
        "\x1b[31;",  // ESC[31; 但没有结束
        "\x1b[31;4", // ESC[31;4 但没有结束
    };

    for (const QString &seq : incompleteSequences)
    {
        qDebug() << "Testing incomplete sequence:" << seq.toUtf8().toHex();
        processAndVerifyNoCrash(seq.toUtf8());
    }

    qDebug() << "Incomplete escape sequences test completed";
}

void TestInvalidVT100Sequences::testMalformedCSISequences()
{
    // 测试格式错误的CSI序列
    qDebug() << "Testing malformed CSI sequences";

    QStringList malformedSequences = {
        "\x1b[m",       // 空参数
        "\x1b[;m",      // 空参数分隔符
        "\x1b[;;m",     // 多个空参数
        "\x1b[31;;42m", // 中间有空参数
        "\x1b[31;m",    // 末尾空参数
        "\x1b[;31m",    // 开头空参数
        "\x1b[31;42;m", // 多个参数末尾空
        "\x1b[abc",     // 非数字参数
        "\x1b[31xm",    // 参数中有非法字符
        "\x1b[31.5m",   // 小数参数
        "\x1b[-31m",    // 负数参数
    };

    for (const QString &seq : malformedSequences)
    {
        qDebug() << "Testing malformed sequence:" << seq.toUtf8().toHex();
        processAndVerifyNoCrash(seq.toUtf8());
    }

    qDebug() << "Malformed CSI sequences test completed";
}

void TestInvalidVT100Sequences::testInvalidParameters()
{
    // 测试无效参数值
    qDebug() << "Testing invalid parameters";

    QStringList invalidParams = {
        "\x1b[999999999m",   // 极大数值
        "\x1b[0;999999999m", // 混合正常和极大数值
        "\x1b[999H",         // 极大行号
        "\x1b[999;999H",     // 极大行列号
        "\x1b[0;0H",         // 零行列号
        "\x1b[-1;-1H",       // 负数行列号
        "\x1b[999999999A",   // 极大移动距离
        "\x1b[999999999B",   // 极大移动距离
        "\x1b[999999999C",   // 极大移动距离
        "\x1b[999999999D",   // 极大移动距离
    };

    for (const QString &seq : invalidParams)
    {
        qDebug() << "Testing invalid parameter:" << seq.toUtf8().toHex();
        processAndVerifyNoCrash(seq.toUtf8());
    }

    qDebug() << "Invalid parameters test completed";
}

void TestInvalidVT100Sequences::testTruncatedSequences()
{
    // 测试被截断的序列
    qDebug() << "Testing truncated sequences";

    // 模拟数据传输中断导致的截断序列
    QStringList truncatedSequences = {
        "Hello\x1b",          // 文本后突然截断
        "Hello\x1b[31",       // 颜色序列截断
        "Hello\x1b[31;42",    // 复杂序列截断
        "Hello\x1b[2J\x1b[",  // 正常序列后截断
        "Hello\x1b[H\x1b[31", // 多个序列最后一个截断
    };

    for (const QString &seq : truncatedSequences)
    {
        qDebug() << "Testing truncated sequence:" << seq.toUtf8().toHex();
        processAndVerifyNoCrash(seq.toUtf8());

        // 测试后续数据能否正常处理
        processAndVerifyNoCrash("World\n");
    }

    qDebug() << "Truncated sequences test completed";
}

void TestInvalidVT100Sequences::testUnknownSequences()
{
    // 测试未知的VT100序列
    qDebug() << "Testing unknown sequences";

    QStringList unknownSequences = {
        "\x1b[31z",         // 未知的最终字符
        "\x1b[31;42x",      // 未知的最终字符
        "\x1b[?25z",        // 未知的私有序列
        "\x1b]0;Title\x07", // OSC序列（可能不支持）
        "\x1b(B",           // 字符集选择（可能不支持）
        "\x1b)0",           // 字符集选择（可能不支持）
        "\x1b#8",           // 屏幕对齐测试（可能不支持）
        "\x1b=",            // 应用键盘模式（可能不支持）
        "\x1b>",            // 数字键盘模式（可能不支持）
    };

    for (const QString &seq : unknownSequences)
    {
        qDebug() << "Testing unknown sequence:" << seq.toUtf8().toHex();
        processAndVerifyNoCrash(seq.toUtf8());
    }

    qDebug() << "Unknown sequences test completed";
}

void TestInvalidVT100Sequences::testMixedValidInvalidSequences()
{
    // 测试有效和无效序列混合
    qDebug() << "Testing mixed valid/invalid sequences";

    QStringList mixedSequences = {
        "Hello\x1b[31mRed\x1b[invalid\x1b[0mNormal",
        "\x1b[2JClear\x1b[999HInvalid\x1b[1;1HValid",
        "\x1b[31mRed\x1b[\x1b[32mGreen\x1b[0mNormal",
        "Text\x1b[31;42mColor\x1b[999999999mInvalid\x1b[0mReset",
    };

    for (const QString &seq : mixedSequences)
    {
        qDebug() << "Testing mixed sequence:" << seq.toUtf8().toHex();
        processAndVerifyNoCrash(seq.toUtf8());
    }

    qDebug() << "Mixed valid/invalid sequences test completed";
}

void TestInvalidVT100Sequences::testExtremelyLongSequences()
{
    // 测试极长的序列
    qDebug() << "Testing extremely long sequences";

    // 生成极长的参数序列
    QString longParams = "\x1b[";
    for (int i = 0; i < 1000; ++i)
    {
        if (i > 0)
            longParams += ";";
        longParams += QString::number(i % 100);
    }
    longParams += "m";

    qDebug() << "Testing extremely long parameter sequence (length:" << longParams.length() << ")";
    processAndVerifyNoCrash(longParams.toUtf8());

    // 测试极长的无效序列
    QString longInvalid = "\x1b[";
    for (int i = 0; i < 1000; ++i)
    {
        longInvalid += "x";
    }

    qDebug() << "Testing extremely long invalid sequence (length:" << longInvalid.length() << ")";
    processAndVerifyNoCrash(longInvalid.toUtf8());

    qDebug() << "Extremely long sequences test completed";
}

void TestInvalidVT100Sequences::testNullAndControlCharacters()
{
    // 测试空字符和控制字符
    qDebug() << "Testing null and control characters";

    QByteArray controlChars;

    // 添加各种控制字符
    for (int i = 0; i < 32; ++i)
    {
        if (i != 7 && i != 8 && i != 9 && i != 10 && i != 11 && i != 12 && i != 13 && i != 27)
        {
            // 排除已知的控制字符（BEL, BS, TAB, LF, VT, FF, CR, ESC）
            controlChars.append(static_cast<char>(i));
        }
    }

    qDebug() << "Testing control characters:" << controlChars.toHex();
    processAndVerifyNoCrash(controlChars);

    // 测试空字符混合在序列中
    QByteArray nullMixed = "Hello\x00World\x1b[31m\x00Color\x00\x1b[0m\x00End";
    qDebug() << "Testing null characters mixed with sequences";
    processAndVerifyNoCrash(nullMixed);

    qDebug() << "Null and control characters test completed";
}

void TestInvalidVT100Sequences::testRecoveryAfterInvalidSequence()
{
    // 测试无效序列后的恢复能力
    qDebug() << "Testing recovery after invalid sequences";

    // 处理无效序列
    processAndVerifyNoCrash("\x1b[invalid_sequence");

    // 验证后续正常序列能否正确处理
    int commandsBefore = getCommandCount();
    processAndVerifyNoCrash("\x1b[31mHello\x1b[0m");
    int commandsAfter = getCommandCount();

    // 应该有新的命令被处理
    QVERIFY(commandsAfter > commandsBefore);

    qDebug() << QString("Commands before: %1, after: %2").arg(commandsBefore).arg(commandsAfter);
    qDebug() << "Recovery after invalid sequences test completed";
}

void TestInvalidVT100Sequences::testConcurrentInvalidSequences()
{
    // 测试并发的无效序列处理
    qDebug() << "Testing concurrent invalid sequences";

    // 快速连续处理多个无效序列
    QStringList sequences = {
        "\x1b[invalid1", "\x1b[invalid2", "\x1b[999999999m", "\x1b[", "\x1b[31;", "\x1b[abc",
    };

    for (int i = 0; i < 100; ++i)
    {
        for (const QString &seq : sequences)
        {
            processAndVerifyNoCrash(seq.toUtf8());
        }

        if (i % 20 == 0)
        {
            QCoreApplication::processEvents();
        }
    }

    // 验证解析器仍然正常工作
    processAndVerifyNoCrash("Normal text\n");

    qDebug() << "Concurrent invalid sequences test completed";
}

void TestInvalidVT100Sequences::testParserStateConsistency()
{
    // 测试解析器状态一致性
    qDebug() << "Testing parser state consistency";

    // 记录初始状态
    int initialCommands = getCommandCount();

    // 处理一系列无效序列，这些序列可能会让解析器进入错误状态
    QStringList problematicSequences = {
        "\x1b[",          // 进入CSI状态但不完整
        "\x1b[31",        // 进入参数状态但不完整
        "\x1b[31;",       // 参数状态，末尾分号
        "\x1b",           // 只有ESC
        "\x1b[999999999", // 极大参数但不完整
    };

    for (const QString &seq : problematicSequences)
    {
        processAndVerifyNoCrash(seq.toUtf8());

        // 在每个无效序列后，测试正常序列是否能正确处理
        int commandsBefore = getCommandCount();
        processAndVerifyNoCrash("Test\n");
        int commandsAfter = getCommandCount();

        // 应该有新命令被添加（至少是字符命令）
        QVERIFY(commandsAfter > commandsBefore);

        qDebug() << QString("After sequence %1: commands %2 -> %3")
                        .arg(seq.toUtf8().toHex())
                        .arg(commandsBefore)
                        .arg(commandsAfter);
    }

    qDebug() << QString("Initial commands: %1, Final commands: %2").arg(initialCommands).arg(getCommandCount());

    qDebug() << "Parser state consistency test completed";
}

void TestInvalidVT100Sequences::testMemoryLeakWithInvalidSequences()
{
    // 测试无效序列是否导致内存泄漏
    qDebug() << "Testing memory leak with invalid sequences";

    // 重复处理大量无效序列
    const int iterations = 1000;

    for (int i = 0; i < iterations; ++i)
    {
        // 生成各种无效序列
        QStringList invalidSequences = {
            "\x1b[",
            "\x1b[31",
            "\x1b[31;",
            "\x1b[999999999",
            "\x1b[abc",
            "\x1b[31;42;",
            QString("\x1b[%1").arg(i % 1000), // 动态生成
        };

        for (const QString &seq : invalidSequences)
        {
            processAndVerifyNoCrash(seq.toUtf8());
        }

        // 每100次迭代处理一次事件循环
        if (i % 100 == 0)
        {
            QCoreApplication::processEvents();
            qDebug() << QString("Processed %1/%2 iterations").arg(i).arg(iterations);
        }
    }

    // 验证解析器仍然正常工作
    int commandsBefore = getCommandCount();
    processAndVerifyNoCrash("Final test\n");
    int commandsAfter = getCommandCount();

    QVERIFY(commandsAfter > commandsBefore);

    qDebug() << QString("Memory leak test completed. Final command count: %1").arg(commandsAfter);
}

void TestInvalidVT100Sequences::testParameterBufferLimits()
{
    // 测试参数缓冲区长度限制
    qDebug() << "Testing parameter buffer limits";

    // 生成超长的参数序列
    QString longParams = "\x1b[";
    for (int i = 0; i < 2000; ++i) // 超过1024字符的限制
    {
        if (i > 0)
            longParams += ";";
        longParams += "1";
    }
    // 不添加结束字符，让它保持在CSIParam状态

    qDebug() << QString("Testing extremely long parameters (length: %1)").arg(longParams.length());

    int commandsBefore = getCommandCount();
    processAndVerifyNoCrash(longParams.toUtf8());

    // 验证解析器能够恢复
    processAndVerifyNoCrash("Normal text\n");
    int commandsAfter = getCommandCount();

    // 应该有新命令被处理
    QVERIFY(commandsAfter > commandsBefore);

    qDebug() << QString("Commands before: %1, after: %2").arg(commandsBefore).arg(commandsAfter);
    qDebug() << "Parameter buffer limits test completed";
}

void TestInvalidVT100Sequences::testInvalidCharacterHandling()
{
    // 测试无效字符的处理
    qDebug() << "Testing invalid character handling";

    // 测试在CSI状态下的无效字符
    QStringList invalidCSISequences = {
        "\x1b[\x01", // 控制字符
        "\x1b[\x1F", // 控制字符
        "\x1b[#",    // 无效字符
        "\x1b[@",    // 边界字符（0x40是有效的）
        "\x1b[\x7F", // DEL字符
        "\x1b[\xFF", // 高位字符
    };

    for (const QString &seq : invalidCSISequences)
    {
        qDebug() << "Testing invalid CSI character:" << seq.toUtf8().toHex();

        int commandsBefore = getCommandCount();
        processAndVerifyNoCrash(seq.toUtf8());

        // 验证解析器能够恢复
        processAndVerifyNoCrash("Test\n");
        int commandsAfter = getCommandCount();

        // 应该有新命令被处理
        QVERIFY(commandsAfter > commandsBefore);
    }

    qDebug() << "Invalid character handling test completed";
}

QTEST_MAIN(TestInvalidVT100Sequences)
#include "test_invalid_vt100_sequences.moc"
