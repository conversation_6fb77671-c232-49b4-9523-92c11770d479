#include "Logger.h"

#include <QFileInfo>

#include <QDebug>
#include "core/CommonConstants.h"

Logger::Logger(QObject *parent) : QObject(parent)
{
    // 新实现不再需要缓冲区和定时器，直接写入文件
}

void Logger::startLogging(const QString &baseFilePath, bool splitEnabled, int splitSizeMB)
{
    if (m_isLogging)
    {
        return;
    }
    m_baseFilePath = baseFilePath;
    m_splitEnabled = splitEnabled;
    m_splitSizeMB = splitSizeMB;
    m_currentSplitIndex = 0;
    m_isAtLineStart = true; // 新文件开始时，位于行开始
    openNewLogFile();
}

void Logger::stopLogging()
{
    if (m_isLogging)
    {
        m_logFile.close();
        m_isLogging = false;
        m_isAtLineStart = true; // 重置状态
    }
}

void Logger::writeData(const QByteArray &data)
{
    if (!m_isLogging)
    {
        return;
    }

    QByteArray processedData = data;
    // Normalize line endings to '\n' to handle \r, \n, and \r\n correctly.
    processedData.replace("\r\n", "\n");
    processedData.replace("\r", "\n");

    // 逐字符处理数据，确保时间戳只在行开始处添加
    for (const char kCh : processedData)
    {
        // 如果在行开始位置且启用了时间戳，添加时间戳
        if (m_isAtLineStart && m_timestampEnabled)
        {
            const QString kTimestamp = QDateTime::currentDateTime().toString(m_timestampFormat);
            m_logFile.write(kTimestamp.toUtf8());
            m_isAtLineStart = false;
        }

        // 写入当前字符
        m_logFile.write(&kCh, 1);

        // 如果是换行符，标记下一个字符为行开始
        if (kCh == '\n')
        {
            m_isAtLineStart = true;
        }
    }

    // Check for file splitting after writing
    if (m_splitEnabled
        && m_logFile.size()
               > static_cast<qint64>(m_splitSizeMB) * App::Common::kBytesPerKilobyte * App::Common::kBytesPerKilobyte)
    {
        openNewLogFile();
    }
    m_logFile.flush(); // Flush to ensure data is written to the file
}

auto Logger::isLogging() const -> bool
{
    return m_isLogging;
}

auto Logger::getCurrentLogFilePath() const -> QString
{
    if (m_logFile.isOpen())
    {
        return m_logFile.fileName();
    }
    return {};
}

void Logger::updateSettings(bool enabled, const QString &format)
{
    m_timestampEnabled = enabled;
    m_timestampFormat = format;
}

void Logger::openNewLogFile()
{
    if (m_logFile.isOpen())
    {
        m_logFile.close();
    }

    QString finalPath = m_baseFilePath;
    if (m_currentSplitIndex > 0)
    {
        const QFileInfo kFileInfo(m_baseFilePath);
        finalPath = kFileInfo.dir().filePath(kFileInfo.baseName() + QString("_part_%1").arg(m_currentSplitIndex) + "."
                                             + kFileInfo.suffix());
    }

    m_logFile.setFileName(finalPath);
    if (m_logFile.open(QIODevice::WriteOnly | QIODevice::Append))
    {
        m_isLogging = true;
        // 检查文件是否为空，如果为空则认为在行开始
        m_isAtLineStart = (m_logFile.size() == 0);
    }
    else
    {
        m_isLogging = false;
        return;
    }
    m_currentSplitIndex++;
}

// forceFlushBuffer方法已移除，因为新实现不再使用缓冲区
